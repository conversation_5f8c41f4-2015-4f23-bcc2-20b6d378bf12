/**
 * 高级音频处理节点集合
 * 提供空间音频、音频滤波、音频效果等高级音频处理功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3 } from 'three';

/**
 * 音频滤波器类型枚举
 */
export enum FilterType {
  LOWPASS = 'lowpass',
  HIGHPASS = 'highpass',
  BANDPASS = 'bandpass',
  LOWSHELF = 'lowshelf',
  HIGHSHELF = 'highshelf',
  PEAKING = 'peaking',
  NOTCH = 'notch',
  ALLPASS = 'allpass'
}

/**
 * 音频效果类型枚举
 */
export enum EffectType {
  REVERB = 'reverb',
  DELAY = 'delay',
  CHORUS = 'chorus',
  FLANGER = 'flanger',
  PHASER = 'phaser',
  DISTORTION = 'distortion',
  COMPRESSOR = 'compressor',
  LIMITER = 'limiter'
}

/**
 * 空间音频模式枚举
 */
export enum SpatialAudioMode {
  STEREO = 'stereo',
  BINAURAL = 'binaural',
  AMBISONICS = 'ambisonics',
  SURROUND_5_1 = 'surround_5_1',
  SURROUND_7_1 = 'surround_7_1'
}

/**
 * 音频滤波器配置接口
 */
export interface AudioFilterConfig {
  type: FilterType;
  frequency: number;
  Q: number;
  gain: number;
  enabled: boolean;
}

/**
 * 音频效果配置接口
 */
export interface AudioEffectConfig {
  type: EffectType;
  parameters: { [key: string]: number };
  wetness: number;
  enabled: boolean;
}

/**
 * 空间音频配置接口
 */
export interface SpatialAudioConfig {
  mode: SpatialAudioMode;
  listenerPosition: Vector3;
  listenerOrientation: Vector3;
  rolloffFactor: number;
  maxDistance: number;
  refDistance: number;
  coneInnerAngle: number;
  coneOuterAngle: number;
  coneOuterGain: number;
}

/**
 * 高级音频管理器
 */
class AdvancedAudioManager {
  private audioContext: AudioContext | null = null;
  private spatialAudioSources: Map<string, any> = new Map();
  private audioFilters: Map<string, BiquadFilterNode> = new Map();
  private audioEffects: Map<string, any> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 初始化音频上下文
   */
  initialize(): void {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      Debug.log('AdvancedAudioManager', '音频上下文初始化完成');
    }
  }

  /**
   * 创建空间音频源
   */
  createSpatialAudioSource(sourceId: string, audioBuffer: AudioBuffer, config: SpatialAudioConfig): any {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化');
    }

    const source = this.audioContext.createBufferSource();
    const panner = this.audioContext.createPanner();
    const gainNode = this.audioContext.createGain();

    source.buffer = audioBuffer;

    // 配置空间音频参数
    panner.panningModel = 'HRTF';
    panner.distanceModel = 'inverse';
    panner.rolloffFactor = config.rolloffFactor;
    panner.maxDistance = config.maxDistance;
    panner.refDistance = config.refDistance;
    panner.coneInnerAngle = config.coneInnerAngle;
    panner.coneOuterAngle = config.coneOuterAngle;
    panner.coneOuterGain = config.coneOuterGain;

    // 连接音频节点
    source.connect(gainNode);
    gainNode.connect(panner);
    panner.connect(this.audioContext.destination);

    const spatialSource = {
      source,
      panner,
      gainNode,
      config,
      playing: false
    };

    this.spatialAudioSources.set(sourceId, spatialSource);
    this.emit('spatialAudioSourceCreated', { sourceId, spatialSource });

    Debug.log('AdvancedAudioManager', `空间音频源创建: ${sourceId}`);
    return spatialSource;
  }

  /**
   * 更新空间音频位置
   */
  updateSpatialAudioPosition(sourceId: string, position: Vector3): boolean {
    const spatialSource = this.spatialAudioSources.get(sourceId);
    if (!spatialSource) {
      return false;
    }

    spatialSource.panner.positionX.setValueAtTime(position.x, this.audioContext!.currentTime);
    spatialSource.panner.positionY.setValueAtTime(position.y, this.audioContext!.currentTime);
    spatialSource.panner.positionZ.setValueAtTime(position.z, this.audioContext!.currentTime);

    this.emit('spatialAudioPositionUpdated', { sourceId, position });
    return true;
  }

  /**
   * 创建音频滤波器
   */
  createAudioFilter(filterId: string, config: AudioFilterConfig): BiquadFilterNode {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化');
    }

    const filter = this.audioContext.createBiquadFilter();
    filter.type = config.type;
    filter.frequency.setValueAtTime(config.frequency, this.audioContext.currentTime);
    filter.Q.setValueAtTime(config.Q, this.audioContext.currentTime);
    filter.gain.setValueAtTime(config.gain, this.audioContext.currentTime);

    this.audioFilters.set(filterId, filter);
    this.emit('audioFilterCreated', { filterId, filter });

    Debug.log('AdvancedAudioManager', `音频滤波器创建: ${filterId} (${config.type})`);
    return filter;
  }

  /**
   * 创建音频效果
   */
  createAudioEffect(effectId: string, config: AudioEffectConfig): any {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化');
    }

    let effect: any;

    switch (config.type) {
      case EffectType.REVERB:
        effect = this.createReverbEffect(config.parameters);
        break;
      case EffectType.DELAY:
        effect = this.createDelayEffect(config.parameters);
        break;
      case EffectType.CHORUS:
        effect = this.createChorusEffect(config.parameters);
        break;
      case EffectType.COMPRESSOR:
        effect = this.createCompressorEffect(config.parameters);
        break;
      default:
        throw new Error(`不支持的音频效果类型: ${config.type}`);
    }

    effect.wetness = config.wetness;
    effect.enabled = config.enabled;

    this.audioEffects.set(effectId, effect);
    this.emit('audioEffectCreated', { effectId, effect });

    Debug.log('AdvancedAudioManager', `音频效果创建: ${effectId} (${config.type})`);
    return effect;
  }

  /**
   * 创建混响效果
   */
  private createReverbEffect(parameters: any): any {
    const convolver = this.audioContext!.createConvolver();
    const wetGain = this.audioContext!.createGain();
    const dryGain = this.audioContext!.createGain();
    const outputGain = this.audioContext!.createGain();

    // 创建冲激响应
    const impulseResponse = this.createImpulseResponse(
      parameters.roomSize || 2,
      parameters.decay || 2,
      parameters.reverse || false
    );
    convolver.buffer = impulseResponse;

    wetGain.gain.setValueAtTime(parameters.wetness || 0.3, this.audioContext!.currentTime);
    dryGain.gain.setValueAtTime(1 - (parameters.wetness || 0.3), this.audioContext!.currentTime);

    return {
      type: EffectType.REVERB,
      convolver,
      wetGain,
      dryGain,
      outputGain,
      connect: (source: AudioNode, destination: AudioNode) => {
        source.connect(dryGain);
        source.connect(convolver);
        convolver.connect(wetGain);
        dryGain.connect(outputGain);
        wetGain.connect(outputGain);
        outputGain.connect(destination);
      }
    };
  }

  /**
   * 创建延迟效果
   */
  private createDelayEffect(parameters: any): any {
    const delay = this.audioContext!.createDelay(parameters.maxDelay || 1);
    const feedback = this.audioContext!.createGain();
    const wetGain = this.audioContext!.createGain();
    const dryGain = this.audioContext!.createGain();
    const outputGain = this.audioContext!.createGain();

    delay.delayTime.setValueAtTime(parameters.delayTime || 0.3, this.audioContext!.currentTime);
    feedback.gain.setValueAtTime(parameters.feedback || 0.3, this.audioContext!.currentTime);
    wetGain.gain.setValueAtTime(parameters.wetness || 0.3, this.audioContext!.currentTime);
    dryGain.gain.setValueAtTime(1 - (parameters.wetness || 0.3), this.audioContext!.currentTime);

    // 连接延迟反馈回路
    delay.connect(feedback);
    feedback.connect(delay);

    return {
      type: EffectType.DELAY,
      delay,
      feedback,
      wetGain,
      dryGain,
      outputGain,
      connect: (source: AudioNode, destination: AudioNode) => {
        source.connect(dryGain);
        source.connect(delay);
        delay.connect(wetGain);
        dryGain.connect(outputGain);
        wetGain.connect(outputGain);
        outputGain.connect(destination);
      }
    };
  }

  /**
   * 创建合唱效果
   */
  private createChorusEffect(parameters: any): any {
    const delay = this.audioContext!.createDelay(0.1);
    const lfo = this.audioContext!.createOscillator();
    const lfoGain = this.audioContext!.createGain();
    const wetGain = this.audioContext!.createGain();
    const dryGain = this.audioContext!.createGain();
    const outputGain = this.audioContext!.createGain();

    lfo.frequency.setValueAtTime(parameters.rate || 1.5, this.audioContext!.currentTime);
    lfoGain.gain.setValueAtTime(parameters.depth || 0.002, this.audioContext!.currentTime);
    delay.delayTime.setValueAtTime(parameters.delay || 0.02, this.audioContext!.currentTime);
    wetGain.gain.setValueAtTime(parameters.wetness || 0.5, this.audioContext!.currentTime);
    dryGain.gain.setValueAtTime(1 - (parameters.wetness || 0.5), this.audioContext!.currentTime);

    // 连接LFO到延迟时间
    lfo.connect(lfoGain);
    lfoGain.connect(delay.delayTime);
    lfo.start();

    return {
      type: EffectType.CHORUS,
      delay,
      lfo,
      lfoGain,
      wetGain,
      dryGain,
      outputGain,
      connect: (source: AudioNode, destination: AudioNode) => {
        source.connect(dryGain);
        source.connect(delay);
        delay.connect(wetGain);
        dryGain.connect(outputGain);
        wetGain.connect(outputGain);
        outputGain.connect(destination);
      }
    };
  }

  /**
   * 创建压缩器效果
   */
  private createCompressorEffect(parameters: any): any {
    const compressor = this.audioContext!.createDynamicsCompressor();

    compressor.threshold.setValueAtTime(parameters.threshold || -24, this.audioContext!.currentTime);
    compressor.knee.setValueAtTime(parameters.knee || 30, this.audioContext!.currentTime);
    compressor.ratio.setValueAtTime(parameters.ratio || 12, this.audioContext!.currentTime);
    compressor.attack.setValueAtTime(parameters.attack || 0.003, this.audioContext!.currentTime);
    compressor.release.setValueAtTime(parameters.release || 0.25, this.audioContext!.currentTime);

    return {
      type: EffectType.COMPRESSOR,
      compressor,
      connect: (source: AudioNode, destination: AudioNode) => {
        source.connect(compressor);
        compressor.connect(destination);
      }
    };
  }

  /**
   * 创建冲激响应
   */
  private createImpulseResponse(duration: number, decay: number, reverse: boolean): AudioBuffer {
    const sampleRate = this.audioContext!.sampleRate;
    const length = sampleRate * duration;
    const impulse = this.audioContext!.createBuffer(2, length, sampleRate);

    for (let channel = 0; channel < 2; channel++) {
      const channelData = impulse.getChannelData(channel);
      for (let i = 0; i < length; i++) {
        const n = reverse ? length - i : i;
        channelData[i] = (Math.random() * 2 - 1) * Math.pow(1 - n / length, decay);
      }
    }

    return impulse;
  }

  /**
   * 获取空间音频源
   */
  getSpatialAudioSource(sourceId: string): any {
    return this.spatialAudioSources.get(sourceId);
  }

  /**
   * 获取音频滤波器
   */
  getAudioFilter(filterId: string): BiquadFilterNode | undefined {
    return this.audioFilters.get(filterId);
  }

  /**
   * 获取音频效果
   */
  getAudioEffect(effectId: string): any {
    return this.audioEffects.get(effectId);
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('AdvancedAudioManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 停止所有音频源
    for (const spatialSource of this.spatialAudioSources.values()) {
      if (spatialSource.playing) {
        spatialSource.source.stop();
      }
    }

    this.spatialAudioSources.clear();
    this.audioFilters.clear();
    this.audioEffects.clear();
    this.eventListeners.clear();

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
  }
}

/**
 * 空间音频节点
 */
export class SpatialAudioNode extends VisualScriptNode {
  public static readonly TYPE = 'SpatialAudio';
  public static readonly NAME = '空间音频';
  public static readonly DESCRIPTION = '3D空间音频处理';

  private static audioManager: AdvancedAudioManager = new AdvancedAudioManager();

  constructor(nodeType: string = SpatialAudioNode.TYPE, name: string = SpatialAudioNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建空间音频');
    this.addInput('updatePosition', 'trigger', '更新位置');
    this.addInput('play', 'trigger', '播放');
    this.addInput('stop', 'trigger', '停止');
    this.addInput('sourceId', 'string', '音频源ID');
    this.addInput('audioBuffer', 'object', '音频缓冲区');
    this.addInput('position', 'object', '音频位置');
    this.addInput('listenerPosition', 'object', '听者位置');
    this.addInput('rolloffFactor', 'number', '衰减因子');
    this.addInput('maxDistance', 'number', '最大距离');
    this.addInput('refDistance', 'number', '参考距离');

    // 输出端口
    this.addOutput('spatialSource', 'object', '空间音频源');
    this.addOutput('sourceId', 'string', '音频源ID');
    this.addOutput('playing', 'boolean', '是否播放中');
    this.addOutput('distance', 'number', '距离');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onPositionUpdated', 'trigger', '位置更新完成');
    this.addOutput('onPlaying', 'trigger', '开始播放');
    this.addOutput('onStopped', 'trigger', '停止播放');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updatePositionTrigger = inputs?.updatePosition;
      const playTrigger = inputs?.play;
      const stopTrigger = inputs?.stop;

      if (createTrigger) {
        return this.createSpatialAudio(inputs);
      } else if (updatePositionTrigger) {
        return this.updatePosition(inputs);
      } else if (playTrigger) {
        return this.playAudio(inputs);
      } else if (stopTrigger) {
        return this.stopAudio(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('SpatialAudioNode', '空间音频操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createSpatialAudio(inputs: any): any {
    SpatialAudioNode.audioManager.initialize();

    const sourceId = inputs?.sourceId as string || this.generateSourceId();
    const audioBuffer = inputs?.audioBuffer as AudioBuffer;
    const position = inputs?.position as Vector3 || new Vector3(0, 0, 0);
    const listenerPosition = inputs?.listenerPosition as Vector3 || new Vector3(0, 0, 0);
    const rolloffFactor = inputs?.rolloffFactor as number || 1;
    const maxDistance = inputs?.maxDistance as number || 10000;
    const refDistance = inputs?.refDistance as number || 1;

    if (!audioBuffer) {
      throw new Error('未提供音频缓冲区');
    }

    const config: SpatialAudioConfig = {
      mode: SpatialAudioMode.BINAURAL,
      listenerPosition,
      listenerOrientation: new Vector3(0, 0, -1),
      rolloffFactor,
      maxDistance,
      refDistance,
      coneInnerAngle: 360,
      coneOuterAngle: 0,
      coneOuterGain: 0
    };

    const spatialSource = SpatialAudioNode.audioManager.createSpatialAudioSource(sourceId, audioBuffer, config);
    SpatialAudioNode.audioManager.updateSpatialAudioPosition(sourceId, position);

    const distance = position.distanceTo(listenerPosition);

    Debug.log('SpatialAudioNode', `空间音频创建: ${sourceId}`);

    return {
      spatialSource,
      sourceId,
      playing: false,
      distance,
      onCreated: true,
      onPositionUpdated: false,
      onPlaying: false,
      onStopped: false,
      onError: false
    };
  }

  private updatePosition(inputs: any): any {
    const sourceId = inputs?.sourceId as string;
    const position = inputs?.position as Vector3;
    const listenerPosition = inputs?.listenerPosition as Vector3 || new Vector3(0, 0, 0);

    if (!sourceId || !position) {
      throw new Error('未提供音频源ID或位置');
    }

    const success = SpatialAudioNode.audioManager.updateSpatialAudioPosition(sourceId, position);
    if (!success) {
      throw new Error('位置更新失败');
    }

    const spatialSource = SpatialAudioNode.audioManager.getSpatialAudioSource(sourceId);
    const distance = position.distanceTo(listenerPosition);

    Debug.log('SpatialAudioNode', `空间音频位置更新: ${sourceId}`);

    return {
      spatialSource,
      sourceId,
      playing: spatialSource?.playing || false,
      distance,
      onCreated: false,
      onPositionUpdated: true,
      onPlaying: false,
      onStopped: false,
      onError: false
    };
  }

  private playAudio(inputs: any): any {
    const sourceId = inputs?.sourceId as string;

    if (!sourceId) {
      throw new Error('未提供音频源ID');
    }

    const spatialSource = SpatialAudioNode.audioManager.getSpatialAudioSource(sourceId);
    if (!spatialSource) {
      throw new Error('空间音频源不存在');
    }

    spatialSource.source.start();
    spatialSource.playing = true;

    Debug.log('SpatialAudioNode', `空间音频播放: ${sourceId}`);

    return {
      spatialSource,
      sourceId,
      playing: true,
      distance: 0,
      onCreated: false,
      onPositionUpdated: false,
      onPlaying: true,
      onStopped: false,
      onError: false
    };
  }

  private stopAudio(inputs: any): any {
    const sourceId = inputs?.sourceId as string;

    if (!sourceId) {
      throw new Error('未提供音频源ID');
    }

    const spatialSource = SpatialAudioNode.audioManager.getSpatialAudioSource(sourceId);
    if (!spatialSource) {
      throw new Error('空间音频源不存在');
    }

    if (spatialSource.playing) {
      spatialSource.source.stop();
      spatialSource.playing = false;
    }

    Debug.log('SpatialAudioNode', `空间音频停止: ${sourceId}`);

    return {
      spatialSource,
      sourceId,
      playing: false,
      distance: 0,
      onCreated: false,
      onPositionUpdated: false,
      onPlaying: false,
      onStopped: true,
      onError: false
    };
  }

  private generateSourceId(): string {
    return 'spatial_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      spatialSource: null,
      sourceId: '',
      playing: false,
      distance: 0,
      onCreated: false,
      onPositionUpdated: false,
      onPlaying: false,
      onStopped: false,
      onError: false
    };
  }
}

/**
 * 音频滤波器节点
 */
export class AudioFilterNode extends VisualScriptNode {
  public static readonly TYPE = 'AudioFilter';
  public static readonly NAME = '音频滤波器';
  public static readonly DESCRIPTION = '音频频率滤波处理';

  private static audioManager: AdvancedAudioManager = new AdvancedAudioManager();

  constructor(nodeType: string = AudioFilterNode.TYPE, name: string = AudioFilterNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建滤波器');
    this.addInput('update', 'trigger', '更新参数');
    this.addInput('filterId', 'string', '滤波器ID');
    this.addInput('filterType', 'string', '滤波器类型');
    this.addInput('frequency', 'number', '截止频率');
    this.addInput('Q', 'number', 'Q值');
    this.addInput('gain', 'number', '增益');
    this.addInput('enabled', 'boolean', '启用状态');

    // 输出端口
    this.addOutput('filter', 'object', '滤波器对象');
    this.addOutput('filterId', 'string', '滤波器ID');
    this.addOutput('filterType', 'string', '滤波器类型');
    this.addOutput('frequency', 'number', '截止频率');
    this.addOutput('Q', 'number', 'Q值');
    this.addOutput('gain', 'number', '增益');
    this.addOutput('onCreated', 'trigger', '滤波器创建完成');
    this.addOutput('onUpdated', 'trigger', '参数更新完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;

      if (createTrigger) {
        return this.createFilter(inputs);
      } else if (updateTrigger) {
        return this.updateFilter(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('AudioFilterNode', '音频滤波器操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createFilter(inputs: any): any {
    AudioFilterNode.audioManager.initialize();

    const filterId = inputs?.filterId as string || this.generateFilterId();
    const filterType = inputs?.filterType as string || 'lowpass';
    const frequency = inputs?.frequency as number || 1000;
    const Q = inputs?.Q as number || 1;
    const gain = inputs?.gain as number || 0;
    const enabled = inputs?.enabled as boolean ?? true;

    const config: AudioFilterConfig = {
      type: filterType as FilterType,
      frequency,
      Q,
      gain,
      enabled
    };

    const filter = AudioFilterNode.audioManager.createAudioFilter(filterId, config);

    Debug.log('AudioFilterNode', `音频滤波器创建: ${filterId} (${filterType})`);

    return {
      filter,
      filterId,
      filterType,
      frequency,
      Q,
      gain,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateFilter(inputs: any): any {
    const filterId = inputs?.filterId as string;

    if (!filterId) {
      throw new Error('未提供滤波器ID');
    }

    const filter = AudioFilterNode.audioManager.getAudioFilter(filterId);
    if (!filter) {
      throw new Error('滤波器不存在');
    }

    const frequency = inputs?.frequency as number;
    const Q = inputs?.Q as number;
    const gain = inputs?.gain as number;

    if (frequency !== undefined) {
      filter.frequency.setValueAtTime(frequency, filter.context.currentTime);
    }
    if (Q !== undefined) {
      filter.Q.setValueAtTime(Q, filter.context.currentTime);
    }
    if (gain !== undefined) {
      filter.gain.setValueAtTime(gain, filter.context.currentTime);
    }

    Debug.log('AudioFilterNode', `音频滤波器参数更新: ${filterId}`);

    return {
      filter,
      filterId,
      filterType: filter.type,
      frequency: filter.frequency.value,
      Q: filter.Q.value,
      gain: filter.gain.value,
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private generateFilterId(): string {
    return 'filter_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      filter: null,
      filterId: '',
      filterType: '',
      frequency: 0,
      Q: 0,
      gain: 0,
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}

/**
 * 音频效果节点
 */
export class AudioEffectNode extends VisualScriptNode {
  public static readonly TYPE = 'AudioEffect';
  public static readonly NAME = '音频效果';
  public static readonly DESCRIPTION = '音频效果处理';

  private static audioManager: AdvancedAudioManager = new AdvancedAudioManager();

  constructor(nodeType: string = AudioEffectNode.TYPE, name: string = AudioEffectNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建效果');
    this.addInput('update', 'trigger', '更新参数');
    this.addInput('effectId', 'string', '效果ID');
    this.addInput('effectType', 'string', '效果类型');
    this.addInput('parameters', 'object', '效果参数');
    this.addInput('wetness', 'number', '湿度');
    this.addInput('enabled', 'boolean', '启用状态');

    // 输出端口
    this.addOutput('effect', 'object', '效果对象');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('effectType', 'string', '效果类型');
    this.addOutput('wetness', 'number', '湿度');
    this.addOutput('enabled', 'boolean', '启用状态');
    this.addOutput('onCreated', 'trigger', '效果创建完成');
    this.addOutput('onUpdated', 'trigger', '参数更新完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;

      if (createTrigger) {
        return this.createEffect(inputs);
      } else if (updateTrigger) {
        return this.updateEffect(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('AudioEffectNode', '音频效果操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createEffect(inputs: any): any {
    AudioEffectNode.audioManager.initialize();

    const effectId = inputs?.effectId as string || this.generateEffectId();
    const effectType = inputs?.effectType as string || 'reverb';
    const parameters = inputs?.parameters || this.getDefaultParameters(effectType);
    const wetness = inputs?.wetness as number || 0.3;
    const enabled = inputs?.enabled as boolean ?? true;

    const config: AudioEffectConfig = {
      type: effectType as EffectType,
      parameters,
      wetness,
      enabled
    };

    const effect = AudioEffectNode.audioManager.createAudioEffect(effectId, config);

    Debug.log('AudioEffectNode', `音频效果创建: ${effectId} (${effectType})`);

    return {
      effect,
      effectId,
      effectType,
      wetness,
      enabled,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateEffect(inputs: any): any {
    const effectId = inputs?.effectId as string;

    if (!effectId) {
      throw new Error('未提供效果ID');
    }

    const effect = AudioEffectNode.audioManager.getAudioEffect(effectId);
    if (!effect) {
      throw new Error('音频效果不存在');
    }

    const wetness = inputs?.wetness as number;
    const enabled = inputs?.enabled as boolean;

    if (wetness !== undefined) {
      effect.wetness = wetness;
    }
    if (enabled !== undefined) {
      effect.enabled = enabled;
    }

    Debug.log('AudioEffectNode', `音频效果参数更新: ${effectId}`);

    return {
      effect,
      effectId,
      effectType: effect.type,
      wetness: effect.wetness,
      enabled: effect.enabled,
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private getDefaultParameters(effectType: string): any {
    switch (effectType) {
      case 'reverb':
        return { roomSize: 2, decay: 2, wetness: 0.3 };
      case 'delay':
        return { delayTime: 0.3, feedback: 0.3, wetness: 0.3 };
      case 'chorus':
        return { rate: 1.5, depth: 0.002, delay: 0.02, wetness: 0.5 };
      case 'compressor':
        return { threshold: -24, knee: 30, ratio: 12, attack: 0.003, release: 0.25 };
      default:
        return {};
    }
  }

  private generateEffectId(): string {
    return 'effect_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      effect: null,
      effectId: '',
      effectType: '',
      wetness: 0,
      enabled: false,
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}
