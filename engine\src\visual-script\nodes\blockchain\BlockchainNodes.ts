/**
 * 区块链系统节点集合
 * 提供区块链交互、智能合约、NFT、DeFi等功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 区块链网络枚举
 */
export enum BlockchainNetwork {
  ETHEREUM = 'ethereum',
  POLYGON = 'polygon',
  BSC = 'bsc',
  AVALANCHE = 'avalanche',
  SOLANA = 'solana',
  CARDANO = 'cardano',
  POLKADOT = 'polkadot',
  NEAR = 'near',
  FLOW = 'flow',
  TEZOS = 'tezos'
}

/**
 * 交易状态枚举
 */
export enum TransactionStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

/**
 * 代币标准枚举
 */
export enum TokenStandard {
  ERC20 = 'erc20',
  ERC721 = 'erc721',
  ERC1155 = 'erc1155',
  SPL = 'spl',
  NEP141 = 'nep141',
  FA2 = 'fa2'
}

/**
 * 钱包接口
 */
export interface Wallet {
  address: string;
  network: BlockchainNetwork;
  balance: number;
  tokens: Token[];
  nfts: NFT[];
  connected: boolean;
  provider: string;
}

/**
 * 代币接口
 */
export interface Token {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  balance: number;
  standard: TokenStandard;
  logoUrl?: string;
}

/**
 * NFT接口
 */
export interface NFT {
  tokenId: string;
  contractAddress: string;
  name: string;
  description: string;
  imageUrl: string;
  attributes: NFTAttribute[];
  owner: string;
  creator: string;
  standard: TokenStandard;
}

/**
 * NFT属性接口
 */
export interface NFTAttribute {
  trait_type: string;
  value: string | number;
  display_type?: string;
}

/**
 * 交易接口
 */
export interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: number;
  gasPrice: number;
  gasLimit: number;
  gasUsed?: number;
  status: TransactionStatus;
  blockNumber?: number;
  timestamp?: number;
  data?: string;
}

/**
 * 智能合约接口
 */
export interface SmartContract {
  address: string;
  abi: any[];
  network: BlockchainNetwork;
  name: string;
  version: string;
}

/**
 * 高级区块链管理器
 */
class AdvancedBlockchainManager {
  private wallets: Map<string, Wallet> = new Map();
  private contracts: Map<string, SmartContract> = new Map();
  private transactions: Map<string, Transaction> = new Map();
  private providers: Map<BlockchainNetwork, any> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 连接钱包
   */
  async connectWallet(network: BlockchainNetwork, provider: string): Promise<Wallet> {
    try {
      // 模拟钱包连接
      const wallet: Wallet = {
        address: this.generateAddress(network),
        network,
        balance: Math.random() * 10,
        tokens: [],
        nfts: [],
        connected: true,
        provider
      };

      // 获取代币余额
      wallet.tokens = await this.getTokenBalances(wallet.address, network);
      
      // 获取NFT
      wallet.nfts = await this.getNFTs(wallet.address, network);

      this.wallets.set(wallet.address, wallet);
      this.emit('walletConnected', { wallet });

      Debug.log('AdvancedBlockchainManager', `钱包连接成功: ${wallet.address} (${network})`);
      return wallet;

    } catch (error) {
      Debug.error('AdvancedBlockchainManager', '钱包连接失败', error);
      throw error;
    }
  }

  /**
   * 断开钱包连接
   */
  disconnectWallet(address: string): void {
    const wallet = this.wallets.get(address);
    if (wallet) {
      wallet.connected = false;
      this.emit('walletDisconnected', { wallet });
      Debug.log('AdvancedBlockchainManager', `钱包断开连接: ${address}`);
    }
  }

  /**
   * 发送交易
   */
  async sendTransaction(from: string, to: string, value: number, data?: string): Promise<Transaction> {
    try {
      const wallet = this.wallets.get(from);
      if (!wallet || !wallet.connected) {
        throw new Error('钱包未连接');
      }

      if (wallet.balance < value) {
        throw new Error('余额不足');
      }

      const transaction: Transaction = {
        hash: this.generateTransactionHash(),
        from,
        to,
        value,
        gasPrice: this.getGasPrice(wallet.network),
        gasLimit: 21000,
        status: TransactionStatus.PENDING,
        data
      };

      this.transactions.set(transaction.hash, transaction);
      
      // 模拟交易确认
      setTimeout(() => {
        this.confirmTransaction(transaction.hash);
      }, 3000);

      this.emit('transactionSent', { transaction });
      Debug.log('AdvancedBlockchainManager', `交易发送: ${transaction.hash}`);

      return transaction;

    } catch (error) {
      Debug.error('AdvancedBlockchainManager', '发送交易失败', error);
      throw error;
    }
  }

  /**
   * 部署智能合约
   */
  async deployContract(bytecode: string, abi: any[], network: BlockchainNetwork, constructorArgs?: any[]): Promise<SmartContract> {
    try {
      const contract: SmartContract = {
        address: this.generateContractAddress(network),
        abi,
        network,
        name: 'CustomContract',
        version: '1.0.0'
      };

      this.contracts.set(contract.address, contract);
      this.emit('contractDeployed', { contract });

      Debug.log('AdvancedBlockchainManager', `智能合约部署成功: ${contract.address}`);
      return contract;

    } catch (error) {
      Debug.error('AdvancedBlockchainManager', '智能合约部署失败', error);
      throw error;
    }
  }

  /**
   * 调用智能合约方法
   */
  async callContractMethod(contractAddress: string, methodName: string, args: any[], value?: number): Promise<any> {
    try {
      const contract = this.contracts.get(contractAddress);
      if (!contract) {
        throw new Error('合约不存在');
      }

      // 模拟合约调用
      const result = this.simulateContractCall(methodName, args);
      
      this.emit('contractMethodCalled', { 
        contractAddress, 
        methodName, 
        args, 
        result 
      });

      Debug.log('AdvancedBlockchainManager', `合约方法调用: ${contractAddress}.${methodName}`);
      return result;

    } catch (error) {
      Debug.error('AdvancedBlockchainManager', '合约方法调用失败', error);
      throw error;
    }
  }

  /**
   * 铸造NFT
   */
  async mintNFT(contractAddress: string, to: string, tokenId: string, metadata: any): Promise<NFT> {
    try {
      const nft: NFT = {
        tokenId,
        contractAddress,
        name: metadata.name || 'Untitled NFT',
        description: metadata.description || '',
        imageUrl: metadata.image || '',
        attributes: metadata.attributes || [],
        owner: to,
        creator: to,
        standard: TokenStandard.ERC721
      };

      // 添加到钱包
      const wallet = this.wallets.get(to);
      if (wallet) {
        wallet.nfts.push(nft);
      }

      this.emit('nftMinted', { nft });
      Debug.log('AdvancedBlockchainManager', `NFT铸造成功: ${tokenId}`);

      return nft;

    } catch (error) {
      Debug.error('AdvancedBlockchainManager', 'NFT铸造失败', error);
      throw error;
    }
  }

  /**
   * 转移NFT
   */
  async transferNFT(contractAddress: string, from: string, to: string, tokenId: string): Promise<Transaction> {
    try {
      const fromWallet = this.wallets.get(from);
      const toWallet = this.wallets.get(to);

      if (!fromWallet) {
        throw new Error('发送方钱包不存在');
      }

      // 查找NFT
      const nftIndex = fromWallet.nfts.findIndex(
        nft => nft.contractAddress === contractAddress && nft.tokenId === tokenId
      );

      if (nftIndex === -1) {
        throw new Error('NFT不存在或不属于发送方');
      }

      const nft = fromWallet.nfts[nftIndex];
      
      // 转移NFT
      fromWallet.nfts.splice(nftIndex, 1);
      if (toWallet) {
        nft.owner = to;
        toWallet.nfts.push(nft);
      }

      // 创建交易记录
      const transaction = await this.sendTransaction(from, to, 0, `transferNFT:${tokenId}`);
      
      this.emit('nftTransferred', { nft, from, to, transaction });
      Debug.log('AdvancedBlockchainManager', `NFT转移成功: ${tokenId} ${from} -> ${to}`);

      return transaction;

    } catch (error) {
      Debug.error('AdvancedBlockchainManager', 'NFT转移失败', error);
      throw error;
    }
  }

  /**
   * 获取代币余额
   */
  private async getTokenBalances(address: string, network: BlockchainNetwork): Promise<Token[]> {
    // 模拟获取代币余额
    const commonTokens = this.getCommonTokens(network);
    return commonTokens.map(token => ({
      ...token,
      balance: Math.random() * 1000
    }));
  }

  /**
   * 获取NFT列表
   */
  private async getNFTs(address: string, network: BlockchainNetwork): Promise<NFT[]> {
    // 模拟获取NFT
    const nfts: NFT[] = [];
    const nftCount = Math.floor(Math.random() * 5);
    
    for (let i = 0; i < nftCount; i++) {
      nfts.push({
        tokenId: (i + 1).toString(),
        contractAddress: this.generateContractAddress(network),
        name: `NFT #${i + 1}`,
        description: `A unique NFT #${i + 1}`,
        imageUrl: `https://example.com/nft/${i + 1}.png`,
        attributes: [
          { trait_type: 'Rarity', value: 'Common' },
          { trait_type: 'Level', value: Math.floor(Math.random() * 100) }
        ],
        owner: address,
        creator: address,
        standard: TokenStandard.ERC721
      });
    }
    
    return nfts;
  }

  /**
   * 获取常见代币
   */
  private getCommonTokens(network: BlockchainNetwork): Token[] {
    const tokens: { [key in BlockchainNetwork]: Token[] } = {
      [BlockchainNetwork.ETHEREUM]: [
        { address: '******************************************', symbol: 'USDC', name: 'USD Coin', decimals: 6, balance: 0, standard: TokenStandard.ERC20 },
        { address: '******************************************', symbol: 'USDT', name: 'Tether USD', decimals: 6, balance: 0, standard: TokenStandard.ERC20 }
      ],
      [BlockchainNetwork.POLYGON]: [
        { address: '******************************************', symbol: 'USDC', name: 'USD Coin', decimals: 6, balance: 0, standard: TokenStandard.ERC20 },
        { address: '******************************************', symbol: 'USDT', name: 'Tether USD', decimals: 6, balance: 0, standard: TokenStandard.ERC20 }
      ],
      [BlockchainNetwork.BSC]: [
        { address: '******************************************', symbol: 'USDC', name: 'USD Coin', decimals: 18, balance: 0, standard: TokenStandard.ERC20 },
        { address: '******************************************', symbol: 'USDT', name: 'Tether USD', decimals: 18, balance: 0, standard: TokenStandard.ERC20 }
      ],
      [BlockchainNetwork.AVALANCHE]: [],
      [BlockchainNetwork.SOLANA]: [],
      [BlockchainNetwork.CARDANO]: [],
      [BlockchainNetwork.POLKADOT]: [],
      [BlockchainNetwork.NEAR]: [],
      [BlockchainNetwork.FLOW]: [],
      [BlockchainNetwork.TEZOS]: []
    };
    
    return tokens[network] || [];
  }

  /**
   * 确认交易
   */
  private confirmTransaction(hash: string): void {
    const transaction = this.transactions.get(hash);
    if (transaction) {
      transaction.status = TransactionStatus.CONFIRMED;
      transaction.blockNumber = Math.floor(Math.random() * 1000000);
      transaction.timestamp = Date.now();
      transaction.gasUsed = Math.floor(transaction.gasLimit * 0.8);
      
      this.emit('transactionConfirmed', { transaction });
      Debug.log('AdvancedBlockchainManager', `交易确认: ${hash}`);
    }
  }

  /**
   * 模拟合约调用
   */
  private simulateContractCall(methodName: string, args: any[]): any {
    // 简化的合约调用模拟
    switch (methodName) {
      case 'balanceOf':
        return Math.floor(Math.random() * 1000);
      case 'totalSupply':
        return 1000000;
      case 'name':
        return 'Test Token';
      case 'symbol':
        return 'TEST';
      default:
        return true;
    }
  }

  /**
   * 获取Gas价格
   */
  private getGasPrice(network: BlockchainNetwork): number {
    const gasPrices: { [key in BlockchainNetwork]: number } = {
      [BlockchainNetwork.ETHEREUM]: 20,
      [BlockchainNetwork.POLYGON]: 30,
      [BlockchainNetwork.BSC]: 5,
      [BlockchainNetwork.AVALANCHE]: 25,
      [BlockchainNetwork.SOLANA]: 0.000005,
      [BlockchainNetwork.CARDANO]: 0.17,
      [BlockchainNetwork.POLKADOT]: 0.01,
      [BlockchainNetwork.NEAR]: 0.0001,
      [BlockchainNetwork.FLOW]: 0.001,
      [BlockchainNetwork.TEZOS]: 0.001
    };
    
    return gasPrices[network];
  }

  /**
   * 生成地址
   */
  private generateAddress(network: BlockchainNetwork): string {
    const prefixes: { [key in BlockchainNetwork]: string } = {
      [BlockchainNetwork.ETHEREUM]: '0x',
      [BlockchainNetwork.POLYGON]: '0x',
      [BlockchainNetwork.BSC]: '0x',
      [BlockchainNetwork.AVALANCHE]: '0x',
      [BlockchainNetwork.SOLANA]: '',
      [BlockchainNetwork.CARDANO]: 'addr1',
      [BlockchainNetwork.POLKADOT]: '1',
      [BlockchainNetwork.NEAR]: '',
      [BlockchainNetwork.FLOW]: '0x',
      [BlockchainNetwork.TEZOS]: 'tz1'
    };
    
    const prefix = prefixes[network];
    const randomHex = Math.random().toString(16).substr(2, 40);
    return prefix + randomHex;
  }

  /**
   * 生成合约地址
   */
  private generateContractAddress(network: BlockchainNetwork): string {
    return this.generateAddress(network);
  }

  /**
   * 生成交易哈希
   */
  private generateTransactionHash(): string {
    return '0x' + Math.random().toString(16).substr(2, 64);
  }

  /**
   * 事件监听
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * 移除事件监听
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('AdvancedBlockchainManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 获取钱包
   */
  getWallet(address: string): Wallet | undefined {
    return this.wallets.get(address);
  }

  /**
   * 获取交易
   */
  getTransaction(hash: string): Transaction | undefined {
    return this.transactions.get(hash);
  }

  /**
   * 获取合约
   */
  getContract(address: string): SmartContract | undefined {
    return this.contracts.get(address);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.wallets.clear();
    this.contracts.clear();
    this.transactions.clear();
    this.providers.clear();
    this.eventListeners.clear();
  }
}

/**
 * 钱包连接节点
 */
export class WalletConnectNode extends VisualScriptNode {
  public static readonly TYPE = 'WalletConnect';
  public static readonly NAME = '钱包连接';
  public static readonly DESCRIPTION = '连接和管理区块链钱包';

  private static blockchainManager: AdvancedBlockchainManager = new AdvancedBlockchainManager();

  constructor(nodeType: string = WalletConnectNode.TYPE, name: string = WalletConnectNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('connect', 'trigger', '连接');
    this.addInput('disconnect', 'trigger', '断开连接');
    this.addInput('network', 'string', '区块链网络');
    this.addInput('provider', 'string', '钱包提供商');
    this.addInput('walletAddress', 'string', '钱包地址');

    // 输出端口
    this.addOutput('wallet', 'object', '钱包信息');
    this.addOutput('address', 'string', '钱包地址');
    this.addOutput('balance', 'number', '余额');
    this.addOutput('network', 'string', '网络');
    this.addOutput('connected', 'boolean', '连接状态');
    this.addOutput('tokenCount', 'number', '代币数量');
    this.addOutput('nftCount', 'number', 'NFT数量');
    this.addOutput('onConnected', 'trigger', '连接成功');
    this.addOutput('onDisconnected', 'trigger', '断开连接');
    this.addOutput('onError', 'trigger', '连接失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const connectTrigger = inputs?.connect;
      const disconnectTrigger = inputs?.disconnect;
      const network = inputs?.network as string || 'ethereum';
      const provider = inputs?.provider as string || 'metamask';
      const walletAddress = inputs?.walletAddress as string;

      if (connectTrigger) {
        return await this.connectWallet(network as BlockchainNetwork, provider);
      } else if (disconnectTrigger && walletAddress) {
        return this.disconnectWallet(walletAddress);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('WalletConnectNode', '钱包操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private async connectWallet(network: BlockchainNetwork, provider: string): Promise<any> {
    try {
      const wallet = await WalletConnectNode.blockchainManager.connectWallet(network, provider);

      Debug.log('WalletConnectNode', `钱包连接成功: ${wallet.address}`);

      return {
        wallet,
        address: wallet.address,
        balance: wallet.balance,
        network: wallet.network,
        connected: wallet.connected,
        tokenCount: wallet.tokens.length,
        nftCount: wallet.nfts.length,
        onConnected: true,
        onDisconnected: false,
        onError: false
      };

    } catch (error) {
      Debug.error('WalletConnectNode', '钱包连接失败', error);
      throw error;
    }
  }

  private disconnectWallet(address: string): any {
    WalletConnectNode.blockchainManager.disconnectWallet(address);

    return {
      wallet: null,
      address: '',
      balance: 0,
      network: '',
      connected: false,
      tokenCount: 0,
      nftCount: 0,
      onConnected: false,
      onDisconnected: true,
      onError: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      wallet: null,
      address: '',
      balance: 0,
      network: '',
      connected: false,
      tokenCount: 0,
      nftCount: 0,
      onConnected: false,
      onDisconnected: false,
      onError: false
    };
  }
}

/**
 * 智能合约交互节点
 */
export class SmartContractNode extends VisualScriptNode {
  public static readonly TYPE = 'SmartContract';
  public static readonly NAME = '智能合约';
  public static readonly DESCRIPTION = '部署和调用智能合约';

  private static blockchainManager: AdvancedBlockchainManager = new AdvancedBlockchainManager();

  constructor(nodeType: string = SmartContractNode.TYPE, name: string = SmartContractNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('deploy', 'trigger', '部署合约');
    this.addInput('call', 'trigger', '调用方法');
    this.addInput('contractAddress', 'string', '合约地址');
    this.addInput('bytecode', 'string', '字节码');
    this.addInput('abi', 'array', 'ABI');
    this.addInput('network', 'string', '网络');
    this.addInput('methodName', 'string', '方法名');
    this.addInput('methodArgs', 'array', '方法参数');
    this.addInput('value', 'number', '发送金额');
    this.addInput('constructorArgs', 'array', '构造函数参数');

    // 输出端口
    this.addOutput('contract', 'object', '合约信息');
    this.addOutput('contractAddress', 'string', '合约地址');
    this.addOutput('deploymentTx', 'string', '部署交易');
    this.addOutput('callResult', 'any', '调用结果');
    this.addOutput('gasUsed', 'number', '消耗Gas');
    this.addOutput('onDeployed', 'trigger', '部署完成');
    this.addOutput('onCalled', 'trigger', '调用完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const deployTrigger = inputs?.deploy;
      const callTrigger = inputs?.call;

      if (deployTrigger) {
        return await this.deployContract(inputs);
      } else if (callTrigger) {
        return await this.callContract(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('SmartContractNode', '智能合约操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private async deployContract(inputs: any): Promise<any> {
    const bytecode = inputs?.bytecode as string;
    const abi = inputs?.abi as any[] || [];
    const network = inputs?.network as string || 'ethereum';
    const constructorArgs = inputs?.constructorArgs as any[] || [];

    if (!bytecode) {
      throw new Error('未提供合约字节码');
    }

    const contract = await SmartContractNode.blockchainManager.deployContract(
      bytecode,
      abi,
      network as BlockchainNetwork,
      constructorArgs
    );

    return {
      contract,
      contractAddress: contract.address,
      deploymentTx: 'deployment_tx_hash',
      callResult: null,
      gasUsed: 500000,
      onDeployed: true,
      onCalled: false,
      onError: false
    };
  }

  private async callContract(inputs: any): Promise<any> {
    const contractAddress = inputs?.contractAddress as string;
    const methodName = inputs?.methodName as string;
    const methodArgs = inputs?.methodArgs as any[] || [];
    const value = inputs?.value as number || 0;

    if (!contractAddress || !methodName) {
      throw new Error('未提供合约地址或方法名');
    }

    const result = await SmartContractNode.blockchainManager.callContractMethod(
      contractAddress,
      methodName,
      methodArgs,
      value
    );

    return {
      contract: null,
      contractAddress,
      deploymentTx: null,
      callResult: result,
      gasUsed: 50000,
      onDeployed: false,
      onCalled: true,
      onError: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      contract: null,
      contractAddress: '',
      deploymentTx: null,
      callResult: null,
      gasUsed: 0,
      onDeployed: false,
      onCalled: false,
      onError: false
    };
  }
}

/**
 * NFT操作节点
 */
export class NFTOperationNode extends VisualScriptNode {
  public static readonly TYPE = 'NFTOperation';
  public static readonly NAME = 'NFT操作';
  public static readonly DESCRIPTION = '铸造、转移和管理NFT';

  private static blockchainManager: AdvancedBlockchainManager = new AdvancedBlockchainManager();

  constructor(nodeType: string = NFTOperationNode.TYPE, name: string = NFTOperationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('mint', 'trigger', '铸造NFT');
    this.addInput('transfer', 'trigger', '转移NFT');
    this.addInput('contractAddress', 'string', '合约地址');
    this.addInput('to', 'string', '接收地址');
    this.addInput('from', 'string', '发送地址');
    this.addInput('tokenId', 'string', '代币ID');
    this.addInput('metadata', 'object', '元数据');
    this.addInput('name', 'string', 'NFT名称');
    this.addInput('description', 'string', 'NFT描述');
    this.addInput('imageUrl', 'string', '图片URL');
    this.addInput('attributes', 'array', '属性列表');

    // 输出端口
    this.addOutput('nft', 'object', 'NFT信息');
    this.addOutput('tokenId', 'string', '代币ID');
    this.addOutput('owner', 'string', '拥有者');
    this.addOutput('transaction', 'object', '交易信息');
    this.addOutput('onMinted', 'trigger', '铸造完成');
    this.addOutput('onTransferred', 'trigger', '转移完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const mintTrigger = inputs?.mint;
      const transferTrigger = inputs?.transfer;

      if (mintTrigger) {
        return await this.mintNFT(inputs);
      } else if (transferTrigger) {
        return await this.transferNFT(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('NFTOperationNode', 'NFT操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private async mintNFT(inputs: any): Promise<any> {
    const contractAddress = inputs?.contractAddress as string;
    const to = inputs?.to as string;
    const tokenId = inputs?.tokenId as string || Date.now().toString();
    const name = inputs?.name as string || 'Untitled NFT';
    const description = inputs?.description as string || '';
    const imageUrl = inputs?.imageUrl as string || '';
    const attributes = inputs?.attributes as NFTAttribute[] || [];

    if (!contractAddress || !to) {
      throw new Error('未提供合约地址或接收地址');
    }

    const metadata = {
      name,
      description,
      image: imageUrl,
      attributes
    };

    const nft = await NFTOperationNode.blockchainManager.mintNFT(
      contractAddress,
      to,
      tokenId,
      metadata
    );

    return {
      nft,
      tokenId: nft.tokenId,
      owner: nft.owner,
      transaction: null,
      onMinted: true,
      onTransferred: false,
      onError: false
    };
  }

  private async transferNFT(inputs: any): Promise<any> {
    const contractAddress = inputs?.contractAddress as string;
    const from = inputs?.from as string;
    const to = inputs?.to as string;
    const tokenId = inputs?.tokenId as string;

    if (!contractAddress || !from || !to || !tokenId) {
      throw new Error('未提供必要的转移参数');
    }

    const transaction = await NFTOperationNode.blockchainManager.transferNFT(
      contractAddress,
      from,
      to,
      tokenId
    );

    return {
      nft: null,
      tokenId,
      owner: to,
      transaction,
      onMinted: false,
      onTransferred: true,
      onError: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      nft: null,
      tokenId: '',
      owner: '',
      transaction: null,
      onMinted: false,
      onTransferred: false,
      onError: false
    };
  }
}
