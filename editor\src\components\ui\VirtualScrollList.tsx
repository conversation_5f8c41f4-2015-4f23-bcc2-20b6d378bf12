/**
 * VirtualScrollList.tsx
 * 
 * 虚拟滚动列表组件，用于高效渲染大量列表项
 */

import React, { useState, useEffect, useRef, useCallback, useMemo, useImperativeHandle } from 'react';
import { Spin } from 'antd';
import './VirtualScrollList.module.css';

/**
 * 虚拟滚动项目接口
 */
export interface VirtualScrollItem {
  id: string;
  data: any;
  height?: number;
}

/**
 * 虚拟滚动配置
 */
export interface VirtualScrollConfig {
  itemHeight: number;
  containerHeight: number;
  bufferSize: number;
  dynamicHeight: boolean;
  scrollThreshold: number;
  overscan: number;
}

/**
 * 虚拟滚动列表属性
 */
export interface VirtualScrollListProps {
  /** 列表项目 */
  items: VirtualScrollItem[];
  /** 项目渲染函数 */
  renderItem: (item: VirtualScrollItem, index: number) => React.ReactNode;
  /** 配置选项 */
  config?: Partial<VirtualScrollConfig>;
  /** 容器样式类名 */
  className?: string;
  /** 容器样式 */
  style?: React.CSSProperties;
  /** 加载状态 */
  loading?: boolean;
  /** 空状态渲染 */
  emptyRender?: () => React.ReactNode;
  /** 滚动回调 */
  onScroll?: (scrollTop: number, scrollLeft: number) => void;
  /** 项目点击回调 */
  onItemClick?: (item: VirtualScrollItem, index: number) => void;
  /** 项目高度变化回调（动态高度模式） */
  onItemHeightChange?: (id: string, height: number) => void;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: VirtualScrollConfig = {
  itemHeight: 40,
  containerHeight: 400,
  bufferSize: 5,
  dynamicHeight: false,
  scrollThreshold: 10,
  overscan: 3
};

/**
 * 虚拟滚动列表组件
 */
export const VirtualScrollList = React.forwardRef<
  {
    scrollToItem: (id: string, alignment?: 'start' | 'center' | 'end') => void;
    scrollToIndex: (index: number, alignment?: 'start' | 'center' | 'end') => void;
  },
  VirtualScrollListProps
>((props, ref) => {
  const {
    items,
    renderItem,
    config: userConfig,
    className,
    style,
    loading = false,
    emptyRender,
    onScroll,
    onItemClick,
    onItemHeightChange
  } = props;
  const config = useMemo(() => ({ ...DEFAULT_CONFIG, ...userConfig }), [userConfig]);
  
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(config.containerHeight);
  const [itemHeights, setItemHeights] = useState<Map<string, number>>(new Map());
  
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollElementRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const resizeObserver = useRef<ResizeObserver | null>(null);

  // 计算总高度
  const totalHeight = useMemo(() => {
    if (config.dynamicHeight) {
      return items.reduce((total, item) => {
        const height = itemHeights.get(item.id) || item.height || config.itemHeight;
        return total + height;
      }, 0);
    }
    return items.length * config.itemHeight;
  }, [items, itemHeights, config.dynamicHeight, config.itemHeight]);

  // 计算可见范围
  const visibleRange = useMemo(() => {
    if (items.length === 0) {
      return { start: 0, end: 0, offsetY: 0 };
    }

    let start = 0;
    let end = 0;

    if (config.dynamicHeight) {
      // 动态高度模式
      let currentTop = 0;
      let startFound = false;

      for (let i = 0; i < items.length; i++) {
        const itemHeight = itemHeights.get(items[i].id) || items[i].height || config.itemHeight;

        if (!startFound && currentTop + itemHeight > scrollTop) {
          start = i;
          startFound = true;
        }

        if (startFound && currentTop > scrollTop + containerHeight) {
          end = i - 1;
          break;
        }

        currentTop += itemHeight;
      }

      if (!startFound) {
        start = items.length - 1;
      }

      if (end === 0) {
        end = items.length - 1;
      }
    } else {
      // 固定高度模式
      start = Math.floor(scrollTop / config.itemHeight);
      end = Math.min(
        items.length - 1,
        Math.ceil((scrollTop + containerHeight) / config.itemHeight)
      );
    }

    // 添加缓冲区
    const bufferedStart = Math.max(0, start - config.bufferSize);
    const bufferedEnd = Math.min(items.length - 1, end + config.bufferSize);

    return {
      start: bufferedStart,
      end: bufferedEnd,
      offsetY: config.dynamicHeight ? 
        getOffsetForIndex(bufferedStart) : 
        bufferedStart * config.itemHeight
    };
  }, [items, scrollTop, containerHeight, itemHeights, config]);

  // 获取指定索引的偏移量（动态高度）
  const getOffsetForIndex = useCallback((index: number): number => {
    if (!config.dynamicHeight) {
      return index * config.itemHeight;
    }

    let offset = 0;
    for (let i = 0; i < index; i++) {
      const item = items[i];
      offset += itemHeights.get(item.id) || item.height || config.itemHeight;
    }
    return offset;
  }, [items, itemHeights, config.dynamicHeight, config.itemHeight]);

  // 可见项目
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end + 1).map((item, index) => ({
      item,
      index: visibleRange.start + index
    }));
  }, [items, visibleRange]);

  // 处理滚动事件
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    
    if (Math.abs(newScrollTop - scrollTop) > config.scrollThreshold) {
      setScrollTop(newScrollTop);
      onScroll?.(newScrollTop, e.currentTarget.scrollLeft);
    }
  }, [scrollTop, config.scrollThreshold, onScroll]);

  // 设置项目高度（动态高度模式）
  const setItemHeight = useCallback((id: string, height: number) => {
    if (config.dynamicHeight) {
      setItemHeights(prev => {
        const newHeights = new Map(prev);
        if (newHeights.get(id) !== height) {
          newHeights.set(id, height);
          onItemHeightChange?.(id, height);
        }
        return newHeights;
      });
    }
  }, [config.dynamicHeight, onItemHeightChange]);

  // 设置ResizeObserver（动态高度模式）
  useEffect(() => {
    if (!config.dynamicHeight) return;

    resizeObserver.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const element = entry.target as HTMLDivElement;
        const id = element.dataset.itemId;
        if (id) {
          const height = entry.contentRect.height;
          setItemHeight(id, height);
        }
      }
    });

    return () => {
      resizeObserver.current?.disconnect();
    };
  }, [config.dynamicHeight, setItemHeight]);

  // 观察项目元素尺寸变化
  useEffect(() => {
    if (!config.dynamicHeight || !resizeObserver.current) return;

    const observer = resizeObserver.current;
    
    // 观察所有可见项目
    itemRefs.current.forEach((element) => {
      observer.observe(element);
    });

    return () => {
      itemRefs.current.forEach((element) => {
        observer.unobserve(element);
      });
    };
  }, [visibleItems, config.dynamicHeight]);

  // 更新容器高度
  useEffect(() => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      setContainerHeight(rect.height);
    }
  }, []);

  // 滚动到指定项目
  const scrollToItem = useCallback((id: string, alignment: 'start' | 'center' | 'end' = 'start') => {
    const index = items.findIndex(item => item.id === id);
    if (index === -1 || !scrollElementRef.current) return;

    const itemTop = getOffsetForIndex(index);
    const itemHeight = config.dynamicHeight ? 
      (itemHeights.get(id) || config.itemHeight) : 
      config.itemHeight;

    let targetScrollTop = itemTop;

    switch (alignment) {
      case 'center':
        targetScrollTop = itemTop - containerHeight / 2 + itemHeight / 2;
        break;
      case 'end':
        targetScrollTop = itemTop - containerHeight + itemHeight;
        break;
    }

    targetScrollTop = Math.max(0, Math.min(targetScrollTop, totalHeight - containerHeight));
    scrollElementRef.current.scrollTop = targetScrollTop;
  }, [items, getOffsetForIndex, itemHeights, config, containerHeight, totalHeight]);

  // 滚动到指定索引
  const scrollToIndex = useCallback((index: number, alignment: 'start' | 'center' | 'end' = 'start') => {
    if (index < 0 || index >= items.length) return;
    const item = items[index];
    scrollToItem(item.id, alignment);
  }, [items, scrollToItem]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    scrollToItem,
    scrollToIndex
  }), [scrollToItem, scrollToIndex]);

  // 渲染项目
  const renderVirtualItem = useCallback((item: VirtualScrollItem, index: number) => {
    const itemHeight = config.dynamicHeight ? 
      (itemHeights.get(item.id) || item.height || config.itemHeight) : 
      config.itemHeight;

    return (
      <div
        key={item.id}
        ref={(el) => {
          if (el) {
            itemRefs.current.set(item.id, el);
          } else {
            itemRefs.current.delete(item.id);
          }
        }}
        data-item-id={item.id}
        className="virtual-scroll-item"
        style={{
          height: config.dynamicHeight ? 'auto' : itemHeight,
          minHeight: config.dynamicHeight ? itemHeight : undefined
        }}
        onClick={() => onItemClick?.(item, index)}
      >
        {renderItem(item, index)}
      </div>
    );
  }, [renderItem, itemHeights, config, onItemClick]);

  if (loading) {
    return (
      <div className={`virtual-scroll-container ${className || ''}`} style={style}>
        <Spin size="large" />
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className={`virtual-scroll-container ${className || ''}`} style={style}>
        {emptyRender ? emptyRender() : <div className="virtual-scroll-empty">暂无数据</div>}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`virtual-scroll-container ${className || ''}`}
      style={{ height: config.containerHeight, ...style }}
    >
      <div
        ref={scrollElementRef}
        className="virtual-scroll-wrapper"
        style={{ height: '100%', overflow: 'auto' }}
        onScroll={handleScroll}
      >
        <div
          className="virtual-scroll-spacer"
          style={{ height: totalHeight, position: 'relative' }}
        >
          <div
            className="virtual-scroll-content"
            style={{
              transform: `translateY(${visibleRange.offsetY}px)`,
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0
            }}
          >
            {visibleItems.map(({ item, index }) => renderVirtualItem(item, index))}
          </div>
        </div>
      </div>
    </div>
  );
});

// 导出工具函数
export const VirtualScrollUtils = {
  scrollToItem: (listRef: React.RefObject<any>, id: string, alignment?: 'start' | 'center' | 'end') => {
    listRef.current?.scrollToItem(id, alignment);
  },
  
  scrollToIndex: (listRef: React.RefObject<any>, index: number, alignment?: 'start' | 'center' | 'end') => {
    listRef.current?.scrollToIndex(index, alignment);
  }
};

export default VirtualScrollList;
