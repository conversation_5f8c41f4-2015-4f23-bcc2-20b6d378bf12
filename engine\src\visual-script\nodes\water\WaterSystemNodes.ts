/**
 * 水系统节点集合
 * 提供水体模拟、流体动力学、水面效果等功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Vector2, Color, Plane } from 'three';
import { Entity } from '../entity/EntityNodes';

/**
 * 水体类型枚举
 */
export enum WaterType {
  OCEAN = 'ocean',
  LAKE = 'lake',
  RIVER = 'river',
  STREAM = 'stream',
  POOL = 'pool',
  WATERFALL = 'waterfall',
  FOUNTAIN = 'fountain'
}

/**
 * 水质类型枚举
 */
export enum WaterQuality {
  CRYSTAL_CLEAR = 'crystal_clear',
  CLEAR = 'clear',
  SLIGHTLY_MURKY = 'slightly_murky',
  MURKY = 'murky',
  DIRTY = 'dirty',
  POLLUTED = 'polluted'
}

/**
 * 水体配置接口
 */
export interface WaterConfig {
  type: WaterType;
  quality: WaterQuality;
  size: Vector3;
  depth: number;
  temperature: number;
  salinity: number;
  pH: number;
  transparency: number;
  color: Color;
  surfaceRoughness: number;
  flowSpeed: number;
  flowDirection: Vector3;
  waveHeight: number;
  waveFrequency: number;
  waveSpeed: number;
  foamIntensity: number;
  causticIntensity: number;
  reflectionIntensity: number;
  refractionIntensity: number;
}

/**
 * 水面波浪配置
 */
export interface WaveConfig {
  amplitude: number;
  frequency: number;
  speed: number;
  direction: Vector2;
  steepness: number;
  wavelength: number;
}

/**
 * 流体粒子接口
 */
export interface FluidParticle {
  id: string;
  position: Vector3;
  velocity: Vector3;
  acceleration: Vector3;
  density: number;
  pressure: number;
  temperature: number;
  mass: number;
  viscosity: number;
  lifetime: number;
  age: number;
}

/**
 * 水体物理属性
 */
export interface WaterPhysics {
  density: number;
  viscosity: number;
  surfaceTension: number;
  compressibility: number;
  thermalExpansion: number;
  freezingPoint: number;
  boilingPoint: number;
  specificHeat: number;
}

/**
 * 高级水体模拟器
 */
class AdvancedWaterSimulator {
  private particles: Map<string, FluidParticle> = new Map();
  private waterBodies: Map<string, WaterConfig> = new Map();
  private waves: WaveConfig[] = [];
  private simulationTime: number = 0;
  private timeStep: number = 1/60; // 60 FPS
  private gravity: Vector3 = new Vector3(0, -9.81, 0);
  private smoothingRadius: number = 1.0;
  private restDensity: number = 1000; // kg/m³

  /**
   * 创建水体
   */
  createWaterBody(id: string, config: WaterConfig): void {
    this.waterBodies.set(id, config);
    
    // 根据水体类型初始化粒子
    this.initializeParticles(id, config);
    
    Debug.log('AdvancedWaterSimulator', `水体创建: ${id} (${config.type})`);
  }

  /**
   * 初始化流体粒子
   */
  private initializeParticles(waterId: string, config: WaterConfig): void {
    const particleCount = this.calculateParticleCount(config);
    const spacing = this.calculateParticleSpacing(config, particleCount);
    
    for (let i = 0; i < particleCount; i++) {
      const particle = this.createParticle(waterId, config, spacing, i);
      this.particles.set(particle.id, particle);
    }
  }

  /**
   * 计算粒子数量
   */
  private calculateParticleCount(config: WaterConfig): number {
    const volume = config.size.x * config.size.y * config.size.z;
    const particleDensity = this.getParticleDensityByType(config.type);
    return Math.floor(volume * particleDensity);
  }

  /**
   * 根据水体类型获取粒子密度
   */
  private getParticleDensityByType(type: WaterType): number {
    const densities: { [key in WaterType]: number } = {
      [WaterType.OCEAN]: 50,
      [WaterType.LAKE]: 30,
      [WaterType.RIVER]: 40,
      [WaterType.STREAM]: 20,
      [WaterType.POOL]: 60,
      [WaterType.WATERFALL]: 80,
      [WaterType.FOUNTAIN]: 100
    };
    return densities[type];
  }

  /**
   * 计算粒子间距
   */
  private calculateParticleSpacing(config: WaterConfig, particleCount: number): number {
    const volume = config.size.x * config.size.y * config.size.z;
    return Math.pow(volume / particleCount, 1/3);
  }

  /**
   * 创建单个粒子
   */
  private createParticle(waterId: string, config: WaterConfig, spacing: number, index: number): FluidParticle {
    const gridSize = Math.ceil(Math.pow(this.calculateParticleCount(config), 1/3));
    const x = (index % gridSize) * spacing - config.size.x / 2;
    const y = (Math.floor(index / gridSize) % gridSize) * spacing;
    const z = Math.floor(index / (gridSize * gridSize)) * spacing - config.size.z / 2;

    return {
      id: `${waterId}_particle_${index}`,
      position: new Vector3(x, y, z),
      velocity: new Vector3(
        config.flowDirection.x * config.flowSpeed,
        0,
        config.flowDirection.z * config.flowSpeed
      ),
      acceleration: new Vector3(0, 0, 0),
      density: this.restDensity,
      pressure: 0,
      temperature: config.temperature,
      mass: 1.0,
      viscosity: this.getViscosityByQuality(config.quality),
      lifetime: Infinity,
      age: 0
    };
  }

  /**
   * 根据水质获取粘度
   */
  private getViscosityByQuality(quality: WaterQuality): number {
    const viscosities: { [key in WaterQuality]: number } = {
      [WaterQuality.CRYSTAL_CLEAR]: 0.001,
      [WaterQuality.CLEAR]: 0.0012,
      [WaterQuality.SLIGHTLY_MURKY]: 0.0015,
      [WaterQuality.MURKY]: 0.002,
      [WaterQuality.DIRTY]: 0.003,
      [WaterQuality.POLLUTED]: 0.005
    };
    return viscosities[quality];
  }

  /**
   * 更新模拟
   */
  updateSimulation(): void {
    this.simulationTime += this.timeStep;
    
    // 更新所有粒子
    for (const particle of this.particles.values()) {
      this.updateParticle(particle);
    }
    
    // 更新波浪
    this.updateWaves();
    
    // 处理粒子间相互作用
    this.processParticleInteractions();
  }

  /**
   * 更新单个粒子
   */
  private updateParticle(particle: FluidParticle): void {
    // 计算密度和压力
    particle.density = this.calculateDensity(particle);
    particle.pressure = this.calculatePressure(particle.density);
    
    // 计算力
    const pressureForce = this.calculatePressureForce(particle);
    const viscosityForce = this.calculateViscosityForce(particle);
    const gravityForce = this.gravity.clone().multiplyScalar(particle.mass);
    
    // 总力
    particle.acceleration.copy(pressureForce)
      .add(viscosityForce)
      .add(gravityForce)
      .divideScalar(particle.mass);
    
    // 更新速度和位置
    particle.velocity.add(particle.acceleration.clone().multiplyScalar(this.timeStep));
    particle.position.add(particle.velocity.clone().multiplyScalar(this.timeStep));
    
    // 更新年龄
    particle.age += this.timeStep;
  }

  /**
   * 计算粒子密度
   */
  private calculateDensity(particle: FluidParticle): number {
    let density = 0;
    
    for (const neighbor of this.particles.values()) {
      if (neighbor.id === particle.id) continue;
      
      const distance = particle.position.distanceTo(neighbor.position);
      if (distance < this.smoothingRadius) {
        density += neighbor.mass * this.smoothingKernel(distance);
      }
    }
    
    return density;
  }

  /**
   * 平滑核函数
   */
  private smoothingKernel(distance: number): number {
    if (distance >= this.smoothingRadius) return 0;
    
    const h = this.smoothingRadius;
    const factor = 315 / (64 * Math.PI * Math.pow(h, 9));
    return factor * Math.pow(h * h - distance * distance, 3);
  }

  /**
   * 计算压力
   */
  private calculatePressure(density: number): number {
    const stiffness = 1000; // 压力常数
    return stiffness * (density - this.restDensity);
  }

  /**
   * 计算压力力
   */
  private calculatePressureForce(particle: FluidParticle): Vector3 {
    const force = new Vector3(0, 0, 0);
    
    for (const neighbor of this.particles.values()) {
      if (neighbor.id === particle.id) continue;
      
      const distance = particle.position.distanceTo(neighbor.position);
      if (distance < this.smoothingRadius && distance > 0) {
        const direction = particle.position.clone().sub(neighbor.position).normalize();
        const pressureGradient = this.pressureGradientKernel(distance);
        const pressureTerm = (particle.pressure + neighbor.pressure) / (2 * neighbor.density);
        
        force.add(direction.multiplyScalar(-neighbor.mass * pressureTerm * pressureGradient));
      }
    }
    
    return force;
  }

  /**
   * 压力梯度核函数
   */
  private pressureGradientKernel(distance: number): number {
    if (distance >= this.smoothingRadius) return 0;
    
    const h = this.smoothingRadius;
    const factor = -45 / (Math.PI * Math.pow(h, 6));
    return factor * Math.pow(h - distance, 2);
  }

  /**
   * 计算粘性力
   */
  private calculateViscosityForce(particle: FluidParticle): Vector3 {
    const force = new Vector3(0, 0, 0);
    
    for (const neighbor of this.particles.values()) {
      if (neighbor.id === particle.id) continue;
      
      const distance = particle.position.distanceTo(neighbor.position);
      if (distance < this.smoothingRadius) {
        const velocityDiff = neighbor.velocity.clone().sub(particle.velocity);
        const viscosityLaplacian = this.viscosityLaplacianKernel(distance);
        
        force.add(velocityDiff.multiplyScalar(
          particle.viscosity * neighbor.mass * viscosityLaplacian / neighbor.density
        ));
      }
    }
    
    return force;
  }

  /**
   * 粘性拉普拉斯核函数
   */
  private viscosityLaplacianKernel(distance: number): number {
    if (distance >= this.smoothingRadius) return 0;
    
    const h = this.smoothingRadius;
    const factor = 45 / (Math.PI * Math.pow(h, 6));
    return factor * (h - distance);
  }

  /**
   * 处理粒子间相互作用
   */
  private processParticleInteractions(): void {
    // 碰撞检测和响应
    for (const particle of this.particles.values()) {
      this.handleBoundaryCollisions(particle);
      this.handleParticleCollisions(particle);
    }
  }

  /**
   * 处理边界碰撞
   */
  private handleBoundaryCollisions(particle: FluidParticle): void {
    const damping = 0.8; // 阻尼系数
    
    // 简化的边界处理（假设在原点周围的立方体区域）
    const bounds = 10;
    
    if (particle.position.x < -bounds) {
      particle.position.x = -bounds;
      particle.velocity.x *= -damping;
    } else if (particle.position.x > bounds) {
      particle.position.x = bounds;
      particle.velocity.x *= -damping;
    }
    
    if (particle.position.y < 0) {
      particle.position.y = 0;
      particle.velocity.y *= -damping;
    } else if (particle.position.y > bounds) {
      particle.position.y = bounds;
      particle.velocity.y *= -damping;
    }
    
    if (particle.position.z < -bounds) {
      particle.position.z = -bounds;
      particle.velocity.z *= -damping;
    } else if (particle.position.z > bounds) {
      particle.position.z = bounds;
      particle.velocity.z *= -damping;
    }
  }

  /**
   * 处理粒子碰撞
   */
  private handleParticleCollisions(particle: FluidParticle): void {
    const minDistance = 0.1;
    
    for (const neighbor of this.particles.values()) {
      if (neighbor.id === particle.id) continue;
      
      const distance = particle.position.distanceTo(neighbor.position);
      if (distance < minDistance) {
        const direction = particle.position.clone().sub(neighbor.position);
        if (direction.length() > 0) {
          direction.normalize();
          const overlap = minDistance - distance;
          particle.position.add(direction.multiplyScalar(overlap * 0.5));
          neighbor.position.sub(direction.multiplyScalar(overlap * 0.5));
        }
      }
    }
  }

  /**
   * 更新波浪
   */
  private updateWaves(): void {
    for (const wave of this.waves) {
      // 更新波浪相位
      // 这里可以添加更复杂的波浪更新逻辑
    }
  }

  /**
   * 添加波浪
   */
  addWave(wave: WaveConfig): void {
    this.waves.push(wave);
  }

  /**
   * 获取水面高度
   */
  getWaterSurfaceHeight(x: number, z: number): number {
    let height = 0;
    
    for (const wave of this.waves) {
      const phase = wave.direction.x * x + wave.direction.y * z - wave.speed * this.simulationTime;
      height += wave.amplitude * Math.sin(wave.frequency * phase);
    }
    
    return height;
  }

  /**
   * 获取所有粒子
   */
  getParticles(): FluidParticle[] {
    return Array.from(this.particles.values());
  }

  /**
   * 获取水体配置
   */
  getWaterBody(id: string): WaterConfig | undefined {
    return this.waterBodies.get(id);
  }

  /**
   * 清理模拟
   */
  cleanup(): void {
    this.particles.clear();
    this.waterBodies.clear();
    this.waves = [];
    this.simulationTime = 0;
  }
}

/**
 * 创建水体节点
 */
export class CreateWaterBodyNode extends VisualScriptNode {
  public static readonly TYPE = 'CreateWaterBody';
  public static readonly NAME = '创建水体';
  public static readonly DESCRIPTION = '创建各种类型的水体';

  private static waterSimulator: AdvancedWaterSimulator = new AdvancedWaterSimulator();

  constructor(nodeType: string = CreateWaterBodyNode.TYPE, name: string = CreateWaterBodyNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('waterType', 'string', '水体类型');
    this.addInput('size', 'object', '尺寸');
    this.addInput('depth', 'number', '深度');
    this.addInput('temperature', 'number', '温度');
    this.addInput('quality', 'string', '水质');
    this.addInput('color', 'object', '颜色');
    this.addInput('transparency', 'number', '透明度');
    this.addInput('flowSpeed', 'number', '流速');
    this.addInput('flowDirection', 'object', '流向');
    this.addInput('waveHeight', 'number', '波浪高度');
    this.addInput('waveFrequency', 'number', '波浪频率');

    // 输出端口
    this.addOutput('waterBody', 'object', '水体');
    this.addOutput('waterId', 'string', '水体ID');
    this.addOutput('particleCount', 'number', '粒子数量');
    this.addOutput('volume', 'number', '体积');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const waterType = inputs?.waterType as string || 'lake';
      const size = inputs?.size as Vector3 || new Vector3(10, 2, 10);
      const depth = inputs?.depth as number || size.y;
      const temperature = inputs?.temperature as number || 20;
      const quality = inputs?.quality as string || 'clear';
      const color = inputs?.color as Color || new Color(0.2, 0.6, 1.0);
      const transparency = inputs?.transparency as number || 0.8;
      const flowSpeed = inputs?.flowSpeed as number || 0;
      const flowDirection = inputs?.flowDirection as Vector3 || new Vector3(1, 0, 0);
      const waveHeight = inputs?.waveHeight as number || 0.1;
      const waveFrequency = inputs?.waveFrequency as number || 1.0;

      // 创建水体配置
      const waterConfig: WaterConfig = {
        type: waterType as WaterType,
        quality: quality as WaterQuality,
        size,
        depth,
        temperature,
        salinity: this.getSalinityByType(waterType as WaterType),
        pH: this.getPHByQuality(quality as WaterQuality),
        transparency,
        color,
        surfaceRoughness: this.getSurfaceRoughnessByType(waterType as WaterType),
        flowSpeed,
        flowDirection: flowDirection.normalize(),
        waveHeight,
        waveFrequency,
        waveSpeed: 1.0,
        foamIntensity: 0.5,
        causticIntensity: 0.7,
        reflectionIntensity: 0.8,
        refractionIntensity: 0.9
      };

      // 生成水体ID
      const waterId = this.generateWaterId();

      // 创建水体
      CreateWaterBodyNode.waterSimulator.createWaterBody(waterId, waterConfig);

      // 计算体积
      const volume = size.x * size.y * size.z;

      // 获取粒子数量
      const particles = CreateWaterBodyNode.waterSimulator.getParticles();
      const particleCount = particles.length;

      Debug.log('CreateWaterBodyNode', `水体创建成功: ${waterId} (${waterType})`);

      return {
        waterBody: waterConfig,
        waterId,
        particleCount,
        volume,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('CreateWaterBodyNode', '创建水体失败', error);
      return {
        waterBody: null,
        waterId: '',
        particleCount: 0,
        volume: 0,
        onCreated: false,
        onError: true
      };
    }
  }

  private getSalinityByType(type: WaterType): number {
    const salinities: { [key in WaterType]: number } = {
      [WaterType.OCEAN]: 35,
      [WaterType.LAKE]: 0.5,
      [WaterType.RIVER]: 0.1,
      [WaterType.STREAM]: 0.05,
      [WaterType.POOL]: 0,
      [WaterType.WATERFALL]: 0.1,
      [WaterType.FOUNTAIN]: 0
    };
    return salinities[type];
  }

  private getPHByQuality(quality: WaterQuality): number {
    const pHValues: { [key in WaterQuality]: number } = {
      [WaterQuality.CRYSTAL_CLEAR]: 7.2,
      [WaterQuality.CLEAR]: 7.0,
      [WaterQuality.SLIGHTLY_MURKY]: 6.8,
      [WaterQuality.MURKY]: 6.5,
      [WaterQuality.DIRTY]: 6.0,
      [WaterQuality.POLLUTED]: 5.5
    };
    return pHValues[quality];
  }

  private getSurfaceRoughnessByType(type: WaterType): number {
    const roughness: { [key in WaterType]: number } = {
      [WaterType.OCEAN]: 0.8,
      [WaterType.LAKE]: 0.3,
      [WaterType.RIVER]: 0.5,
      [WaterType.STREAM]: 0.6,
      [WaterType.POOL]: 0.1,
      [WaterType.WATERFALL]: 0.9,
      [WaterType.FOUNTAIN]: 0.4
    };
    return roughness[type];
  }

  private generateWaterId(): string {
    return 'water_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      waterBody: null,
      waterId: '',
      particleCount: 0,
      volume: 0,
      onCreated: false,
      onError: false
    };
  }
}

/**
 * 水面波浪节点
 */
export class WaterWaveNode extends VisualScriptNode {
  public static readonly TYPE = 'WaterWave';
  public static readonly NAME = '水面波浪';
  public static readonly DESCRIPTION = '控制水面波浪效果';

  private static waterSimulator: AdvancedWaterSimulator = new AdvancedWaterSimulator();

  constructor(nodeType: string = WaterWaveNode.TYPE, name: string = WaterWaveNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('addWave', 'trigger', '添加波浪');
    this.addInput('amplitude', 'number', '振幅');
    this.addInput('frequency', 'number', '频率');
    this.addInput('speed', 'number', '速度');
    this.addInput('direction', 'object', '方向');
    this.addInput('steepness', 'number', '陡峭度');
    this.addInput('wavelength', 'number', '波长');
    this.addInput('position', 'object', '查询位置');
    this.addInput('getHeight', 'trigger', '获取高度');

    // 输出端口
    this.addOutput('waveHeight', 'number', '波浪高度');
    this.addOutput('surfaceNormal', 'object', '表面法线');
    this.addOutput('waveConfig', 'object', '波浪配置');
    this.addOutput('onWaveAdded', 'trigger', '波浪添加完成');
    this.addOutput('onHeightCalculated', 'trigger', '高度计算完成');
  }

  public execute(inputs?: any): any {
    try {
      const addWaveTrigger = inputs?.addWave;
      const getHeightTrigger = inputs?.getHeight;

      if (addWaveTrigger) {
        return this.addWave(inputs);
      } else if (getHeightTrigger) {
        return this.getWaveHeight(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('WaterWaveNode', '水面波浪操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private addWave(inputs: any): any {
    const amplitude = inputs?.amplitude as number || 0.5;
    const frequency = inputs?.frequency as number || 1.0;
    const speed = inputs?.speed as number || 1.0;
    const direction = inputs?.direction as Vector2 || new Vector2(1, 0);
    const steepness = inputs?.steepness as number || 0.5;
    const wavelength = inputs?.wavelength as number || 2.0;

    const waveConfig: WaveConfig = {
      amplitude,
      frequency,
      speed,
      direction: direction.normalize(),
      steepness: Math.max(0, Math.min(1, steepness)),
      wavelength
    };

    WaterWaveNode.waterSimulator.addWave(waveConfig);

    Debug.log('WaterWaveNode', `波浪添加成功: 振幅=${amplitude}, 频率=${frequency}`);

    return {
      waveHeight: 0,
      surfaceNormal: new Vector3(0, 1, 0),
      waveConfig,
      onWaveAdded: true,
      onHeightCalculated: false
    };
  }

  private getWaveHeight(inputs: any): any {
    const position = inputs?.position as Vector3 || new Vector3(0, 0, 0);

    const height = WaterWaveNode.waterSimulator.getWaterSurfaceHeight(position.x, position.z);

    // 计算表面法线（简化）
    const epsilon = 0.1;
    const heightX = WaterWaveNode.waterSimulator.getWaterSurfaceHeight(position.x + epsilon, position.z);
    const heightZ = WaterWaveNode.waterSimulator.getWaterSurfaceHeight(position.x, position.z + epsilon);

    const normal = new Vector3(
      -(heightX - height) / epsilon,
      1,
      -(heightZ - height) / epsilon
    ).normalize();

    return {
      waveHeight: height,
      surfaceNormal: normal,
      waveConfig: null,
      onWaveAdded: false,
      onHeightCalculated: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      waveHeight: 0,
      surfaceNormal: new Vector3(0, 1, 0),
      waveConfig: null,
      onWaveAdded: false,
      onHeightCalculated: false
    };
  }
}
