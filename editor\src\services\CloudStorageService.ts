/**
 * 云端项目存储服务
 * 提供云端项目存储、文件版本管理、增量同步、数据压缩等功能
 */
import { EventEmitter } from '../utils/EventEmitter';

// 存储提供商枚举
export enum StorageProvider {
  AWS_S3 = 'aws_s3',
  AZURE_BLOB = 'azure_blob',
  GOOGLE_CLOUD = 'google_cloud',
  ALIYUN_OSS = 'aliyun_oss',
  TENCENT_COS = 'tencent_cos',
  CUSTOM = 'custom'
}

// 文件状态枚举
export enum FileStatus {
  UPLOADING = 'uploading',
  UPLOADED = 'uploaded',
  DOWNLOADING = 'downloading',
  DOWNLOADED = 'downloaded',
  SYNCING = 'syncing',
  SYNCED = 'synced',
  CONFLICT = 'conflict',
  ERROR = 'error'
}

// 同步策略枚举
export enum SyncStrategy {
  MANUAL = 'manual',
  AUTO = 'auto',
  REAL_TIME = 'real_time',
  SCHEDULED = 'scheduled'
}

// 压缩算法枚举
export enum CompressionAlgorithm {
  NONE = 'none',
  GZIP = 'gzip',
  BROTLI = 'brotli',
  LZ4 = 'lz4',
  ZSTD = 'zstd'
}

// 云端文件信息接口
export interface CloudFile {
  id: string;
  name: string;
  path: string;
  size: number;
  compressedSize?: number;
  mimeType: string;
  hash: string;
  version: number;
  status: FileStatus;
  
  // 时间戳
  createdAt: number;
  updatedAt: number;
  lastSyncAt?: number;
  
  // 版本信息
  versions: FileVersion[];
  
  // 元数据
  metadata: {
    author: string;
    description?: string;
    tags: string[];
    isPublic: boolean;
    permissions: FilePermissions;
  };
  
  // 同步信息
  syncInfo: {
    strategy: SyncStrategy;
    lastSync: number;
    conflicts: FileConflict[];
  };
}

// 文件版本接口
export interface FileVersion {
  id: string;
  version: number;
  size: number;
  hash: string;
  createdAt: number;
  author: string;
  message?: string;
  changes: FileChange[];
}

// 文件变更接口
export interface FileChange {
  type: 'create' | 'update' | 'delete' | 'rename' | 'move';
  path: string;
  oldPath?: string;
  size?: number;
  timestamp: number;
}

// 文件权限接口
export interface FilePermissions {
  owner: string;
  readers: string[];
  writers: string[];
  admins: string[];
  isPublic: boolean;
  allowDownload: boolean;
  allowShare: boolean;
}

// 文件冲突接口
export interface FileConflict {
  id: string;
  type: 'content' | 'metadata' | 'permission';
  localVersion: FileVersion;
  remoteVersion: FileVersion;
  resolvedAt?: number;
  resolution?: 'local' | 'remote' | 'merge' | 'manual';
}

// 云端项目接口
export interface CloudProject {
  id: string;
  name: string;
  description: string;
  
  // 项目信息
  owner: string;
  collaborators: ProjectCollaborator[];
  
  // 存储信息
  storageProvider: StorageProvider;
  storageConfig: StorageConfig;
  totalSize: number;
  fileCount: number;
  
  // 文件列表
  files: CloudFile[];
  
  // 同步设置
  syncSettings: ProjectSyncSettings;
  
  // 时间戳
  createdAt: number;
  updatedAt: number;
  lastSyncAt?: number;
}

// 项目协作者接口
export interface ProjectCollaborator {
  userId: string;
  username: string;
  email: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  permissions: string[];
  joinedAt: number;
}

// 存储配置接口
export interface StorageConfig {
  provider: StorageProvider;
  region?: string;
  bucket?: string;
  accessKey?: string;
  secretKey?: string;
  endpoint?: string;
  compression: CompressionAlgorithm;
  encryption: boolean;
  customConfig?: Record<string, any>;
}

// 项目同步设置接口
export interface ProjectSyncSettings {
  strategy: SyncStrategy;
  autoSyncInterval: number; // 分钟
  conflictResolution: 'manual' | 'auto_local' | 'auto_remote';
  enableCompression: boolean;
  compressionAlgorithm: CompressionAlgorithm;
  enableEncryption: boolean;
  maxFileSize: number; // MB
  excludePatterns: string[];
}

// 上传进度接口
export interface UploadProgress {
  fileId: string;
  fileName: string;
  loaded: number;
  total: number;
  percentage: number;
  speed: number; // bytes/second
  remainingTime: number; // seconds
  chunkIndex?: number;
  totalChunks?: number;
}

// 下载进度接口
export interface DownloadProgress {
  fileId: string;
  fileName: string;
  loaded: number;
  total: number;
  percentage: number;
  speed: number;
  remainingTime: number;
}

// 同步进度接口
export interface SyncProgress {
  projectId: string;
  phase: 'scanning' | 'uploading' | 'downloading' | 'resolving' | 'completed';
  filesProcessed: number;
  totalFiles: number;
  bytesTransferred: number;
  totalBytes: number;
  currentFile?: string;
  conflicts: FileConflict[];
}

/**
 * 云端项目存储服务类
 */
export class CloudStorageService extends EventEmitter {
  private static instance: CloudStorageService;
  private projects: Map<string, CloudProject> = new Map();
  private activeProject: CloudProject | null = null;
  private storageConfig: StorageConfig | null = null;
  private isOnline: boolean = navigator.onLine;
  private syncQueue: Map<string, CloudFile> = new Map();
  private uploadQueue: Map<string, File> = new Map();

  private constructor() {
    super();
    this.initializeNetworkMonitoring();
    this.initializeAutoSync();
  }

  public static getInstance(): CloudStorageService {
    if (!CloudStorageService.instance) {
      CloudStorageService.instance = new CloudStorageService();
    }
    return CloudStorageService.instance;
  }

  /**
   * 初始化网络监控
   */
  private initializeNetworkMonitoring(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.emit('networkStatusChanged', { online: true });
      this.resumeSync();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.emit('networkStatusChanged', { online: false });
      this.pauseSync();
    });
  }

  /**
   * 初始化自动同步
   */
  private initializeAutoSync(): void {
    setInterval(() => {
      if (this.isOnline && this.activeProject) {
        this.autoSync();
      }
    }, 60000); // 每分钟检查一次
  }

  /**
   * 配置存储提供商
   */
  public configureStorage(config: StorageConfig): void {
    this.storageConfig = config;
    this.emit('storageConfigured', config);
  }

  /**
   * 创建云端项目
   */
  public async createProject(
    name: string,
    description: string,
    storageConfig?: StorageConfig
  ): Promise<CloudProject> {
    const projectId = `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const project: CloudProject = {
      id: projectId,
      name,
      description,
      owner: 'current_user', // 应该从用户服务获取
      collaborators: [],
      storageProvider: storageConfig?.provider || this.storageConfig?.provider || StorageProvider.CUSTOM,
      storageConfig: storageConfig || this.storageConfig || {
        provider: StorageProvider.CUSTOM,
        compression: CompressionAlgorithm.GZIP,
        encryption: true
      },
      totalSize: 0,
      fileCount: 0,
      files: [],
      syncSettings: {
        strategy: SyncStrategy.AUTO,
        autoSyncInterval: 5,
        conflictResolution: 'manual',
        enableCompression: true,
        compressionAlgorithm: CompressionAlgorithm.GZIP,
        enableEncryption: true,
        maxFileSize: 100,
        excludePatterns: ['*.tmp', '*.log', 'node_modules/**']
      },
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    this.projects.set(projectId, project);
    this.emit('projectCreated', project);
    
    return project;
  }

  /**
   * 打开项目
   */
  public async openProject(projectId: string): Promise<CloudProject> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    this.activeProject = project;
    this.emit('projectOpened', project);
    
    // 开始同步
    await this.syncProject(projectId);
    
    return project;
  }

  /**
   * 上传文件
   */
  public async uploadFile(
    file: File,
    path: string,
    options?: {
      compress?: boolean;
      encrypt?: boolean;
      chunkSize?: number;
      resumable?: boolean;
    }
  ): Promise<CloudFile> {
    if (!this.activeProject) {
      throw new Error('No active project');
    }

    const fileId = `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const hash = await this.calculateFileHash(file);
    
    // 检查是否已存在相同文件
    const existingFile = this.activeProject.files.find(f => f.hash === hash);
    if (existingFile) {
      this.emit('fileAlreadyExists', existingFile);
      return existingFile;
    }

    const cloudFile: CloudFile = {
      id: fileId,
      name: file.name,
      path,
      size: file.size,
      mimeType: file.type,
      hash,
      version: 1,
      status: FileStatus.UPLOADING,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      versions: [],
      metadata: {
        author: 'current_user',
        tags: [],
        isPublic: false,
        permissions: {
          owner: 'current_user',
          readers: [],
          writers: [],
          admins: [],
          isPublic: false,
          allowDownload: true,
          allowShare: false
        }
      },
      syncInfo: {
        strategy: this.activeProject.syncSettings.strategy,
        lastSync: 0,
        conflicts: []
      }
    };

    this.activeProject.files.push(cloudFile);
    this.uploadQueue.set(fileId, file);

    try {
      // 开始上传
      await this.performUpload(cloudFile, file, options);
      
      cloudFile.status = FileStatus.UPLOADED;
      cloudFile.lastSyncAt = Date.now();
      
      this.emit('fileUploaded', cloudFile);
      return cloudFile;
      
    } catch (error) {
      cloudFile.status = FileStatus.ERROR;
      this.emit('fileUploadError', { file: cloudFile, error });
      throw error;
    } finally {
      this.uploadQueue.delete(fileId);
    }
  }

  /**
   * 执行文件上传
   */
  private async performUpload(
    cloudFile: CloudFile,
    file: File,
    options?: any
  ): Promise<void> {
    const chunkSize = options?.chunkSize || 1024 * 1024; // 1MB chunks
    const totalChunks = Math.ceil(file.size / chunkSize);
    
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      // 模拟上传延迟
      await new Promise(resolve => setTimeout(resolve, 100));

      // TODO: 实际上传 chunk 到云存储
      console.log(`上传分片 ${chunkIndex + 1}/${totalChunks}，大小: ${chunk.size} 字节`);
      
      const progress: UploadProgress = {
        fileId: cloudFile.id,
        fileName: cloudFile.name,
        loaded: end,
        total: file.size,
        percentage: (end / file.size) * 100,
        speed: chunkSize / 0.1, // 模拟速度
        remainingTime: ((file.size - end) / chunkSize) * 0.1,
        chunkIndex,
        totalChunks
      };
      
      this.emit('uploadProgress', progress);
    }
  }

  /**
   * 下载文件
   */
  public async downloadFile(fileId: string): Promise<Blob> {
    if (!this.activeProject) {
      throw new Error('No active project');
    }

    const cloudFile = this.activeProject.files.find(f => f.id === fileId);
    if (!cloudFile) {
      throw new Error('File not found');
    }

    cloudFile.status = FileStatus.DOWNLOADING;
    this.emit('downloadStarted', cloudFile);

    try {
      // 模拟下载过程
      const blob = await this.performDownload(cloudFile);
      
      cloudFile.status = FileStatus.DOWNLOADED;
      this.emit('fileDownloaded', cloudFile);
      
      return blob;
      
    } catch (error) {
      cloudFile.status = FileStatus.ERROR;
      this.emit('fileDownloadError', { file: cloudFile, error });
      throw error;
    }
  }

  /**
   * 执行文件下载
   */
  private async performDownload(cloudFile: CloudFile): Promise<Blob> {
    const chunkSize = 1024 * 1024; // 1MB chunks
    const totalChunks = Math.ceil(cloudFile.size / chunkSize);
    const chunks: ArrayBuffer[] = [];
    
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      // 模拟下载延迟
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const start = chunkIndex * chunkSize;
      const end = Math.min(start + chunkSize, cloudFile.size);
      
      // 模拟chunk数据
      const chunk = new ArrayBuffer(end - start);
      chunks.push(chunk);
      
      const progress: DownloadProgress = {
        fileId: cloudFile.id,
        fileName: cloudFile.name,
        loaded: end,
        total: cloudFile.size,
        percentage: (end / cloudFile.size) * 100,
        speed: chunkSize / 0.1,
        remainingTime: ((cloudFile.size - end) / chunkSize) * 0.1
      };
      
      this.emit('downloadProgress', progress);
    }
    
    return new Blob(chunks);
  }

  /**
   * 同步项目
   */
  public async syncProject(projectId: string): Promise<void> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    if (!this.isOnline) {
      this.emit('syncSkipped', { reason: 'offline' });
      return;
    }

    const syncProgress: SyncProgress = {
      projectId,
      phase: 'scanning',
      filesProcessed: 0,
      totalFiles: project.files.length,
      bytesTransferred: 0,
      totalBytes: project.totalSize,
      conflicts: []
    };

    this.emit('syncStarted', syncProgress);

    try {
      // 扫描阶段
      syncProgress.phase = 'scanning';
      this.emit('syncProgress', syncProgress);
      await this.scanForChanges(project);

      // 上传阶段
      syncProgress.phase = 'uploading';
      this.emit('syncProgress', syncProgress);
      await this.uploadPendingFiles(project, syncProgress);

      // 下载阶段
      syncProgress.phase = 'downloading';
      this.emit('syncProgress', syncProgress);
      await this.downloadUpdatedFiles(project, syncProgress);

      // 冲突解决阶段
      if (syncProgress.conflicts.length > 0) {
        syncProgress.phase = 'resolving';
        this.emit('syncProgress', syncProgress);
        await this.resolveConflicts(project, syncProgress.conflicts);
      }

      // 完成
      syncProgress.phase = 'completed';
      project.lastSyncAt = Date.now();
      this.emit('syncCompleted', syncProgress);

    } catch (error) {
      this.emit('syncError', { projectId, error });
      throw error;
    }
  }

  /**
   * 扫描变更
   */
  private async scanForChanges(project: CloudProject): Promise<void> {
    // 模拟扫描过程
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 检查本地文件变更
    for (const file of project.files) {
      if (file.status === FileStatus.SYNCED) {
        // 检查是否有本地修改
        const hasLocalChanges = Math.random() > 0.8; // 20%概率有变更
        if (hasLocalChanges) {
          file.status = FileStatus.SYNCING;
          this.syncQueue.set(file.id, file);
        }
      }
    }
  }

  /**
   * 上传待同步文件
   */
  private async uploadPendingFiles(project: CloudProject, progress: SyncProgress): Promise<void> {
    const pendingFiles = Array.from(this.syncQueue.values());

    console.log(`开始上传项目 ${project.name} 的待同步文件`);

    for (const file of pendingFiles) {
      progress.currentFile = file.name;
      progress.filesProcessed++;
      this.emit('syncProgress', progress);
      
      // 模拟上传
      await new Promise(resolve => setTimeout(resolve, 200));
      
      file.status = FileStatus.SYNCED;
      file.lastSyncAt = Date.now();
      this.syncQueue.delete(file.id);
    }
  }

  /**
   * 下载更新文件
   */
  private async downloadUpdatedFiles(project: CloudProject, progress: SyncProgress): Promise<void> {
    // 模拟检查远程更新
    const hasRemoteUpdates = Math.random() > 0.7; // 30%概率有远程更新
    
    if (hasRemoteUpdates) {
      const updatedFile = project.files[Math.floor(Math.random() * project.files.length)];
      progress.currentFile = updatedFile.name;
      
      // 模拟下载
      await new Promise(resolve => setTimeout(resolve, 300));
      
      updatedFile.lastSyncAt = Date.now();
    }
  }

  /**
   * 解决冲突
   */
  private async resolveConflicts(project: CloudProject, conflicts: FileConflict[]): Promise<void> {
    for (const conflict of conflicts) {
      // 根据项目设置自动解决或等待手动解决
      if (project.syncSettings.conflictResolution === 'auto_local') {
        conflict.resolution = 'local';
        conflict.resolvedAt = Date.now();
      } else if (project.syncSettings.conflictResolution === 'auto_remote') {
        conflict.resolution = 'remote';
        conflict.resolvedAt = Date.now();
      } else {
        // 手动解决，发出事件等待用户处理
        this.emit('conflictDetected', conflict);
      }
    }
  }

  /**
   * 自动同步
   */
  private async autoSync(): Promise<void> {
    if (!this.activeProject || this.activeProject.syncSettings.strategy !== SyncStrategy.AUTO) {
      return;
    }

    const lastSync = this.activeProject.lastSyncAt || 0;
    const interval = this.activeProject.syncSettings.autoSyncInterval * 60 * 1000; // 转换为毫秒

    if (Date.now() - lastSync >= interval) {
      try {
        await this.syncProject(this.activeProject.id);
      } catch (error) {
        console.error('Auto sync failed:', error);
      }
    }
  }

  /**
   * 暂停同步
   */
  private pauseSync(): void {
    this.emit('syncPaused');
  }

  /**
   * 恢复同步
   */
  private resumeSync(): void {
    this.emit('syncResumed');
    if (this.activeProject) {
      this.syncProject(this.activeProject.id);
    }
  }

  /**
   * 计算文件哈希
   */
  private async calculateFileHash(file: File): Promise<string> {
    const buffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * 删除文件
   */
  public async deleteFile(fileId: string): Promise<void> {
    if (!this.activeProject) {
      throw new Error('No active project');
    }

    const fileIndex = this.activeProject.files.findIndex(f => f.id === fileId);
    if (fileIndex === -1) {
      throw new Error('File not found');
    }

    const file = this.activeProject.files[fileIndex];

    // 从云端删除
    await this.performDelete(file);

    // 从本地列表删除
    this.activeProject.files.splice(fileIndex, 1);
    this.activeProject.fileCount--;
    this.activeProject.totalSize -= file.size;
    this.activeProject.updatedAt = Date.now();

    this.emit('fileDeleted', file);
  }

  /**
   * 执行文件删除
   */
  private async performDelete(file: CloudFile): Promise<void> {
    // 模拟删除延迟
    await new Promise(resolve => setTimeout(resolve, 200));
    this.emit('fileDeleteProgress', { fileId: file.id, status: 'deleting' });
  }

  /**
   * 重命名文件
   */
  public async renameFile(fileId: string, newName: string): Promise<CloudFile> {
    if (!this.activeProject) {
      throw new Error('No active project');
    }

    const file = this.activeProject.files.find(f => f.id === fileId);
    if (!file) {
      throw new Error('File not found');
    }

    const oldName = file.name;
    file.name = newName;
    file.updatedAt = Date.now();

    // 添加到同步队列
    this.syncQueue.set(fileId, file);

    this.emit('fileRenamed', { file, oldName, newName });
    return file;
  }

  /**
   * 移动文件
   */
  public async moveFile(fileId: string, newPath: string): Promise<CloudFile> {
    if (!this.activeProject) {
      throw new Error('No active project');
    }

    const file = this.activeProject.files.find(f => f.id === fileId);
    if (!file) {
      throw new Error('File not found');
    }

    const oldPath = file.path;
    file.path = newPath;
    file.updatedAt = Date.now();

    // 添加到同步队列
    this.syncQueue.set(fileId, file);

    this.emit('fileMoved', { file, oldPath, newPath });
    return file;
  }

  /**
   * 获取文件版本历史
   */
  public getFileVersions(fileId: string): FileVersion[] {
    if (!this.activeProject) {
      throw new Error('No active project');
    }

    const file = this.activeProject.files.find(f => f.id === fileId);
    if (!file) {
      throw new Error('File not found');
    }

    return file.versions;
  }

  /**
   * 恢复文件版本
   */
  public async restoreFileVersion(fileId: string, versionId: string): Promise<CloudFile> {
    if (!this.activeProject) {
      throw new Error('No active project');
    }

    const file = this.activeProject.files.find(f => f.id === fileId);
    if (!file) {
      throw new Error('File not found');
    }

    const version = file.versions.find(v => v.id === versionId);
    if (!version) {
      throw new Error('Version not found');
    }

    // 创建新版本作为恢复点
    const newVersion: FileVersion = {
      id: `version_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      version: file.version + 1,
      size: version.size,
      hash: version.hash,
      createdAt: Date.now(),
      author: 'current_user',
      message: `Restored from version ${version.version}`,
      changes: [{
        type: 'update',
        path: file.path,
        size: version.size,
        timestamp: Date.now()
      }]
    };

    file.versions.push(newVersion);
    file.version = newVersion.version;
    file.size = version.size;
    file.hash = version.hash;
    file.updatedAt = Date.now();

    // 添加到同步队列
    this.syncQueue.set(fileId, file);

    this.emit('fileVersionRestored', { file, restoredVersion: version });
    return file;
  }

  /**
   * 压缩文件
   */
  public async compressFile(file: File, algorithm: CompressionAlgorithm): Promise<Blob> {
    if (algorithm === CompressionAlgorithm.NONE) {
      return file;
    }

    // 模拟压缩过程
    await new Promise(resolve => setTimeout(resolve, 500));

    // 模拟压缩效果（实际应该使用真实的压缩算法）
    const compressionRatio = this.getCompressionRatio(algorithm);
    const compressedSize = Math.floor(file.size * compressionRatio);

    // 创建模拟的压缩数据
    const compressedData = new ArrayBuffer(compressedSize);

    this.emit('fileCompressed', {
      originalSize: file.size,
      compressedSize,
      ratio: compressionRatio,
      algorithm
    });

    return new Blob([compressedData]);
  }

  /**
   * 获取压缩比率
   */
  private getCompressionRatio(algorithm: CompressionAlgorithm): number {
    switch (algorithm) {
      case CompressionAlgorithm.GZIP:
        return 0.7;
      case CompressionAlgorithm.BROTLI:
        return 0.65;
      case CompressionAlgorithm.LZ4:
        return 0.75;
      case CompressionAlgorithm.ZSTD:
        return 0.6;
      default:
        return 1.0;
    }
  }

  /**
   * 解压文件
   */
  public async decompressFile(compressedBlob: Blob, algorithm: CompressionAlgorithm): Promise<Blob> {
    if (algorithm === CompressionAlgorithm.NONE) {
      return compressedBlob;
    }

    // 模拟解压过程
    await new Promise(resolve => setTimeout(resolve, 300));

    // 模拟解压效果
    const compressionRatio = this.getCompressionRatio(algorithm);
    const originalSize = Math.floor(compressedBlob.size / compressionRatio);

    const decompressedData = new ArrayBuffer(originalSize);

    this.emit('fileDecompressed', {
      compressedSize: compressedBlob.size,
      originalSize,
      algorithm
    });

    return new Blob([decompressedData]);
  }

  /**
   * 获取存储使用情况
   */
  public getStorageUsage(): {
    totalSize: number;
    usedSize: number;
    availableSize: number;
    fileCount: number;
    projects: Array<{
      id: string;
      name: string;
      size: number;
      fileCount: number;
    }>;
  } {
    const projects = Array.from(this.projects.values());
    const totalUsed = projects.reduce((sum, p) => sum + p.totalSize, 0);
    const totalFiles = projects.reduce((sum, p) => sum + p.fileCount, 0);

    return {
      totalSize: 10 * 1024 * 1024 * 1024, // 10GB 限制
      usedSize: totalUsed,
      availableSize: (10 * 1024 * 1024 * 1024) - totalUsed,
      fileCount: totalFiles,
      projects: projects.map(p => ({
        id: p.id,
        name: p.name,
        size: p.totalSize,
        fileCount: p.fileCount
      }))
    };
  }

  /**
   * 清理临时文件
   */
  public async cleanupTempFiles(): Promise<void> {
    // 清理上传队列中的失败文件
    this.uploadQueue.clear();

    // 清理同步队列中的过期项目
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    for (const [fileId, file] of this.syncQueue.entries()) {
      if (now - file.updatedAt > maxAge) {
        this.syncQueue.delete(fileId);
      }
    }

    this.emit('tempFilesCleanedUp');
  }

  /**
   * 获取所有项目
   */
  public getAllProjects(): CloudProject[] {
    return Array.from(this.projects.values());
  }

  /**
   * 获取活动项目
   */
  public getActiveProject(): CloudProject | null {
    return this.activeProject;
  }

  /**
   * 获取项目文件
   */
  public getProjectFiles(projectId: string): CloudFile[] {
    const project = this.projects.get(projectId);
    return project ? project.files : [];
  }

  /**
   * 搜索文件
   */
  public searchFiles(query: string, filters?: {
    projectId?: string;
    mimeType?: string;
    author?: string;
    dateRange?: { start: number; end: number };
    sizeRange?: { min: number; max: number };
  }): CloudFile[] {
    let files: CloudFile[] = [];

    if (filters?.projectId) {
      const project = this.projects.get(filters.projectId);
      files = project ? project.files : [];
    } else {
      files = Array.from(this.projects.values()).flatMap(p => p.files);
    }

    return files.filter(file => {
      // 文本搜索
      const matchesQuery = !query ||
        file.name.toLowerCase().includes(query.toLowerCase()) ||
        file.path.toLowerCase().includes(query.toLowerCase()) ||
        file.metadata.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()));

      // 类型过滤
      const matchesMimeType = !filters?.mimeType || file.mimeType.includes(filters.mimeType);

      // 作者过滤
      const matchesAuthor = !filters?.author || file.metadata.author === filters.author;

      // 日期范围过滤
      const matchesDateRange = !filters?.dateRange ||
        (file.createdAt >= filters.dateRange.start && file.createdAt <= filters.dateRange.end);

      // 大小范围过滤
      const matchesSizeRange = !filters?.sizeRange ||
        (file.size >= filters.sizeRange.min && file.size <= filters.sizeRange.max);

      return matchesQuery && matchesMimeType && matchesAuthor && matchesDateRange && matchesSizeRange;
    });
  }

  /**
   * 获取网络状态
   */
  public isNetworkOnline(): boolean {
    return this.isOnline;
  }

  /**
   * 获取同步状态
   */
  public getSyncStatus(): {
    isActive: boolean;
    queueSize: number;
    lastSync?: number;
    nextSync?: number;
  } {
    const queueSize = this.syncQueue.size;
    const lastSync = this.activeProject?.lastSyncAt;

    let nextSync: number | undefined;
    if (this.activeProject && this.activeProject.syncSettings.strategy === SyncStrategy.AUTO) {
      const interval = this.activeProject.syncSettings.autoSyncInterval * 60 * 1000;
      nextSync = (lastSync || 0) + interval;
    }

    return {
      isActive: queueSize > 0,
      queueSize,
      lastSync,
      nextSync
    };
  }

  /**
   * 强制同步
   */
  public async forceSync(): Promise<void> {
    if (!this.activeProject) {
      throw new Error('No active project');
    }

    await this.syncProject(this.activeProject.id);
  }

  /**
   * 导出项目
   */
  public async exportProject(projectId: string): Promise<Blob> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    const exportData = {
      project: {
        ...project,
        files: project.files.map(f => ({
          ...f,
          // 不包含实际文件数据，只包含元数据
          data: null
        }))
      },
      exportedAt: Date.now(),
      version: '1.0.0'
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    return new Blob([jsonString], { type: 'application/json' });
  }

  /**
   * 导入项目
   */
  public async importProject(projectData: Blob): Promise<CloudProject> {
    const text = await projectData.text();
    const data = JSON.parse(text);

    if (!data.project || !data.version) {
      throw new Error('Invalid project data');
    }

    const project = data.project as CloudProject;
    project.id = `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    project.createdAt = Date.now();
    project.updatedAt = Date.now();

    this.projects.set(project.id, project);
    this.emit('projectImported', project);

    return project;
  }
}

export default CloudStorageService;
