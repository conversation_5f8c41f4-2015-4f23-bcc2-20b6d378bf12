#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 正确的版本映射
const versionFixes = {
  // NestJS 核心包
  '@nestjs/common': '^9.4.3',
  '@nestjs/core': '^9.4.3',
  '@nestjs/platform-express': '^9.4.3',
  '@nestjs/websockets': '^9.4.3',
  '@nestjs/platform-socket.io': '^9.4.3',
  '@nestjs/typeorm': '^9.0.1',
  '@nestjs/config': '^2.3.4',
  '@nestjs/schedule': '^2.2.3',
  '@nestjs/swagger': '^6.3.0',
  '@nestjs/microservices': '^9.4.3',
  '@nestjs/event-emitter': '^1.4.3',
  
  // NestJS 开发依赖
  '@nestjs/cli': '^9.5.0',
  '@nestjs/schematics': '^9.2.0',
  '@nestjs/testing': '^9.4.3',
  
  // 移除不存在的包
  'tensorflow': null, // 应该使用 @tensorflow/tfjs
  'pytorch': null,   // Node.js 中不存在
  'scikit-learn': null, // Python 包，Node.js 中不存在
  'numpy': null,     // Python 包，Node.js 中不存在
  'pandas': null,    // Python 包，Node.js 中不存在
  'grpc': null,      // 已弃用，使用 @grpc/grpc-js
  
  // 修复不存在的包
  'or-tools': null,
  'genetic-algorithm': null,
  'simulated-annealing': null,
  'particle-swarm-optimization': null,
  'linear-programming': null,
  'constraint-programming': null,
  'graph-theory': null,
  'optimization-algorithms': null,
  'onnxjs': null,
  'tensorrt': null,
  'openvino': null,
  'edge-impulse': null,
  'tflite': null,
  'quantization': null,
  'api-gateway': null,
  'oauth2-server': null,
  'webhook': null,
  'marketplace': null,
  'partner-management': null,
  'certification': null,
  'standards-compliance': null,
  'crypto': null, // Node.js 内置模块
};

// 查找所有 package.json 文件
function findPackageJsonFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          traverse(fullPath);
        } else if (item === 'package.json') {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`无法读取目录 ${currentDir}:`, error.message);
    }
  }
  
  traverse(dir);
  return files;
}

// 修复单个 package.json 文件
function fixPackageJson(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const packageJson = JSON.parse(content);
    let modified = false;
    
    // 修复 dependencies
    if (packageJson.dependencies) {
      for (const [pkg, correctVersion] of Object.entries(versionFixes)) {
        if (packageJson.dependencies[pkg]) {
          if (correctVersion === null) {
            // 删除不存在的包
            delete packageJson.dependencies[pkg];
            modified = true;
            console.log(`  - 删除不存在的依赖: ${pkg}`);
          } else if (packageJson.dependencies[pkg] !== correctVersion) {
            // 更新版本
            const oldVersion = packageJson.dependencies[pkg];
            packageJson.dependencies[pkg] = correctVersion;
            modified = true;
            console.log(`  - 更新 ${pkg}: ${oldVersion} -> ${correctVersion}`);
          }
        }
      }
    }
    
    // 修复 devDependencies
    if (packageJson.devDependencies) {
      for (const [pkg, correctVersion] of Object.entries(versionFixes)) {
        if (packageJson.devDependencies[pkg]) {
          if (correctVersion === null) {
            // 删除不存在的包
            delete packageJson.devDependencies[pkg];
            modified = true;
            console.log(`  - 删除不存在的开发依赖: ${pkg}`);
          } else if (packageJson.devDependencies[pkg] !== correctVersion) {
            // 更新版本
            const oldVersion = packageJson.devDependencies[pkg];
            packageJson.devDependencies[pkg] = correctVersion;
            modified = true;
            console.log(`  - 更新开发依赖 ${pkg}: ${oldVersion} -> ${correctVersion}`);
          }
        }
      }
    }
    
    if (modified) {
      // 写回文件，保持格式
      fs.writeFileSync(filePath, JSON.stringify(packageJson, null, 2) + '\n');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`修复 ${filePath} 时出错:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  console.log('🔧 开始批量修复 package.json 版本问题...\n');
  
  const serverDir = path.join(__dirname, 'server');
  
  if (!fs.existsSync(serverDir)) {
    console.error('server 目录不存在');
    process.exit(1);
  }
  
  const packageFiles = findPackageJsonFiles(serverDir);
  console.log(`📦 找到 ${packageFiles.length} 个 package.json 文件\n`);
  
  let fixedCount = 0;
  
  packageFiles.forEach((file, index) => {
    const relativePath = path.relative(__dirname, file);
    console.log(`[${index + 1}/${packageFiles.length}] 检查 ${relativePath}:`);
    
    if (fixPackageJson(file)) {
      console.log(`  ✅ 已修复\n`);
      fixedCount++;
    } else {
      console.log(`  ⏭️  无需修复\n`);
    }
  });
  
  console.log(`📊 修复完成！`);
  console.log(`总文件数: ${packageFiles.length}`);
  console.log(`已修复: ${fixedCount}`);
  console.log(`无需修复: ${packageFiles.length - fixedCount}`);
  
  if (fixedCount > 0) {
    console.log('\n💡 建议：');
    console.log('1. 删除所有 node_modules 目录');
    console.log('2. 删除所有 package-lock.json 文件');
    console.log('3. 重新运行 npm install');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { fixPackageJson, findPackageJsonFiles };
