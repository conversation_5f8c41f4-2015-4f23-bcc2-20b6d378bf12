/**
 * RAG应用系统节点集合
 * 提供检索增强生成、知识库管理、智能问答等功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3 } from 'three';

/**
 * 文档类型枚举
 */
export enum DocumentType {
  TEXT = 'text',
  PDF = 'pdf',
  WORD = 'word',
  MARKDOWN = 'markdown',
  HTML = 'html',
  JSON = 'json',
  CSV = 'csv',
  XML = 'xml',
  CODE = 'code',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video'
}

/**
 * 向量化模型枚举
 */
export enum EmbeddingModel {
  OPENAI_ADA_002 = 'text-embedding-ada-002',
  OPENAI_3_SMALL = 'text-embedding-3-small',
  OPENAI_3_LARGE = 'text-embedding-3-large',
  SENTENCE_TRANSFORMERS = 'sentence-transformers',
  COHERE_EMBED = 'cohere-embed',
  HUGGINGFACE_BGE = 'bge-large-zh',
  CUSTOM = 'custom'
}

/**
 * 检索策略枚举
 */
export enum RetrievalStrategy {
  SIMILARITY = 'similarity',
  KEYWORD = 'keyword',
  HYBRID = 'hybrid',
  SEMANTIC = 'semantic',
  CONTEXTUAL = 'contextual',
  MULTI_QUERY = 'multi_query',
  PARENT_DOCUMENT = 'parent_document'
}

/**
 * 文档接口
 */
export interface Document {
  id: string;
  title: string;
  content: string;
  type: DocumentType;
  metadata: DocumentMetadata;
  chunks: DocumentChunk[];
  embedding?: number[];
  createdAt: number;
  updatedAt: number;
}

/**
 * 文档元数据接口
 */
export interface DocumentMetadata {
  source: string;
  author?: string;
  tags: string[];
  category: string;
  language: string;
  size: number;
  checksum: string;
  version: string;
  customFields: { [key: string]: any };
}

/**
 * 文档块接口
 */
export interface DocumentChunk {
  id: string;
  documentId: string;
  content: string;
  startIndex: number;
  endIndex: number;
  embedding?: number[];
  metadata: ChunkMetadata;
}

/**
 * 块元数据接口
 */
export interface ChunkMetadata {
  chunkIndex: number;
  tokenCount: number;
  characterCount: number;
  section?: string;
  heading?: string;
  pageNumber?: number;
  confidence: number;
}

/**
 * 检索结果接口
 */
export interface RetrievalResult {
  chunk: DocumentChunk;
  document: Document;
  score: number;
  relevanceScore: number;
  contextScore: number;
  explanation?: string;
}

/**
 * RAG查询接口
 */
export interface RAGQuery {
  id: string;
  question: string;
  context?: string;
  filters?: QueryFilters;
  retrievalConfig: RetrievalConfig;
  generationConfig: GenerationConfig;
  timestamp: number;
}

/**
 * 查询过滤器接口
 */
export interface QueryFilters {
  documentTypes?: DocumentType[];
  categories?: string[];
  tags?: string[];
  dateRange?: { start: number; end: number };
  authors?: string[];
  languages?: string[];
  customFilters?: { [key: string]: any };
}

/**
 * 检索配置接口
 */
export interface RetrievalConfig {
  strategy: RetrievalStrategy;
  topK: number;
  scoreThreshold: number;
  maxTokens: number;
  embeddingModel: EmbeddingModel;
  rerankModel?: string;
  diversityPenalty: number;
  contextWindow: number;
}

/**
 * 生成配置接口
 */
export interface GenerationConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
  systemPrompt?: string;
  userPrompt?: string;
  stopSequences?: string[];
}

/**
 * RAG响应接口
 */
export interface RAGResponse {
  id: string;
  queryId: string;
  answer: string;
  sources: RetrievalResult[];
  confidence: number;
  reasoning?: string;
  metadata: ResponseMetadata;
  timestamp: number;
}

/**
 * 响应元数据接口
 */
export interface ResponseMetadata {
  processingTime: number;
  tokensUsed: number;
  retrievalTime: number;
  generationTime: number;
  sourcesCount: number;
  model: string;
  strategy: RetrievalStrategy;
}

/**
 * 知识库接口
 */
export interface KnowledgeBase {
  id: string;
  name: string;
  description: string;
  documents: Map<string, Document>;
  vectorIndex: VectorIndex;
  config: KnowledgeBaseConfig;
  statistics: KnowledgeBaseStats;
  createdAt: number;
  updatedAt: number;
}

/**
 * 向量索引接口
 */
export interface VectorIndex {
  id: string;
  dimension: number;
  indexType: 'flat' | 'ivf' | 'hnsw' | 'annoy';
  vectors: Map<string, number[]>;
  metadata: Map<string, any>;
  buildTime: number;
}

/**
 * 知识库配置接口
 */
export interface KnowledgeBaseConfig {
  embeddingModel: EmbeddingModel;
  chunkSize: number;
  chunkOverlap: number;
  indexType: string;
  autoUpdate: boolean;
  versionControl: boolean;
  accessControl: boolean;
}

/**
 * 知识库统计接口
 */
export interface KnowledgeBaseStats {
  documentCount: number;
  chunkCount: number;
  totalTokens: number;
  totalSize: number;
  averageChunkSize: number;
  languageDistribution: { [language: string]: number };
  typeDistribution: { [type: string]: number };
  lastIndexed: number;
}

/**
 * 高级RAG应用管理器
 */
class AdvancedRAGManager {
  private knowledgeBases: Map<string, KnowledgeBase> = new Map();
  private documents: Map<string, Document> = new Map();
  private queries: Map<string, RAGQuery> = new Map();
  private responses: Map<string, RAGResponse> = new Map();
  private embeddingCache: Map<string, number[]> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 创建知识库
   */
  createKnowledgeBase(name: string, description: string, config: KnowledgeBaseConfig): KnowledgeBase {
    const kb: KnowledgeBase = {
      id: this.generateKnowledgeBaseId(),
      name,
      description,
      documents: new Map(),
      vectorIndex: this.createVectorIndex(config),
      config,
      statistics: this.initializeStats(),
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    this.knowledgeBases.set(kb.id, kb);
    this.emit('knowledgeBaseCreated', { knowledgeBase: kb });

    Debug.log('AdvancedRAGManager', `知识库创建: ${kb.id} - ${name}`);
    return kb;
  }

  /**
   * 添加文档到知识库
   */
  async addDocument(knowledgeBaseId: string, title: string, content: string, type: DocumentType, metadata: Partial<DocumentMetadata>): Promise<Document> {
    const kb = this.knowledgeBases.get(knowledgeBaseId);
    if (!kb) {
      throw new Error('知识库不存在');
    }

    const document: Document = {
      id: this.generateDocumentId(),
      title,
      content,
      type,
      metadata: this.completeMetadata(metadata, content),
      chunks: [],
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    // 分块处理
    document.chunks = await this.chunkDocument(document, kb.config);

    // 生成向量嵌入
    await this.generateEmbeddings(document, kb.config.embeddingModel);

    // 添加到知识库
    kb.documents.set(document.id, document);
    this.documents.set(document.id, document);

    // 更新向量索引
    await this.updateVectorIndex(kb, document);

    // 更新统计信息
    this.updateKnowledgeBaseStats(kb);

    this.emit('documentAdded', { knowledgeBaseId, document });

    Debug.log('AdvancedRAGManager', `文档添加: ${document.id} 到知识库 ${knowledgeBaseId}`);
    return document;
  }

  /**
   * 执行RAG查询
   */
  async executeRAGQuery(knowledgeBaseId: string, question: string, retrievalConfig: RetrievalConfig, generationConfig: GenerationConfig, filters?: QueryFilters): Promise<RAGResponse> {
    const startTime = Date.now();
    
    const query: RAGQuery = {
      id: this.generateQueryId(),
      question,
      filters,
      retrievalConfig,
      generationConfig,
      timestamp: startTime
    };

    this.queries.set(query.id, query);

    try {
      // 检索阶段
      const retrievalStart = Date.now();
      const retrievalResults = await this.retrieveRelevantChunks(knowledgeBaseId, question, retrievalConfig, filters);
      const retrievalTime = Date.now() - retrievalStart;

      // 生成阶段
      const generationStart = Date.now();
      const answer = await this.generateAnswer(question, retrievalResults, generationConfig);
      const generationTime = Date.now() - generationStart;

      // 创建响应
      const response: RAGResponse = {
        id: this.generateResponseId(),
        queryId: query.id,
        answer,
        sources: retrievalResults,
        confidence: this.calculateConfidence(retrievalResults, answer),
        metadata: {
          processingTime: Date.now() - startTime,
          tokensUsed: this.estimateTokens(question + answer),
          retrievalTime,
          generationTime,
          sourcesCount: retrievalResults.length,
          model: generationConfig.model,
          strategy: retrievalConfig.strategy
        },
        timestamp: Date.now()
      };

      this.responses.set(response.id, response);
      this.emit('ragQueryExecuted', { query, response });

      Debug.log('AdvancedRAGManager', `RAG查询完成: ${query.id}, 耗时${response.metadata.processingTime}ms`);
      return response;

    } catch (error) {
      Debug.error('AdvancedRAGManager', 'RAG查询失败', error);
      throw error;
    }
  }

  /**
   * 检索相关文档块
   */
  private async retrieveRelevantChunks(knowledgeBaseId: string, question: string, config: RetrievalConfig, filters?: QueryFilters): Promise<RetrievalResult[]> {
    const kb = this.knowledgeBases.get(knowledgeBaseId);
    if (!kb) {
      throw new Error('知识库不存在');
    }

    // 生成查询向量
    const queryEmbedding = await this.generateQueryEmbedding(question, config.embeddingModel);

    // 获取候选文档块
    const candidates = this.getCandidateChunks(kb, filters);

    // 计算相似度分数
    const scoredResults: RetrievalResult[] = [];

    for (const chunk of candidates) {
      if (!chunk.embedding) continue;

      const similarity = this.calculateCosineSimilarity(queryEmbedding, chunk.embedding);
      
      if (similarity >= config.scoreThreshold) {
        const document = this.documents.get(chunk.documentId);
        if (document) {
          scoredResults.push({
            chunk,
            document,
            score: similarity,
            relevanceScore: similarity,
            contextScore: this.calculateContextScore(chunk, question)
          });
        }
      }
    }

    // 排序和截取
    scoredResults.sort((a, b) => b.score - a.score);
    const topResults = scoredResults.slice(0, config.topK);

    // 应用多样性惩罚
    if (config.diversityPenalty > 0) {
      return this.applyDiversityPenalty(topResults, config.diversityPenalty);
    }

    return topResults;
  }

  /**
   * 生成答案
   */
  private async generateAnswer(question: string, sources: RetrievalResult[], config: GenerationConfig): Promise<string> {
    // 构建上下文
    const context = sources.map(result => result.chunk.content).join('\n\n');
    
    // 构建提示词
    const systemPrompt = config.systemPrompt || '你是一个有用的AI助手，基于提供的上下文回答用户问题。';
    const userPrompt = config.userPrompt || `上下文：\n${context}\n\n问题：${question}\n\n请基于上下文回答问题：`;

    // 模拟LLM调用（实际应用中需要调用真实的LLM API）
    const answer = await this.callLLM(systemPrompt, userPrompt, config);

    return answer;
  }

  /**
   * 模拟LLM调用
   */
  private async callLLM(systemPrompt: string, userPrompt: string, config: GenerationConfig): Promise<string> {
    // 这里是模拟实现，实际应用中需要调用真实的LLM API
    await new Promise(resolve => setTimeout(resolve, 100)); // 模拟网络延迟
    
    return `基于提供的上下文，我可以回答您的问题。这是一个模拟的AI回答，实际应用中会调用真实的语言模型API来生成更准确的回答。`;
  }

  /**
   * 文档分块
   */
  private async chunkDocument(document: Document, config: KnowledgeBaseConfig): Promise<DocumentChunk[]> {
    const chunks: DocumentChunk[] = [];
    const content = document.content;
    const chunkSize = config.chunkSize;
    const overlap = config.chunkOverlap;

    let startIndex = 0;
    let chunkIndex = 0;

    while (startIndex < content.length) {
      const endIndex = Math.min(startIndex + chunkSize, content.length);
      const chunkContent = content.slice(startIndex, endIndex);

      const chunk: DocumentChunk = {
        id: this.generateChunkId(),
        documentId: document.id,
        content: chunkContent,
        startIndex,
        endIndex,
        metadata: {
          chunkIndex,
          tokenCount: this.estimateTokens(chunkContent),
          characterCount: chunkContent.length,
          confidence: 1.0
        }
      };

      chunks.push(chunk);
      chunkIndex++;
      startIndex = endIndex - overlap;
    }

    return chunks;
  }

  /**
   * 生成嵌入向量
   */
  private async generateEmbeddings(document: Document, model: EmbeddingModel): Promise<void> {
    // 为文档生成嵌入
    document.embedding = await this.getEmbedding(document.content, model);

    // 为每个块生成嵌入
    for (const chunk of document.chunks) {
      chunk.embedding = await this.getEmbedding(chunk.content, model);
    }
  }

  /**
   * 获取嵌入向量
   */
  private async getEmbedding(text: string, model: EmbeddingModel): Promise<number[]> {
    // 检查缓存
    const cacheKey = `${model}:${this.hashText(text)}`;
    if (this.embeddingCache.has(cacheKey)) {
      return this.embeddingCache.get(cacheKey)!;
    }

    // 模拟嵌入生成（实际应用中需要调用真实的嵌入API）
    const embedding = this.generateMockEmbedding(text, this.getEmbeddingDimension(model));
    
    // 缓存结果
    this.embeddingCache.set(cacheKey, embedding);
    
    return embedding;
  }

  /**
   * 生成模拟嵌入向量
   */
  private generateMockEmbedding(text: string, dimension: number): number[] {
    const embedding = new Array(dimension);
    const hash = this.hashText(text);
    
    for (let i = 0; i < dimension; i++) {
      // 基于文本哈希生成确定性的向量
      embedding[i] = Math.sin(hash + i) * 0.5;
    }
    
    // 归一化
    const norm = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / norm);
  }

  /**
   * 计算余弦相似度
   */
  private calculateCosineSimilarity(vec1: number[], vec2: number[]): number {
    if (vec1.length !== vec2.length) return 0;
    
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;
    
    for (let i = 0; i < vec1.length; i++) {
      dotProduct += vec1[i] * vec2[i];
      norm1 += vec1[i] * vec1[i];
      norm2 += vec2[i] * vec2[i];
    }
    
    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  /**
   * 计算上下文分数
   */
  private calculateContextScore(chunk: DocumentChunk, question: string): number {
    // 简化的上下文分数计算
    const questionWords = question.toLowerCase().split(/\s+/);
    const chunkWords = chunk.content.toLowerCase().split(/\s+/);
    
    let matches = 0;
    for (const word of questionWords) {
      if (chunkWords.includes(word)) {
        matches++;
      }
    }
    
    return matches / questionWords.length;
  }

  /**
   * 应用多样性惩罚
   */
  private applyDiversityPenalty(results: RetrievalResult[], penalty: number): RetrievalResult[] {
    // 简化的多样性惩罚实现
    const diverseResults: RetrievalResult[] = [];
    const usedDocuments = new Set<string>();
    
    for (const result of results) {
      if (!usedDocuments.has(result.document.id)) {
        diverseResults.push(result);
        usedDocuments.add(result.document.id);
      } else {
        // 应用惩罚
        result.score *= (1 - penalty);
        diverseResults.push(result);
      }
    }
    
    return diverseResults.sort((a, b) => b.score - a.score);
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(sources: RetrievalResult[], answer: string): number {
    if (sources.length === 0) return 0;
    
    const avgScore = sources.reduce((sum, result) => sum + result.score, 0) / sources.length;
    const answerLength = answer.length;
    const lengthFactor = Math.min(answerLength / 100, 1); // 答案长度因子
    
    return avgScore * lengthFactor;
  }

  // 辅助方法
  private createVectorIndex(config: KnowledgeBaseConfig): VectorIndex {
    return {
      id: this.generateIndexId(),
      dimension: this.getEmbeddingDimension(config.embeddingModel),
      indexType: 'flat',
      vectors: new Map(),
      metadata: new Map(),
      buildTime: Date.now()
    };
  }

  private initializeStats(): KnowledgeBaseStats {
    return {
      documentCount: 0,
      chunkCount: 0,
      totalTokens: 0,
      totalSize: 0,
      averageChunkSize: 0,
      languageDistribution: {},
      typeDistribution: {},
      lastIndexed: Date.now()
    };
  }

  private completeMetadata(partial: Partial<DocumentMetadata>, content: string): DocumentMetadata {
    return {
      source: partial.source || 'unknown',
      author: partial.author,
      tags: partial.tags || [],
      category: partial.category || 'general',
      language: partial.language || 'zh',
      size: content.length,
      checksum: this.hashText(content).toString(),
      version: partial.version || '1.0',
      customFields: partial.customFields || {}
    };
  }

  private async updateVectorIndex(kb: KnowledgeBase, document: Document): Promise<void> {
    // 添加文档和块的向量到索引
    if (document.embedding) {
      kb.vectorIndex.vectors.set(document.id, document.embedding);
    }
    
    for (const chunk of document.chunks) {
      if (chunk.embedding) {
        kb.vectorIndex.vectors.set(chunk.id, chunk.embedding);
      }
    }
  }

  private updateKnowledgeBaseStats(kb: KnowledgeBase): void {
    const stats = kb.statistics;
    stats.documentCount = kb.documents.size;
    stats.chunkCount = Array.from(kb.documents.values()).reduce((sum, doc) => sum + doc.chunks.length, 0);
    stats.totalTokens = Array.from(kb.documents.values()).reduce((sum, doc) => 
      sum + doc.chunks.reduce((chunkSum, chunk) => chunkSum + chunk.metadata.tokenCount, 0), 0);
    stats.totalSize = Array.from(kb.documents.values()).reduce((sum, doc) => sum + doc.metadata.size, 0);
    stats.averageChunkSize = stats.chunkCount > 0 ? stats.totalTokens / stats.chunkCount : 0;
    stats.lastIndexed = Date.now();
    
    kb.updatedAt = Date.now();
  }

  private getCandidateChunks(kb: KnowledgeBase, filters?: QueryFilters): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];
    
    for (const document of kb.documents.values()) {
      if (this.documentMatchesFilters(document, filters)) {
        chunks.push(...document.chunks);
      }
    }
    
    return chunks;
  }

  private documentMatchesFilters(document: Document, filters?: QueryFilters): boolean {
    if (!filters) return true;
    
    if (filters.documentTypes && !filters.documentTypes.includes(document.type)) {
      return false;
    }
    
    if (filters.categories && !filters.categories.includes(document.metadata.category)) {
      return false;
    }
    
    if (filters.tags && !filters.tags.some(tag => document.metadata.tags.includes(tag))) {
      return false;
    }
    
    return true;
  }

  private async generateQueryEmbedding(question: string, model: EmbeddingModel): Promise<number[]> {
    return await this.getEmbedding(question, model);
  }

  private getEmbeddingDimension(model: EmbeddingModel): number {
    const dimensions: { [key in EmbeddingModel]: number } = {
      [EmbeddingModel.OPENAI_ADA_002]: 1536,
      [EmbeddingModel.OPENAI_3_SMALL]: 1536,
      [EmbeddingModel.OPENAI_3_LARGE]: 3072,
      [EmbeddingModel.SENTENCE_TRANSFORMERS]: 768,
      [EmbeddingModel.COHERE_EMBED]: 1024,
      [EmbeddingModel.HUGGINGFACE_BGE]: 1024,
      [EmbeddingModel.CUSTOM]: 512
    };
    return dimensions[model];
  }

  private estimateTokens(text: string): number {
    // 简化的token估算：大约4个字符=1个token
    return Math.ceil(text.length / 4);
  }

  private hashText(text: string): number {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  // ID生成方法
  private generateKnowledgeBaseId(): string {
    return 'kb_' + Math.random().toString(36).substr(2, 9);
  }

  private generateDocumentId(): string {
    return 'doc_' + Math.random().toString(36).substr(2, 9);
  }

  private generateChunkId(): string {
    return 'chunk_' + Math.random().toString(36).substr(2, 9);
  }

  private generateQueryId(): string {
    return 'query_' + Math.random().toString(36).substr(2, 9);
  }

  private generateResponseId(): string {
    return 'response_' + Math.random().toString(36).substr(2, 9);
  }

  private generateIndexId(): string {
    return 'index_' + Math.random().toString(36).substr(2, 9);
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('AdvancedRAGManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 获取知识库
   */
  getKnowledgeBase(id: string): KnowledgeBase | undefined {
    return this.knowledgeBases.get(id);
  }

  /**
   * 获取文档
   */
  getDocument(id: string): Document | undefined {
    return this.documents.get(id);
  }

  /**
   * 获取查询
   */
  getQuery(id: string): RAGQuery | undefined {
    return this.queries.get(id);
  }

  /**
   * 获取响应
   */
  getResponse(id: string): RAGResponse | undefined {
    return this.responses.get(id);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.knowledgeBases.clear();
    this.documents.clear();
    this.queries.clear();
    this.responses.clear();
    this.embeddingCache.clear();
    this.eventListeners.clear();
  }
}

/**
 * 知识库管理节点
 */
export class KnowledgeBaseNode extends VisualScriptNode {
  public static readonly TYPE = 'KnowledgeBase';
  public static readonly NAME = '知识库管理';
  public static readonly DESCRIPTION = '创建和管理RAG知识库';

  private static ragManager: AdvancedRAGManager = new AdvancedRAGManager();

  constructor(nodeType: string = KnowledgeBaseNode.TYPE, name: string = KnowledgeBaseNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建知识库');
    this.addInput('addDocument', 'trigger', '添加文档');
    this.addInput('name', 'string', '知识库名称');
    this.addInput('description', 'string', '描述');
    this.addInput('knowledgeBaseId', 'string', '知识库ID');
    this.addInput('documentTitle', 'string', '文档标题');
    this.addInput('documentContent', 'string', '文档内容');
    this.addInput('documentType', 'string', '文档类型');
    this.addInput('embeddingModel', 'string', '嵌入模型');
    this.addInput('chunkSize', 'number', '分块大小');
    this.addInput('chunkOverlap', 'number', '分块重叠');

    // 输出端口
    this.addOutput('knowledgeBase', 'object', '知识库');
    this.addOutput('knowledgeBaseId', 'string', '知识库ID');
    this.addOutput('document', 'object', '文档');
    this.addOutput('documentId', 'string', '文档ID');
    this.addOutput('documentCount', 'number', '文档数量');
    this.addOutput('chunkCount', 'number', '文档块数量');
    this.addOutput('statistics', 'object', '统计信息');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onDocumentAdded', 'trigger', '文档添加完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const createTrigger = inputs?.create;
      const addDocumentTrigger = inputs?.addDocument;

      if (createTrigger) {
        return await this.createKnowledgeBase(inputs);
      } else if (addDocumentTrigger) {
        return await this.addDocument(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('KnowledgeBaseNode', '知识库操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private async createKnowledgeBase(inputs: any): Promise<any> {
    const name = inputs?.name as string || 'New Knowledge Base';
    const description = inputs?.description as string || '';
    const embeddingModel = inputs?.embeddingModel as string || 'text-embedding-ada-002';
    const chunkSize = inputs?.chunkSize as number || 1000;
    const chunkOverlap = inputs?.chunkOverlap as number || 200;

    const config: KnowledgeBaseConfig = {
      embeddingModel: embeddingModel as EmbeddingModel,
      chunkSize: Math.max(100, Math.min(4000, chunkSize)),
      chunkOverlap: Math.max(0, Math.min(chunkSize / 2, chunkOverlap)),
      indexType: 'flat',
      autoUpdate: true,
      versionControl: false,
      accessControl: false
    };

    const knowledgeBase = KnowledgeBaseNode.ragManager.createKnowledgeBase(name, description, config);

    Debug.log('KnowledgeBaseNode', `知识库创建成功: ${knowledgeBase.id}`);

    return {
      knowledgeBase,
      knowledgeBaseId: knowledgeBase.id,
      document: null,
      documentId: '',
      documentCount: knowledgeBase.statistics.documentCount,
      chunkCount: knowledgeBase.statistics.chunkCount,
      statistics: knowledgeBase.statistics,
      onCreated: true,
      onDocumentAdded: false,
      onError: false
    };
  }

  private async addDocument(inputs: any): Promise<any> {
    const knowledgeBaseId = inputs?.knowledgeBaseId as string;
    const documentTitle = inputs?.documentTitle as string || 'Untitled Document';
    const documentContent = inputs?.documentContent as string;
    const documentType = inputs?.documentType as string || 'text';

    if (!knowledgeBaseId || !documentContent) {
      throw new Error('未提供知识库ID或文档内容');
    }

    const metadata: Partial<DocumentMetadata> = {
      source: 'user_input',
      category: 'general',
      tags: [],
      language: 'zh'
    };

    const document = await KnowledgeBaseNode.ragManager.addDocument(
      knowledgeBaseId,
      documentTitle,
      documentContent,
      documentType as DocumentType,
      metadata
    );

    const knowledgeBase = KnowledgeBaseNode.ragManager.getKnowledgeBase(knowledgeBaseId);

    Debug.log('KnowledgeBaseNode', `文档添加成功: ${document.id}`);

    return {
      knowledgeBase,
      knowledgeBaseId,
      document,
      documentId: document.id,
      documentCount: knowledgeBase?.statistics.documentCount || 0,
      chunkCount: knowledgeBase?.statistics.chunkCount || 0,
      statistics: knowledgeBase?.statistics,
      onCreated: false,
      onDocumentAdded: true,
      onError: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      knowledgeBase: null,
      knowledgeBaseId: '',
      document: null,
      documentId: '',
      documentCount: 0,
      chunkCount: 0,
      statistics: null,
      onCreated: false,
      onDocumentAdded: false,
      onError: false
    };
  }
}

/**
 * RAG查询节点
 */
export class RAGQueryNode extends VisualScriptNode {
  public static readonly TYPE = 'RAGQuery';
  public static readonly NAME = 'RAG查询';
  public static readonly DESCRIPTION = '执行检索增强生成查询';

  private static ragManager: AdvancedRAGManager = new AdvancedRAGManager();

  constructor(nodeType: string = RAGQueryNode.TYPE, name: string = RAGQueryNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('query', 'trigger', '执行查询');
    this.addInput('knowledgeBaseId', 'string', '知识库ID');
    this.addInput('question', 'string', '问题');
    this.addInput('topK', 'number', '检索数量');
    this.addInput('scoreThreshold', 'number', '分数阈值');
    this.addInput('retrievalStrategy', 'string', '检索策略');
    this.addInput('generationModel', 'string', '生成模型');
    this.addInput('temperature', 'number', '温度参数');
    this.addInput('maxTokens', 'number', '最大Token数');
    this.addInput('systemPrompt', 'string', '系统提示词');

    // 输出端口
    this.addOutput('response', 'object', 'RAG响应');
    this.addOutput('answer', 'string', '答案');
    this.addOutput('sources', 'array', '来源文档');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('processingTime', 'number', '处理时间');
    this.addOutput('tokensUsed', 'number', '使用Token数');
    this.addOutput('sourcesCount', 'number', '来源数量');
    this.addOutput('onQueryCompleted', 'trigger', '查询完成');
    this.addOutput('onError', 'trigger', '查询失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const queryTrigger = inputs?.query;
      if (!queryTrigger) {
        return this.getDefaultOutputs();
      }

      const knowledgeBaseId = inputs?.knowledgeBaseId as string;
      const question = inputs?.question as string;

      if (!knowledgeBaseId || !question) {
        throw new Error('未提供知识库ID或问题');
      }

      const topK = inputs?.topK as number || 5;
      const scoreThreshold = inputs?.scoreThreshold as number || 0.7;
      const retrievalStrategy = inputs?.retrievalStrategy as string || 'similarity';
      const generationModel = inputs?.generationModel as string || 'gpt-3.5-turbo';
      const temperature = inputs?.temperature as number || 0.7;
      const maxTokens = inputs?.maxTokens as number || 1000;
      const systemPrompt = inputs?.systemPrompt as string;

      // 构建检索配置
      const retrievalConfig: RetrievalConfig = {
        strategy: retrievalStrategy as RetrievalStrategy,
        topK: Math.max(1, Math.min(20, topK)),
        scoreThreshold: Math.max(0, Math.min(1, scoreThreshold)),
        maxTokens: Math.max(100, Math.min(4000, maxTokens)),
        embeddingModel: EmbeddingModel.OPENAI_ADA_002,
        diversityPenalty: 0.1,
        contextWindow: 4000
      };

      // 构建生成配置
      const generationConfig: GenerationConfig = {
        model: generationModel,
        temperature: Math.max(0, Math.min(2, temperature)),
        maxTokens: Math.max(50, Math.min(2000, maxTokens)),
        topP: 0.9,
        frequencyPenalty: 0,
        presencePenalty: 0,
        systemPrompt,
        stopSequences: []
      };

      // 执行RAG查询
      const response = await RAGQueryNode.ragManager.executeRAGQuery(
        knowledgeBaseId,
        question,
        retrievalConfig,
        generationConfig
      );

      Debug.log('RAGQueryNode', `RAG查询完成: ${response.id}, 置信度=${response.confidence.toFixed(2)}`);

      return {
        response,
        answer: response.answer,
        sources: response.sources,
        confidence: response.confidence,
        processingTime: response.metadata.processingTime,
        tokensUsed: response.metadata.tokensUsed,
        sourcesCount: response.metadata.sourcesCount,
        onQueryCompleted: true,
        onError: false
      };

    } catch (error) {
      Debug.error('RAGQueryNode', 'RAG查询失败', error);
      return {
        response: null,
        answer: '',
        sources: [],
        confidence: 0,
        processingTime: 0,
        tokensUsed: 0,
        sourcesCount: 0,
        onQueryCompleted: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      response: null,
      answer: '',
      sources: [],
      confidence: 0,
      processingTime: 0,
      tokensUsed: 0,
      sourcesCount: 0,
      onQueryCompleted: false,
      onError: false
    };
  }
}

/**
 * 文档处理节点
 */
export class DocumentProcessingNode extends VisualScriptNode {
  public static readonly TYPE = 'DocumentProcessing';
  public static readonly NAME = '文档处理';
  public static readonly DESCRIPTION = '处理和预处理各种格式的文档';

  private static ragManager: AdvancedRAGManager = new AdvancedRAGManager();

  constructor(nodeType: string = DocumentProcessingNode.TYPE, name: string = DocumentProcessingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('process', 'trigger', '处理文档');
    this.addInput('documentContent', 'string', '文档内容');
    this.addInput('documentType', 'string', '文档类型');
    this.addInput('processingOptions', 'object', '处理选项');
    this.addInput('chunkSize', 'number', '分块大小');
    this.addInput('chunkOverlap', 'number', '分块重叠');
    this.addInput('extractMetadata', 'boolean', '提取元数据');

    // 输出端口
    this.addOutput('processedDocument', 'object', '处理后文档');
    this.addOutput('chunks', 'array', '文档块列表');
    this.addOutput('metadata', 'object', '文档元数据');
    this.addOutput('chunkCount', 'number', '块数量');
    this.addOutput('tokenCount', 'number', 'Token数量');
    this.addOutput('onProcessed', 'trigger', '处理完成');
    this.addOutput('onError', 'trigger', '处理失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const processTrigger = inputs?.process;
      if (!processTrigger) {
        return this.getDefaultOutputs();
      }

      const documentContent = inputs?.documentContent as string;
      const documentType = inputs?.documentType as string || 'text';
      const processingOptions = inputs?.processingOptions as any || {};
      const chunkSize = inputs?.chunkSize as number || 1000;
      const chunkOverlap = inputs?.chunkOverlap as number || 200;
      const extractMetadata = inputs?.extractMetadata as boolean ?? true;

      if (!documentContent) {
        throw new Error('未提供文档内容');
      }

      // 预处理文档内容
      const preprocessedContent = this.preprocessDocument(documentContent, documentType, processingOptions);

      // 提取元数据
      const metadata = extractMetadata ? this.extractDocumentMetadata(preprocessedContent, documentType) : {};

      // 文档分块
      const chunks = this.chunkDocument(preprocessedContent, chunkSize, chunkOverlap);

      // 计算token数量
      const tokenCount = this.estimateTokenCount(preprocessedContent);

      // 创建处理后的文档对象
      const processedDocument = {
        id: this.generateDocumentId(),
        content: preprocessedContent,
        originalContent: documentContent,
        type: documentType,
        metadata,
        chunks,
        tokenCount,
        chunkCount: chunks.length,
        processedAt: Date.now(),
        processingOptions
      };

      Debug.log('DocumentProcessingNode', `文档处理完成: ${processedDocument.id}, ${chunks.length}个块, ${tokenCount}个token`);

      return {
        processedDocument,
        chunks,
        metadata,
        chunkCount: chunks.length,
        tokenCount,
        onProcessed: true,
        onError: false
      };

    } catch (error) {
      Debug.error('DocumentProcessingNode', '文档处理失败', error);
      return {
        processedDocument: null,
        chunks: [],
        metadata: {},
        chunkCount: 0,
        tokenCount: 0,
        onProcessed: false,
        onError: true
      };
    }
  }

  private preprocessDocument(content: string, type: string, options: any): string {
    let processed = content;

    // 基础清理
    processed = processed.trim();

    // 根据文档类型进行特定处理
    switch (type.toLowerCase()) {
      case 'html':
        processed = this.processHTML(processed, options);
        break;
      case 'markdown':
        processed = this.processMarkdown(processed, options);
        break;
      case 'pdf':
        processed = this.processPDF(processed, options);
        break;
      case 'json':
        processed = this.processJSON(processed, options);
        break;
      default:
        processed = this.processPlainText(processed, options);
    }

    return processed;
  }

  private processHTML(content: string, options: any): string {
    // 简化的HTML处理：移除标签，保留文本
    return content
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // 移除script标签
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '') // 移除style标签
      .replace(/<[^>]+>/g, ' ') // 移除所有HTML标签
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim();
  }

  private processMarkdown(content: string, options: any): string {
    // 简化的Markdown处理：移除标记符号
    return content
      .replace(/^#{1,6}\s+/gm, '') // 移除标题标记
      .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
      .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
      .replace(/`(.*?)`/g, '$1') // 移除行内代码标记
      .replace(/```[\s\S]*?```/g, '') // 移除代码块
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接，保留文本
      .replace(/\s+/g, ' ')
      .trim();
  }

  private processPDF(content: string, options: any): string {
    // PDF内容通常已经是提取后的文本
    return content
      .replace(/\f/g, '\n') // 替换换页符
      .replace(/\s+/g, ' ')
      .trim();
  }

  private processJSON(content: string, options: any): string {
    try {
      const jsonData = JSON.parse(content);
      return this.extractTextFromJSON(jsonData);
    } catch (error) {
      return content;
    }
  }

  private processPlainText(content: string, options: any): string {
    return content
      .replace(/\s+/g, ' ')
      .trim();
  }

  private extractTextFromJSON(obj: any): string {
    const texts: string[] = [];

    const extractRecursive = (value: any) => {
      if (typeof value === 'string') {
        texts.push(value);
      } else if (typeof value === 'object' && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(extractRecursive);
        } else {
          Object.values(value).forEach(extractRecursive);
        }
      }
    };

    extractRecursive(obj);
    return texts.join(' ');
  }

  private extractDocumentMetadata(content: string, type: string): any {
    const metadata: any = {
      type,
      length: content.length,
      wordCount: content.split(/\s+/).length,
      extractedAt: Date.now()
    };

    // 尝试提取语言
    metadata.language = this.detectLanguage(content);

    // 提取关键统计信息
    metadata.sentences = content.split(/[.!?]+/).length;
    metadata.paragraphs = content.split(/\n\s*\n/).length;

    return metadata;
  }

  private detectLanguage(content: string): string {
    // 简化的语言检测
    const chineseChars = content.match(/[\u4e00-\u9fff]/g);
    const englishChars = content.match(/[a-zA-Z]/g);

    if (chineseChars && chineseChars.length > (englishChars?.length || 0)) {
      return 'zh';
    } else if (englishChars && englishChars.length > 0) {
      return 'en';
    }

    return 'unknown';
  }

  private chunkDocument(content: string, chunkSize: number, overlap: number): any[] {
    const chunks: any[] = [];
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);

    let currentChunk = '';
    let currentSize = 0;
    let chunkIndex = 0;

    for (const sentence of sentences) {
      const sentenceLength = sentence.length;

      if (currentSize + sentenceLength > chunkSize && currentChunk.length > 0) {
        // 创建当前块
        chunks.push({
          id: `chunk_${chunkIndex}`,
          content: currentChunk.trim(),
          index: chunkIndex,
          size: currentSize,
          tokenCount: this.estimateTokenCount(currentChunk)
        });

        // 开始新块，保留重叠部分
        const overlapText = this.getOverlapText(currentChunk, overlap);
        currentChunk = overlapText + sentence;
        currentSize = overlapText.length + sentenceLength;
        chunkIndex++;
      } else {
        currentChunk += sentence + '. ';
        currentSize += sentenceLength + 2;
      }
    }

    // 添加最后一个块
    if (currentChunk.trim().length > 0) {
      chunks.push({
        id: `chunk_${chunkIndex}`,
        content: currentChunk.trim(),
        index: chunkIndex,
        size: currentSize,
        tokenCount: this.estimateTokenCount(currentChunk)
      });
    }

    return chunks;
  }

  private getOverlapText(text: string, overlapSize: number): string {
    if (text.length <= overlapSize) return text;

    const overlapText = text.slice(-overlapSize);
    // 尝试在句子边界处截断
    const lastSentenceEnd = overlapText.lastIndexOf('. ');
    if (lastSentenceEnd > overlapSize / 2) {
      return overlapText.slice(lastSentenceEnd + 2);
    }

    return overlapText;
  }

  private estimateTokenCount(text: string): number {
    // 简化的token估算：大约4个字符=1个token
    return Math.ceil(text.length / 4);
  }

  private generateDocumentId(): string {
    return 'doc_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      processedDocument: null,
      chunks: [],
      metadata: {},
      chunkCount: 0,
      tokenCount: 0,
      onProcessed: false,
      onError: false
    };
  }
}

/**
 * 语义搜索节点
 */
export class SemanticSearchNode extends VisualScriptNode {
  public static readonly TYPE = 'SemanticSearch';
  public static readonly NAME = '语义搜索';
  public static readonly DESCRIPTION = '执行基于语义相似度的搜索';

  private static ragManager: AdvancedRAGManager = new AdvancedRAGManager();

  constructor(nodeType: string = SemanticSearchNode.TYPE, name: string = SemanticSearchNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('search', 'trigger', '执行搜索');
    this.addInput('query', 'string', '搜索查询');
    this.addInput('knowledgeBaseId', 'string', '知识库ID');
    this.addInput('topK', 'number', '返回数量');
    this.addInput('threshold', 'number', '相似度阈值');
    this.addInput('searchType', 'string', '搜索类型');
    this.addInput('filters', 'object', '过滤条件');

    // 输出端口
    this.addOutput('results', 'array', '搜索结果');
    this.addOutput('similarities', 'array', '相似度分数');
    this.addOutput('documents', 'array', '相关文档');
    this.addOutput('totalResults', 'number', '总结果数');
    this.addOutput('searchTime', 'number', '搜索耗时');
    this.addOutput('onSearchCompleted', 'trigger', '搜索完成');
    this.addOutput('onError', 'trigger', '搜索失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const searchTrigger = inputs?.search;
      if (!searchTrigger) {
        return this.getDefaultOutputs();
      }

      const query = inputs?.query as string;
      const knowledgeBaseId = inputs?.knowledgeBaseId as string;
      const topK = inputs?.topK as number || 5;
      const threshold = inputs?.threshold as number || 0.7;
      const searchType = inputs?.searchType as string || 'semantic';
      const filters = inputs?.filters as any;

      if (!query) {
        throw new Error('未提供搜索查询');
      }

      if (!knowledgeBaseId) {
        throw new Error('未提供知识库ID');
      }

      const startTime = Date.now();

      // 执行语义搜索
      const searchResults = await this.performSemanticSearch(
        query,
        knowledgeBaseId,
        topK,
        threshold,
        searchType,
        filters
      );

      const searchTime = Date.now() - startTime;

      Debug.log('SemanticSearchNode', `语义搜索完成: 查询="${query}", 找到${searchResults.length}个结果, 耗时${searchTime}ms`);

      return {
        results: searchResults,
        similarities: searchResults.map(r => r.similarity),
        documents: searchResults.map(r => r.document),
        totalResults: searchResults.length,
        searchTime,
        onSearchCompleted: true,
        onError: false
      };

    } catch (error) {
      Debug.error('SemanticSearchNode', '语义搜索失败', error);
      return {
        results: [],
        similarities: [],
        documents: [],
        totalResults: 0,
        searchTime: 0,
        onSearchCompleted: false,
        onError: true
      };
    }
  }

  private async performSemanticSearch(
    query: string,
    knowledgeBaseId: string,
    topK: number,
    threshold: number,
    searchType: string,
    filters?: any
  ): Promise<any[]> {
    // 获取知识库
    const knowledgeBase = SemanticSearchNode.ragManager.getKnowledgeBase(knowledgeBaseId);
    if (!knowledgeBase) {
      throw new Error('知识库不存在');
    }

    // 生成查询向量
    const queryEmbedding = await this.generateQueryEmbedding(query);

    // 搜索相似文档块
    const candidates = this.getCandidateChunks(knowledgeBase, filters);
    const scoredResults: any[] = [];

    for (const chunk of candidates) {
      if (!chunk.embedding) continue;

      const similarity = this.calculateSimilarity(queryEmbedding, chunk.embedding, searchType);

      if (similarity >= threshold) {
        const document = SemanticSearchNode.ragManager.getDocument(chunk.documentId);
        if (document) {
          scoredResults.push({
            chunk,
            document,
            similarity,
            relevanceScore: similarity,
            contextScore: this.calculateContextScore(chunk, query)
          });
        }
      }
    }

    // 排序和截取
    scoredResults.sort((a, b) => b.similarity - a.similarity);
    return scoredResults.slice(0, topK);
  }

  private async generateQueryEmbedding(query: string): Promise<number[]> {
    // 模拟向量生成（实际应用中需要调用真实的嵌入API）
    const dimension = 768; // 假设使用768维向量
    const embedding = new Array(dimension);

    // 基于查询文本生成确定性向量
    const hash = this.hashText(query);
    for (let i = 0; i < dimension; i++) {
      embedding[i] = Math.sin(hash + i) * 0.5;
    }

    // 归一化
    const norm = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / norm);
  }

  private getCandidateChunks(knowledgeBase: any, filters?: any): any[] {
    const chunks: any[] = [];

    for (const document of knowledgeBase.documents.values()) {
      if (this.documentMatchesFilters(document, filters)) {
        chunks.push(...document.chunks);
      }
    }

    return chunks;
  }

  private documentMatchesFilters(document: any, filters?: any): boolean {
    if (!filters) return true;

    // 实现过滤逻辑
    if (filters.documentTypes && !filters.documentTypes.includes(document.type)) {
      return false;
    }

    if (filters.categories && !filters.categories.includes(document.metadata.category)) {
      return false;
    }

    return true;
  }

  private calculateSimilarity(vec1: number[], vec2: number[], searchType: string): number {
    switch (searchType) {
      case 'cosine':
      case 'semantic':
        return this.cosineSimilarity(vec1, vec2);
      case 'euclidean':
        return 1 / (1 + this.euclideanDistance(vec1, vec2));
      case 'dot':
        return this.dotProduct(vec1, vec2);
      default:
        return this.cosineSimilarity(vec1, vec2);
    }
  }

  private cosineSimilarity(vec1: number[], vec2: number[]): number {
    if (vec1.length !== vec2.length) return 0;

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < vec1.length; i++) {
      dotProduct += vec1[i] * vec2[i];
      norm1 += vec1[i] * vec1[i];
      norm2 += vec2[i] * vec2[i];
    }

    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  private euclideanDistance(vec1: number[], vec2: number[]): number {
    if (vec1.length !== vec2.length) return Infinity;

    let sum = 0;
    for (let i = 0; i < vec1.length; i++) {
      const diff = vec1[i] - vec2[i];
      sum += diff * diff;
    }

    return Math.sqrt(sum);
  }

  private dotProduct(vec1: number[], vec2: number[]): number {
    if (vec1.length !== vec2.length) return 0;

    let product = 0;
    for (let i = 0; i < vec1.length; i++) {
      product += vec1[i] * vec2[i];
    }

    return product;
  }

  private calculateContextScore(chunk: any, query: string): number {
    // 简化的上下文分数计算
    const queryWords = query.toLowerCase().split(/\s+/);
    const chunkWords = chunk.content.toLowerCase().split(/\s+/);

    let matches = 0;
    for (const word of queryWords) {
      if (chunkWords.includes(word)) {
        matches++;
      }
    }

    return matches / queryWords.length;
  }

  private hashText(text: string): number {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  private getDefaultOutputs(): any {
    return {
      results: [],
      similarities: [],
      documents: [],
      totalResults: 0,
      searchTime: 0,
      onSearchCompleted: false,
      onError: false
    };
  }
}
