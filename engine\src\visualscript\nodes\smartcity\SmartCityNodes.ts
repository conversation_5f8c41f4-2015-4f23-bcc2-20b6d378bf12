/**
 * 智慧城市专用视觉脚本节点
 * 提供城市管理、IoT设备、交通、环境监测等专业功能
 */

// 简化的节点基类，用于智慧城市节点
export abstract class SmartCityNode {
  public readonly type: string;
  public readonly name: string;
  public readonly description: string;
  protected inputs: Map<string, any> = new Map();
  protected outputs: Map<string, any> = new Map();

  constructor(type: string, name: string, description: string) {
    this.type = type;
    this.name = name;
    this.description = description;
  }

  protected addInput(name: string, type: string, description: string, defaultValue?: any): void {
    this.inputs.set(name, { name, type, description, defaultValue, value: defaultValue });
  }

  protected addOutput(name: string, type: string, description: string): void {
    this.outputs.set(name, { name, type, description, value: undefined });
  }

  protected getInputValue(name: string): any {
    const input = this.inputs.get(name);
    return input ? input.value : undefined;
  }

  protected setOutputValue(name: string, value: any): void {
    const output = this.outputs.get(name);
    if (output) {
      output.value = value;
    }
  }

  public abstract execute(): Promise<void> | void;
}

// ==================== IoT设备管理节点 ====================

/**
 * IoT设备连接节点
 * 连接和管理IoT设备
 */
export class IoTDeviceConnectNode extends SmartCityNode {
  public static readonly TYPE = 'iot_device_connect';
  public static readonly NAME = 'IoT设备连接';
  public static readonly DESCRIPTION = '连接和管理IoT设备，支持多种协议';

  constructor() {
    super(IoTDeviceConnectNode.TYPE, IoTDeviceConnectNode.NAME, IoTDeviceConnectNode.DESCRIPTION);

    // 添加输入
    this.addInput('deviceId', 'string', '设备ID');
    this.addInput('protocol', 'string', '协议类型'); // MQTT, HTTP, CoAP, LoRaWAN
    this.addInput('endpoint', 'string', '连接端点');
    this.addInput('credentials', 'object', '认证信息');
    this.addInput('config', 'object', '设备配置');

    // 添加输出
    this.addOutput('device', 'object', '设备对象');
    this.addOutput('connected', 'boolean', '连接状态');
    this.addOutput('onConnected', 'exec', '连接成功事件');
    this.addOutput('onError', 'exec', '连接错误事件');
    this.addOutput('onDataReceived', 'exec', '数据接收事件');
  }

  async execute(): Promise<void> {
    const deviceId = this.getInputValue('deviceId') as string;
    const protocol = this.getInputValue('protocol') as string;
    const endpoint = this.getInputValue('endpoint') as string;
    const credentials = this.getInputValue('credentials') as any;
    const config = this.getInputValue('config') as any;

    try {
      // 创建设备连接
      const device = this.createDeviceConnection(deviceId, protocol, endpoint, credentials, config);

      this.setOutputValue('device', device);
      this.setOutputValue('connected', true);

      // 设置数据接收监听
      this.setupDataListener(device);

      console.log(`IoT设备 ${deviceId} 连接成功`);

    } catch (error) {
      this.setOutputValue('connected', false);
      console.error(`IoT设备连接失败: ${error}`);
    }
  }

  private createDeviceConnection(deviceId: string, protocol: string, endpoint: string, credentials: any, config: any): any {
    // 模拟设备连接创建
    return {
      id: deviceId,
      protocol,
      endpoint,
      status: 'connected',
      lastSeen: new Date(),
      data: {},
      metadata: config
    };
  }

  private setupDataListener(device: any): void {
    // 模拟数据接收
    setInterval(() => {
      const mockData = this.generateMockData(device);
      device.data = mockData;
      console.log(`设备 ${device.id} 接收到数据:`, mockData);
    }, 5000);
  }

  private generateMockData(device: any): any {
    // 根据设备类型生成模拟数据
    switch (device.metadata?.type) {
      case 'temperature_sensor':
        return { temperature: 20 + Math.random() * 15, humidity: 40 + Math.random() * 40 };
      case 'air_quality_sensor':
        return { pm25: Math.random() * 100, pm10: Math.random() * 150, aqi: Math.random() * 200 };
      case 'traffic_camera':
        return { vehicleCount: Math.floor(Math.random() * 50), avgSpeed: 30 + Math.random() * 40 };
      default:
        return { value: Math.random() * 100, timestamp: new Date() };
    }
  }
}

/**
 * IoT数据处理节点
 * 处理和分析IoT设备数据
 */
export class IoTDataProcessingNode extends SmartCityNode {
  public static readonly TYPE = 'iot_data_processing';
  public static readonly NAME = 'IoT数据处理';
  public static readonly DESCRIPTION = '处理和分析IoT设备数据，支持实时计算和异常检测';

  constructor() {
    super(IoTDataProcessingNode.TYPE, IoTDataProcessingNode.NAME, IoTDataProcessingNode.DESCRIPTION);

    // 添加输入
    this.addInput('rawData', 'object', '原始数据');
    this.addInput('processingType', 'string', '处理类型'); // filter, aggregate, anomaly_detect
    this.addInput('timeWindow', 'number', '时间窗口', 60);
    this.addInput('threshold', 'object', '阈值配置');

    // 添加输出
    this.addOutput('processedData', 'object', '处理后数据');
    this.addOutput('statistics', 'object', '统计信息');
    this.addOutput('anomalies', 'array', '异常数据');
    this.addOutput('onProcessed', 'exec', '处理完成事件');
    this.addOutput('onAnomaly', 'exec', '异常检测事件');
  }

  execute(): void {
    const rawData = this.getInputValue('rawData') as any;
    const processingType = this.getInputValue('processingType') as string;
    const timeWindow = this.getInputValue('timeWindow') as number || 60;
    const threshold = this.getInputValue('threshold') as any;

    if (!rawData) return;

    let processedData: any;
    let statistics: any;
    let anomalies: any[] = [];

    switch (processingType) {
      case 'filter':
        processedData = this.filterData(rawData, threshold);
        break;
      case 'aggregate':
        processedData = this.aggregateData(rawData, timeWindow);
        statistics = this.calculateStatistics(processedData);
        break;
      case 'anomaly_detect':
        const result = this.detectAnomalies(rawData, threshold);
        processedData = result.data;
        anomalies = result.anomalies;
        break;
      default:
        processedData = rawData;
    }

    this.setOutputValue('processedData', processedData);
    this.setOutputValue('statistics', statistics);
    this.setOutputValue('anomalies', anomalies);

    console.log(`数据处理完成，类型: ${processingType}`);

    if (anomalies.length > 0) {
      console.log(`检测到 ${anomalies.length} 个异常`);
    }
  }

  private filterData(data: any, threshold: any): any {
    // 数据过滤逻辑
    if (threshold && typeof data === 'object') {
      const filtered: any = {};
      for (const [key, value] of Object.entries(data)) {
        if (typeof value === 'number' && threshold[key]) {
          if (value >= threshold[key].min && value <= threshold[key].max) {
            filtered[key] = value;
          }
        } else {
          filtered[key] = value;
        }
      }
      return filtered;
    }
    return data;
  }

  private aggregateData(data: any, timeWindow: number): any {
    // 数据聚合逻辑（简化实现）
    if (Array.isArray(data)) {
      const now = Date.now();
      const windowStart = now - (timeWindow * 1000);
      
      return data.filter((item: any) => {
        const timestamp = new Date(item.timestamp).getTime();
        return timestamp >= windowStart;
      });
    }
    return data;
  }

  private calculateStatistics(data: any): any {
    if (Array.isArray(data) && data.length > 0) {
      const numericFields = Object.keys(data[0]).filter(key => 
        typeof data[0][key] === 'number'
      );
      
      const stats: any = {};
      numericFields.forEach(field => {
        const values = data.map(item => item[field]).filter(v => typeof v === 'number');
        if (values.length > 0) {
          stats[field] = {
            count: values.length,
            min: Math.min(...values),
            max: Math.max(...values),
            avg: values.reduce((a, b) => a + b, 0) / values.length,
            sum: values.reduce((a, b) => a + b, 0)
          };
        }
      });
      
      return stats;
    }
    return {};
  }

  private detectAnomalies(data: any, threshold: any): { data: any, anomalies: any[] } {
    const anomalies: any[] = [];
    
    if (threshold && typeof data === 'object') {
      for (const [key, value] of Object.entries(data)) {
        if (typeof value === 'number' && threshold[key]) {
          if (value < threshold[key].min || value > threshold[key].max) {
            anomalies.push({
              field: key,
              value,
              threshold: threshold[key],
              timestamp: new Date(),
              severity: this.calculateSeverity(value, threshold[key])
            });
          }
        }
      }
    }
    
    return { data, anomalies };
  }

  private calculateSeverity(value: number, threshold: any): string {
    const deviation = Math.max(
      Math.abs(value - threshold.min) / threshold.min,
      Math.abs(value - threshold.max) / threshold.max
    );

    if (deviation > 0.5) return 'critical';
    if (deviation > 0.3) return 'high';
    if (deviation > 0.1) return 'medium';
    return 'low';
  }
}

// ==================== 智能交通节点 ====================

/**
 * 交通流量监测节点
 * 监测和分析交通流量数据
 */
export class TrafficFlowMonitorNode extends SmartCityNode {
  public static readonly TYPE = 'traffic_flow_monitor';
  public static readonly NAME = '交通流量监测';
  public static readonly DESCRIPTION = '监测和分析道路交通流量，提供实时交通状态';

  constructor() {
    super(TrafficFlowMonitorNode.TYPE, TrafficFlowMonitorNode.NAME, TrafficFlowMonitorNode.DESCRIPTION);

    // 添加输入
    this.addInput('cameraData', 'object', '摄像头数据');
    this.addInput('sensorData', 'object', '传感器数据');
    this.addInput('roadId', 'string', '道路ID');
    this.addInput('analysisInterval', 'number', '分析间隔', 60);

    // 添加输出
    this.addOutput('vehicleCount', 'number', '车辆数量');
    this.addOutput('averageSpeed', 'number', '平均速度');
    this.addOutput('congestionLevel', 'string', '拥堵等级');
    this.addOutput('flowRate', 'number', '流量率');
    this.addOutput('trafficStatus', 'object', '交通状态');
    this.addOutput('onCongestion', 'exec', '拥堵事件');
    this.addOutput('onFlowChange', 'exec', '流量变化事件');
  }

  execute(): void {
    const cameraData = this.getInputValue('cameraData') as any;
    const sensorData = this.getInputValue('sensorData') as any;
    const roadId = this.getInputValue('roadId') as string;
    const analysisInterval = this.getInputValue('analysisInterval') as number || 60;

    // 分析交通数据
    const analysis = this.analyzeTrafficFlow(cameraData, sensorData, roadId);

    this.setOutputValue('vehicleCount', analysis.vehicleCount);
    this.setOutputValue('averageSpeed', analysis.averageSpeed);
    this.setOutputValue('congestionLevel', analysis.congestionLevel);
    this.setOutputValue('flowRate', analysis.flowRate);
    this.setOutputValue('trafficStatus', analysis);

    console.log(`交通流量分析完成: 道路 ${roadId}, 拥堵等级: ${analysis.congestionLevel}`);

    // 触发相应事件
    if (analysis.congestionLevel === 'heavy' || analysis.congestionLevel === 'severe') {
      console.log(`检测到严重拥堵: ${analysis.congestionLevel}`);
    }
  }

  private analyzeTrafficFlow(cameraData: any, sensorData: any, roadId: string): any {
    // 模拟交通流量分析
    const vehicleCount = Math.floor(Math.random() * 100);
    const averageSpeed = 20 + Math.random() * 60;
    const flowRate = vehicleCount / 60; // 车辆/分钟

    let congestionLevel = 'light';
    if (vehicleCount > 80 || averageSpeed < 20) {
      congestionLevel = 'severe';
    } else if (vehicleCount > 60 || averageSpeed < 30) {
      congestionLevel = 'heavy';
    } else if (vehicleCount > 40 || averageSpeed < 45) {
      congestionLevel = 'moderate';
    }

    return {
      roadId,
      vehicleCount,
      averageSpeed,
      congestionLevel,
      flowRate,
      timestamp: new Date(),
      confidence: 0.85 + Math.random() * 0.15
    };
  }
}

/**
 * 智能信号灯控制节点
 * 根据交通流量智能控制信号灯
 */
export class SmartTrafficLightNode extends SmartCityNode {
  public static readonly TYPE = 'smart_traffic_light';
  public static readonly NAME = '智能信号灯控制';
  public static readonly DESCRIPTION = '根据实时交通流量智能调节信号灯时序';

  constructor() {
    super(SmartTrafficLightNode.TYPE, SmartTrafficLightNode.NAME, SmartTrafficLightNode.DESCRIPTION);

    // 添加输入
    this.addInput('intersectionId', 'string', '路口ID');
    this.addInput('trafficData', 'object', '交通数据');
    this.addInput('currentPhase', 'string', '当前相位');
    this.addInput('optimizationMode', 'string', '优化模式', 'flow'); // flow, delay, emission

    // 添加输出
    this.addOutput('newPhase', 'string', '新相位');
    this.addOutput('phaseDuration', 'number', '相位时长');
    this.addOutput('cycleTime', 'number', '周期时间');
    this.addOutput('optimization', 'object', '优化结果');
    this.addOutput('onPhaseChange', 'exec', '相位变化事件');
  }

  execute(): void {
    const intersectionId = this.getInputValue('intersectionId') as string;
    const trafficData = this.getInputValue('trafficData') as any;
    const currentPhase = this.getInputValue('currentPhase') as string;
    const optimizationMode = this.getInputValue('optimizationMode') as string || 'flow';

    // 计算最优信号灯配时
    const optimization = this.optimizeSignalTiming(intersectionId, trafficData, currentPhase, optimizationMode);

    this.setOutputValue('newPhase', optimization.newPhase);
    this.setOutputValue('phaseDuration', optimization.phaseDuration);
    this.setOutputValue('cycleTime', optimization.cycleTime);
    this.setOutputValue('optimization', optimization);

    console.log(`信号灯优化完成: 路口 ${intersectionId}, 新相位: ${optimization.newPhase}`);

    if (optimization.newPhase !== currentPhase) {
      console.log(`相位变化: ${currentPhase} -> ${optimization.newPhase}`);
    }
  }

  private optimizeSignalTiming(intersectionId: string, trafficData: any, currentPhase: string, mode: string): any {
    // 简化的信号灯优化算法
    const phases = ['north_south', 'east_west', 'left_turn', 'pedestrian'];
    const baseDuration = 30;

    // 根据交通流量调整相位时长
    let phaseDuration = baseDuration;
    if (trafficData?.congestionLevel === 'heavy') {
      phaseDuration = baseDuration * 1.5;
    } else if (trafficData?.congestionLevel === 'light') {
      phaseDuration = baseDuration * 0.8;
    }

    // 选择下一个相位
    const currentIndex = phases.indexOf(currentPhase);
    const newPhase = phases[(currentIndex + 1) % phases.length];

    return {
      intersectionId,
      newPhase,
      phaseDuration,
      cycleTime: phases.length * phaseDuration,
      optimizationMode: mode,
      efficiency: 0.75 + Math.random() * 0.25,
      timestamp: new Date()
    };
  }
}

// ==================== 环境监测节点 ====================

/**
 * 空气质量监测节点
 * 监测和分析空气质量数据
 */
export class AirQualityMonitorNode extends SmartCityNode {
  public static readonly TYPE = 'air_quality_monitor';
  public static readonly NAME = '空气质量监测';
  public static readonly DESCRIPTION = '监测空气质量指标，提供污染预警和健康建议';

  constructor() {
    super(AirQualityMonitorNode.TYPE, AirQualityMonitorNode.NAME, AirQualityMonitorNode.DESCRIPTION);

    // 添加输入
    this.addInput('sensorData', 'object', '传感器数据');
    this.addInput('location', 'object', '监测位置');
    this.addInput('weatherData', 'object', '气象数据');

    // 添加输出
    this.addOutput('aqi', 'number', 'AQI指数');
    this.addOutput('pm25', 'number', 'PM2.5浓度');
    this.addOutput('pm10', 'number', 'PM10浓度');
    this.addOutput('pollutionLevel', 'string', '污染等级');
    this.addOutput('healthAdvice', 'string', '健康建议');
    this.addOutput('forecast', 'object', '质量预测');
    this.addOutput('onAlert', 'exec', '污染预警事件');
  }

  execute(): void {
    const sensorData = this.getInputValue('sensorData') as any;
    const location = this.getInputValue('location') as any;
    const weatherData = this.getInputValue('weatherData') as any;

    if (!sensorData) return;

    // 分析空气质量
    const analysis = this.analyzeAirQuality(sensorData, location, weatherData);

    this.setOutputValue('aqi', analysis.aqi);
    this.setOutputValue('pm25', analysis.pm25);
    this.setOutputValue('pm10', analysis.pm10);
    this.setOutputValue('pollutionLevel', analysis.pollutionLevel);
    this.setOutputValue('healthAdvice', analysis.healthAdvice);
    this.setOutputValue('forecast', analysis.forecast);

    console.log(`空气质量分析完成: AQI ${analysis.aqi}, 等级: ${analysis.pollutionLevel}`);

    // 触发污染预警
    if (analysis.aqi > 150) {
      console.log(`空气质量预警: AQI ${analysis.aqi} 超过阈值`);
    }
  }

  private analyzeAirQuality(sensorData: any, location: any, weatherData: any): any {
    const pm25 = sensorData.pm25 || Math.random() * 100;
    const pm10 = sensorData.pm10 || Math.random() * 150;

    // 计算AQI
    const aqi = Math.max(
      this.calculateAQI(pm25, 'pm25'),
      this.calculateAQI(pm10, 'pm10')
    );

    // 确定污染等级
    let pollutionLevel = 'good';
    let healthAdvice = '空气质量良好，适合户外活动';

    if (aqi > 300) {
      pollutionLevel = 'hazardous';
      healthAdvice = '空气质量危险，避免户外活动';
    } else if (aqi > 200) {
      pollutionLevel = 'very_unhealthy';
      healthAdvice = '空气质量很不健康，减少户外活动';
    } else if (aqi > 150) {
      pollutionLevel = 'unhealthy';
      healthAdvice = '空气质量不健康，敏感人群避免户外活动';
    } else if (aqi > 100) {
      pollutionLevel = 'unhealthy_for_sensitive';
      healthAdvice = '对敏感人群不健康，注意防护';
    } else if (aqi > 50) {
      pollutionLevel = 'moderate';
      healthAdvice = '空气质量一般，可正常活动';
    }

    return {
      aqi,
      pm25,
      pm10,
      pollutionLevel,
      healthAdvice,
      forecast: this.generateForecast(aqi, weatherData),
      timestamp: new Date(),
      location
    };
  }

  private calculateAQI(concentration: number, pollutant: string): number {
    // 简化的AQI计算
    const breakpoints: any = {
      pm25: [
        { low: 0, high: 12, aqiLow: 0, aqiHigh: 50 },
        { low: 12.1, high: 35.4, aqiLow: 51, aqiHigh: 100 },
        { low: 35.5, high: 55.4, aqiLow: 101, aqiHigh: 150 },
        { low: 55.5, high: 150.4, aqiLow: 151, aqiHigh: 200 },
        { low: 150.5, high: 250.4, aqiLow: 201, aqiHigh: 300 },
        { low: 250.5, high: 500, aqiLow: 301, aqiHigh: 500 }
      ],
      pm10: [
        { low: 0, high: 54, aqiLow: 0, aqiHigh: 50 },
        { low: 55, high: 154, aqiLow: 51, aqiHigh: 100 },
        { low: 155, high: 254, aqiLow: 101, aqiHigh: 150 },
        { low: 255, high: 354, aqiLow: 151, aqiHigh: 200 },
        { low: 355, high: 424, aqiLow: 201, aqiHigh: 300 },
        { low: 425, high: 604, aqiLow: 301, aqiHigh: 500 }
      ]
    };

    const ranges = breakpoints[pollutant];
    for (const range of ranges) {
      if (concentration >= range.low && concentration <= range.high) {
        return Math.round(
          ((range.aqiHigh - range.aqiLow) / (range.high - range.low)) *
          (concentration - range.low) + range.aqiLow
        );
      }
    }

    return 500; // 超出范围
  }

  private generateForecast(currentAQI: number, weatherData: any): any {
    // 简化的空气质量预测
    const trend = Math.random() > 0.5 ? 'improving' : 'worsening';
    const change = (Math.random() - 0.5) * 50;

    return {
      trend,
      expectedAQI: Math.max(0, currentAQI + change),
      confidence: 0.7 + Math.random() * 0.3,
      factors: ['wind_speed', 'humidity', 'temperature'],
      timeframe: '24h'
    };
  }
}

// ==================== 城市管理节点 ====================

/**
 * 智慧路灯管理节点
 * 管理城市智能路灯系统
 */
export class SmartStreetLightNode extends SmartCityNode {
  public static readonly TYPE = 'smart_street_light';
  public static readonly NAME = '智慧路灯管理';
  public static readonly DESCRIPTION = '智能控制路灯亮度、开关时间，支持节能和故障检测';

  constructor() {
    super(SmartStreetLightNode.TYPE, SmartStreetLightNode.NAME, SmartStreetLightNode.DESCRIPTION);

    // 添加输入
    this.addInput('lightId', 'string', '路灯ID');
    this.addInput('ambientLight', 'number', '环境光照', 50);
    this.addInput('motionDetected', 'boolean', '运动检测', false);
    this.addInput('timeOfDay', 'string', '时间', 'day');
    this.addInput('energyMode', 'string', '节能模式', 'normal');

    // 添加输出
    this.addOutput('brightness', 'number', '亮度等级');
    this.addOutput('powerConsumption', 'number', '功耗');
    this.addOutput('status', 'string', '状态');
    this.addOutput('energySaved', 'number', '节能量');
    this.addOutput('onFault', 'exec', '故障事件');
    this.addOutput('onMotion', 'exec', '运动检测事件');
  }

  execute(): void {
    const lightId = this.getInputValue('lightId') as string;
    const ambientLight = this.getInputValue('ambientLight') as number || 0;
    const motionDetected = this.getInputValue('motionDetected') as boolean;
    const timeOfDay = this.getInputValue('timeOfDay') as string;
    const energyMode = this.getInputValue('energyMode') as string || 'normal';

    // 智能控制逻辑
    const control = this.calculateLightControl(lightId, ambientLight, motionDetected, timeOfDay, energyMode);

    this.setOutputValue('brightness', control.brightness);
    this.setOutputValue('powerConsumption', control.powerConsumption);
    this.setOutputValue('status', control.status);
    this.setOutputValue('energySaved', control.energySaved);

    console.log(`路灯控制完成: ${lightId}, 亮度: ${control.brightness}%, 状态: ${control.status}`);

    // 触发事件
    if (control.fault) {
      console.log(`路灯故障检测: ${lightId}`);
    }

    if (motionDetected) {
      console.log(`运动检测触发: ${lightId}`);
    }
  }

  private calculateLightControl(lightId: string, ambientLight: number, motionDetected: boolean, timeOfDay: string, energyMode: string): any {
    let brightness = 0;
    let status = 'off';

    // 基础亮度计算
    if (ambientLight < 50) { // 环境较暗
      if (timeOfDay === 'night' || timeOfDay === 'evening') {
        brightness = 80;
        status = 'on';
      } else if (timeOfDay === 'dawn' || timeOfDay === 'dusk') {
        brightness = 50;
        status = 'on';
      }
    }

    // 运动检测增强
    if (motionDetected && brightness > 0) {
      brightness = Math.min(100, brightness + 30);
    }

    // 节能模式调整
    if (energyMode === 'eco' && !motionDetected) {
      brightness = Math.floor(brightness * 0.7);
    } else if (energyMode === 'high_efficiency') {
      brightness = Math.floor(brightness * 0.5);
    }

    // 计算功耗
    const basePower = 50; // 瓦特
    const powerConsumption = (brightness / 100) * basePower;

    // 计算节能量
    const standardPower = 50;
    const energySaved = Math.max(0, standardPower - powerConsumption);

    // 模拟故障检测
    const fault = Math.random() < 0.01; // 1%故障率

    return {
      lightId,
      brightness,
      powerConsumption,
      status: fault ? 'fault' : status,
      energySaved,
      fault,
      timestamp: new Date()
    };
  }
}

/**
 * 应急响应管理节点
 * 处理城市应急事件和响应协调
 */
export class EmergencyResponseNode extends SmartCityNode {
  public static readonly TYPE = 'emergency_response';
  public static readonly NAME = '应急响应管理';
  public static readonly DESCRIPTION = '处理应急事件，协调响应资源，提供决策支持';

  constructor() {
    super(EmergencyResponseNode.TYPE, EmergencyResponseNode.NAME, EmergencyResponseNode.DESCRIPTION);

    // 添加输入
    this.addInput('eventType', 'string', '事件类型'); // fire, flood, accident, medical
    this.addInput('location', 'object', '事件位置');
    this.addInput('severity', 'string', '严重程度'); // low, medium, high, critical
    this.addInput('description', 'string', '事件描述');
    this.addInput('availableResources', 'array', '可用资源', []);

    // 添加输出
    this.addOutput('responseLevel', 'string', '响应级别');
    this.addOutput('assignedResources', 'array', '分配资源');
    this.addOutput('evacuationPlan', 'object', '疏散计划');
    this.addOutput('estimatedTime', 'number', '预计响应时间');
    this.addOutput('actionPlan', 'object', '行动计划');
    this.addOutput('onResourceDispatched', 'exec', '资源调度事件');
    this.addOutput('onPlanUpdated', 'exec', '计划更新事件');
  }

  execute(): void {
    const eventType = this.getInputValue('eventType') as string;
    const location = this.getInputValue('location') as any;
    const severity = this.getInputValue('severity') as string;
    const description = this.getInputValue('description') as string;
    const availableResources = this.getInputValue('availableResources') as any[] || [];

    // 生成应急响应计划
    const response = this.generateEmergencyResponse(eventType, location, severity, description, availableResources);

    this.setOutputValue('responseLevel', response.responseLevel);
    this.setOutputValue('assignedResources', response.assignedResources);
    this.setOutputValue('evacuationPlan', response.evacuationPlan);
    this.setOutputValue('estimatedTime', response.estimatedTime);
    this.setOutputValue('actionPlan', response.actionPlan);

    console.log(`应急响应计划生成完成: 事件类型 ${eventType}, 响应级别 ${response.responseLevel}`);
    console.log(`预计响应时间: ${response.estimatedTime} 分钟`);
    console.log(`分配资源数量: ${response.assignedResources.length}`);
  }

  private generateEmergencyResponse(eventType: string, location: any, severity: string, description: string, availableResources: any[]): any {
    // 确定响应级别
    let responseLevel = 'level_1';
    if (severity === 'critical') {
      responseLevel = 'level_4';
    } else if (severity === 'high') {
      responseLevel = 'level_3';
    } else if (severity === 'medium') {
      responseLevel = 'level_2';
    }

    // 分配资源
    const assignedResources = this.allocateResources(eventType, severity, availableResources);

    // 生成疏散计划
    const evacuationPlan = this.generateEvacuationPlan(eventType, location, severity);

    // 估算响应时间
    const estimatedTime = this.calculateResponseTime(eventType, location, assignedResources);

    // 生成行动计划
    const actionPlan = this.generateActionPlan(eventType, severity, assignedResources);

    return {
      responseLevel,
      assignedResources,
      evacuationPlan,
      estimatedTime,
      actionPlan,
      timestamp: new Date(),
      eventId: `event_${Date.now()}`
    };
  }

  private allocateResources(eventType: string, severity: string, availableResources: any[]): any[] {
    // 简化的资源分配逻辑
    const requiredResources: any[] = [];

    switch (eventType) {
      case 'fire':
        requiredResources.push({ type: 'fire_truck', count: severity === 'critical' ? 3 : 2 });
        requiredResources.push({ type: 'ambulance', count: 1 });
        break;
      case 'flood':
        requiredResources.push({ type: 'rescue_boat', count: 2 });
        requiredResources.push({ type: 'evacuation_bus', count: 3 });
        break;
      case 'accident':
        requiredResources.push({ type: 'ambulance', count: 2 });
        requiredResources.push({ type: 'police_car', count: 1 });
        break;
      case 'medical':
        requiredResources.push({ type: 'ambulance', count: 1 });
        requiredResources.push({ type: 'medical_team', count: 1 });
        break;
    }

    return requiredResources;
  }

  private generateEvacuationPlan(eventType: string, location: any, severity: string): any {
    return {
      evacuationRadius: severity === 'critical' ? 1000 : 500, // 米
      evacuationRoutes: ['route_a', 'route_b', 'route_c'],
      safetyZones: ['zone_1', 'zone_2'],
      estimatedEvacuees: Math.floor(Math.random() * 1000),
      transportationNeeded: true
    };
  }

  private calculateResponseTime(eventType: string, location: any, assignedResources: any[]): number {
    // 基础响应时间（分钟）
    let baseTime = 10;

    switch (eventType) {
      case 'fire':
        baseTime = 8;
        break;
      case 'medical':
        baseTime = 6;
        break;
      case 'accident':
        baseTime = 12;
        break;
      case 'flood':
        baseTime = 15;
        break;
    }

    // 考虑距离和资源可用性
    const distanceFactor = Math.random() * 5; // 0-5分钟
    const resourceFactor = assignedResources.length > 2 ? -2 : 2; // 资源多则快

    return Math.max(5, baseTime + distanceFactor + resourceFactor);
  }

  private generateActionPlan(eventType: string, severity: string, assignedResources: any[]): any {
    return {
      phases: [
        { name: 'immediate_response', duration: 5, actions: ['assess_situation', 'secure_area'] },
        { name: 'resource_deployment', duration: 10, actions: ['deploy_teams', 'establish_command'] },
        { name: 'active_response', duration: 30, actions: ['execute_plan', 'monitor_progress'] },
        { name: 'recovery', duration: 60, actions: ['cleanup', 'restore_services'] }
      ],
      priority: severity === 'critical' ? 'highest' : 'high',
      coordination: ['fire_department', 'police', 'medical_services'],
      communication: ['radio', 'mobile', 'emergency_broadcast']
    };
  }
}
