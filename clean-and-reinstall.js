#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 查找所有需要清理的目录和文件
function findCleanupTargets(dir) {
  const targets = {
    nodeModules: [],
    packageLocks: []
  };
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (item === 'node_modules') {
            targets.nodeModules.push(fullPath);
          } else if (!item.startsWith('.')) {
            traverse(fullPath);
          }
        } else if (item === 'package-lock.json') {
          targets.packageLocks.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`无法读取目录 ${currentDir}:`, error.message);
    }
  }
  
  traverse(dir);
  return targets;
}

// 删除目录或文件
function removeTarget(targetPath) {
  try {
    const stat = fs.statSync(targetPath);
    if (stat.isDirectory()) {
      fs.rmSync(targetPath, { recursive: true, force: true });
    } else {
      fs.unlinkSync(targetPath);
    }
    return true;
  } catch (error) {
    console.error(`删除 ${targetPath} 失败:`, error.message);
    return false;
  }
}

// 查找所有有package.json的目录
function findPackageDirectories(dir) {
  const directories = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      if (items.includes('package.json')) {
        directories.push(currentDir);
      }
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          traverse(fullPath);
        }
      }
    } catch (error) {
      console.error(`无法读取目录 ${currentDir}:`, error.message);
    }
  }
  
  traverse(dir);
  return directories;
}

// 安装依赖
function installDependencies(directory) {
  try {
    console.log(`  📦 安装依赖中...`);
    execSync('npm install --legacy-peer-deps', { 
      cwd: directory, 
      stdio: 'pipe',
      timeout: 300000 // 5分钟超时
    });
    console.log(`  ✅ 安装成功`);
    return true;
  } catch (error) {
    console.log(`  ❌ 安装失败: ${error.message.split('\n')[0]}`);
    return false;
  }
}

// 主函数
function main() {
  console.log('🧹 开始清理和重新安装依赖...\n');
  
  const serverDir = path.join(__dirname, 'server');
  
  if (!fs.existsSync(serverDir)) {
    console.error('server 目录不存在');
    process.exit(1);
  }
  
  // 1. 查找清理目标
  console.log('🔍 查找需要清理的文件和目录...');
  const targets = findCleanupTargets(serverDir);
  
  console.log(`找到 ${targets.nodeModules.length} 个 node_modules 目录`);
  console.log(`找到 ${targets.packageLocks.length} 个 package-lock.json 文件\n`);
  
  // 2. 清理 node_modules
  if (targets.nodeModules.length > 0) {
    console.log('🗑️  删除 node_modules 目录...');
    let deletedCount = 0;
    targets.nodeModules.forEach((dir, index) => {
      const relativePath = path.relative(__dirname, dir);
      process.stdout.write(`  [${index + 1}/${targets.nodeModules.length}] 删除 ${relativePath}...`);
      if (removeTarget(dir)) {
        console.log(' ✅');
        deletedCount++;
      } else {
        console.log(' ❌');
      }
    });
    console.log(`删除了 ${deletedCount}/${targets.nodeModules.length} 个 node_modules 目录\n`);
  }
  
  // 3. 清理 package-lock.json
  if (targets.packageLocks.length > 0) {
    console.log('🗑️  删除 package-lock.json 文件...');
    let deletedCount = 0;
    targets.packageLocks.forEach((file, index) => {
      const relativePath = path.relative(__dirname, file);
      process.stdout.write(`  [${index + 1}/${targets.packageLocks.length}] 删除 ${relativePath}...`);
      if (removeTarget(file)) {
        console.log(' ✅');
        deletedCount++;
      } else {
        console.log(' ❌');
      }
    });
    console.log(`删除了 ${deletedCount}/${targets.packageLocks.length} 个 package-lock.json 文件\n`);
  }
  
  // 4. 重新安装依赖
  console.log('📦 重新安装依赖...');
  const packageDirs = findPackageDirectories(serverDir);
  console.log(`找到 ${packageDirs.length} 个包含 package.json 的目录\n`);
  
  let installedCount = 0;
  let failedDirs = [];
  
  packageDirs.forEach((dir, index) => {
    const relativePath = path.relative(__dirname, dir);
    console.log(`[${index + 1}/${packageDirs.length}] 处理 ${relativePath}:`);
    
    if (installDependencies(dir)) {
      installedCount++;
    } else {
      failedDirs.push(relativePath);
    }
    console.log('');
  });
  
  // 5. 总结
  console.log('📊 清理和安装完成！');
  console.log(`总目录数: ${packageDirs.length}`);
  console.log(`安装成功: ${installedCount}`);
  console.log(`安装失败: ${failedDirs.length}`);
  
  if (failedDirs.length > 0) {
    console.log('\n❌ 安装失败的目录:');
    failedDirs.forEach(dir => {
      console.log(`  - ${dir}`);
    });
    console.log('\n💡 建议手动检查这些目录的依赖问题');
  } else {
    console.log('\n🎉 所有依赖安装成功！');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { findCleanupTargets, findPackageDirectories };
