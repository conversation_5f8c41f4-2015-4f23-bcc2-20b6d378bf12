/**
 * 实时协作同步引擎服务
 * 提供多用户实时协作、操作同步、冲突解决、状态管理等功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { WebSocketConnectionManager } from './WebSocketConnectionManager';

// 用户在线状态
export enum UserPresenceStatus {
  ONLINE = 'online',
  AWAY = 'away',
  BUSY = 'busy',
  OFFLINE = 'offline'
}

// 用户光标信息
export interface UserCursor {
  userId: string;
  x: number;
  y: number;
  entityId?: string;
  componentId?: string;
  propertyPath?: string;
  timestamp: number;
}

// 用户选择信息
export interface UserSelection {
  userId: string;
  entityIds: string[];
  timestamp: number;
}

// 编辑区域信息
export interface EditingZone {
  id: string;
  userId: string;
  entityId: string;
  componentId?: string;
  propertyPath?: string;
  startTime: number;
  lastActivity: number;
  isActive: boolean;
}

// 实时协作用户信息
export interface RealtimeUser {
  id: string;
  name: string;
  avatar?: string;
  color: string;
  role: string;
  status: UserPresenceStatus;
  lastSeen: number;
  cursor?: UserCursor;
  selection?: UserSelection;
  editingZones: EditingZone[];
  isTyping: boolean;
  typingLocation?: string;
}

// 实时消息类型
export enum RealtimeMessageType {
  // 用户状态相关
  USER_JOIN = 'user_join',
  USER_LEAVE = 'user_leave',
  USER_STATUS_UPDATE = 'user_status_update',
  USER_LIST = 'user_list',
  
  // 光标和选择相关
  CURSOR_UPDATE = 'cursor_update',
  SELECTION_UPDATE = 'selection_update',
  
  // 编辑区域相关
  EDITING_ZONE_ENTER = 'editing_zone_enter',
  EDITING_ZONE_EXIT = 'editing_zone_exit',
  EDITING_ZONE_UPDATE = 'editing_zone_update',
  
  // 实时编辑相关
  TYPING_START = 'typing_start',
  TYPING_STOP = 'typing_stop',
  TYPING_UPDATE = 'typing_update',
  
  // 操作相关
  OPERATION = 'operation',
  OPERATION_ACK = 'operation_ack',
  OPERATION_CONFLICT = 'operation_conflict',
  
  // 系统消息
  HEARTBEAT = 'heartbeat',
  ERROR = 'error',
  NOTIFICATION = 'notification'
}

// 实时消息接口
export interface RealtimeMessage {
  type: RealtimeMessageType;
  data: any;
  timestamp: number;
  messageId: string;
  userId?: string;
}

/**
 * 实时协作服务类
 */
export class RealtimeCollaborationService extends EventEmitter {
  private connectionManager: WebSocketConnectionManager | null = null;
  private currentUser: RealtimeUser | null = null;
  private users: Map<string, RealtimeUser> = new Map();
  private editingZones: Map<string, EditingZone> = new Map();
  private messageQueue: RealtimeMessage[] = [];
  private isConnected: boolean = false;
  private roomId: string = '';
  
  // 心跳相关
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastHeartbeat: number = 0;
  
  // 状态同步相关
  private statusSyncInterval: NodeJS.Timeout | null = null;
  private cursorUpdateThrottle: NodeJS.Timeout | null = null;
  
  constructor() {
    super();
    this.setupEventHandlers();
  }

  /**
   * 初始化服务
   */
  public async initialize(serverUrl: string, roomId: string, user: Partial<RealtimeUser>): Promise<void> {
    this.roomId = roomId;
    this.currentUser = {
      id: user.id || '',
      name: user.name || '',
      avatar: user.avatar,
      color: user.color || this.generateUserColor(),
      role: user.role || 'editor',
      status: UserPresenceStatus.ONLINE,
      lastSeen: Date.now(),
      editingZones: [],
      isTyping: false
    };

    // 创建连接管理器
    this.connectionManager = new WebSocketConnectionManager();
    
    // 设置连接事件监听
    this.connectionManager.on('connected', this.handleConnected.bind(this));
    this.connectionManager.on('disconnected', this.handleDisconnected.bind(this));
    this.connectionManager.on('message', this.handleMessage.bind(this));
    this.connectionManager.on('error', this.handleError.bind(this));

    // 初始化连接管理器
    this.connectionManager.initialize(serverUrl, {
      room: roomId,
      user: this.currentUser.id
    });

    // 连接到服务器
    this.connectionManager.connect();
  }

  /**
   * 获取当前房间ID
   */
  public getRoomId(): string {
    return this.roomId;
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    this.cleanup();
    if (this.connectionManager) {
      this.connectionManager.disconnect();
      this.connectionManager = null;
    }
  }

  /**
   * 发送消息
   */
  public sendMessage(type: RealtimeMessageType, data: any): void {
    if (!this.isConnected || !this.connectionManager) {
      this.queueMessage(type, data);
      return;
    }

    const message: RealtimeMessage = {
      type,
      data,
      timestamp: Date.now(),
      messageId: this.generateMessageId(),
      userId: this.currentUser?.id
    };

    this.connectionManager.send('operation', message);
  }

  /**
   * 更新用户光标位置
   */
  public updateCursor(x: number, y: number, entityId?: string, componentId?: string, propertyPath?: string): void {
    if (!this.currentUser) return;

    const cursor: UserCursor = {
      userId: this.currentUser.id,
      x,
      y,
      entityId,
      componentId,
      propertyPath,
      timestamp: Date.now()
    };

    this.currentUser.cursor = cursor;

    // 节流发送光标更新
    if (this.cursorUpdateThrottle) {
      clearTimeout(this.cursorUpdateThrottle);
    }

    this.cursorUpdateThrottle = setTimeout(() => {
      this.sendMessage(RealtimeMessageType.CURSOR_UPDATE, cursor);
    }, 50); // 50ms节流
  }

  /**
   * 更新用户选择
   */
  public updateSelection(entityIds: string[]): void {
    if (!this.currentUser) return;

    const selection: UserSelection = {
      userId: this.currentUser.id,
      entityIds,
      timestamp: Date.now()
    };

    this.currentUser.selection = selection;
    this.sendMessage(RealtimeMessageType.SELECTION_UPDATE, selection);
  }

  /**
   * 进入编辑区域
   */
  public enterEditingZone(entityId: string, componentId?: string, propertyPath?: string): void {
    if (!this.currentUser) return;

    const zoneId = `${entityId}_${componentId || ''}_${propertyPath || ''}`;
    const zone: EditingZone = {
      id: zoneId,
      userId: this.currentUser.id,
      entityId,
      componentId,
      propertyPath,
      startTime: Date.now(),
      lastActivity: Date.now(),
      isActive: true
    };

    this.editingZones.set(zoneId, zone);
    this.currentUser.editingZones.push(zone);

    this.sendMessage(RealtimeMessageType.EDITING_ZONE_ENTER, zone);
  }

  /**
   * 退出编辑区域
   */
  public exitEditingZone(entityId: string, componentId?: string, propertyPath?: string): void {
    if (!this.currentUser) return;

    const zoneId = `${entityId}_${componentId || ''}_${propertyPath || ''}`;
    const zone = this.editingZones.get(zoneId);
    
    if (zone && zone.userId === this.currentUser.id) {
      zone.isActive = false;
      this.editingZones.delete(zoneId);
      
      // 从用户的编辑区域列表中移除
      this.currentUser.editingZones = this.currentUser.editingZones.filter(z => z.id !== zoneId);
      
      this.sendMessage(RealtimeMessageType.EDITING_ZONE_EXIT, { zoneId });
    }
  }

  /**
   * 开始输入
   */
  public startTyping(location: string): void {
    if (!this.currentUser || this.currentUser.isTyping) return;

    this.currentUser.isTyping = true;
    this.currentUser.typingLocation = location;

    this.sendMessage(RealtimeMessageType.TYPING_START, { location });
  }

  /**
   * 停止输入
   */
  public stopTyping(): void {
    if (!this.currentUser || !this.currentUser.isTyping) return;

    this.currentUser.isTyping = false;
    this.currentUser.typingLocation = undefined;

    this.sendMessage(RealtimeMessageType.TYPING_STOP, {});
  }

  /**
   * 获取在线用户列表
   */
  public getOnlineUsers(): RealtimeUser[] {
    return Array.from(this.users.values()).filter(user => user.status !== UserPresenceStatus.OFFLINE);
  }

  /**
   * 获取用户信息
   */
  public getUser(userId: string): RealtimeUser | null {
    return this.users.get(userId) || null;
  }

  /**
   * 获取当前用户
   */
  public getCurrentUser(): RealtimeUser | null {
    return this.currentUser;
  }

  /**
   * 检查编辑区域是否被占用
   */
  public isEditingZoneOccupied(entityId: string, componentId?: string, propertyPath?: string): boolean {
    const zoneId = `${entityId}_${componentId || ''}_${propertyPath || ''}`;
    const zone = this.editingZones.get(zoneId);
    return zone ? zone.isActive && zone.userId !== this.currentUser?.id : false;
  }

  /**
   * 获取编辑区域的占用者
   */
  public getEditingZoneOccupant(entityId: string, componentId?: string, propertyPath?: string): RealtimeUser | null {
    const zoneId = `${entityId}_${componentId || ''}_${propertyPath || ''}`;
    const zone = this.editingZones.get(zoneId);
    return zone ? this.users.get(zone.userId) || null : null;
  }

  // 私有方法

  private setupEventHandlers(): void {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.updateUserStatus(UserPresenceStatus.AWAY);
      } else {
        this.updateUserStatus(UserPresenceStatus.ONLINE);
      }
    });

    // 监听页面卸载
    window.addEventListener('beforeunload', () => {
      this.disconnect();
    });
  }

  private handleConnected(): void {
    this.isConnected = true;
    this.startHeartbeat();
    this.startStatusSync();
    this.processMessageQueue();
    
    // 发送用户加入消息
    if (this.currentUser) {
      this.sendMessage(RealtimeMessageType.USER_JOIN, this.currentUser);
    }

    this.emit('connected');
  }

  private handleDisconnected(): void {
    this.isConnected = false;
    this.cleanup();
    this.emit('disconnected');
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: RealtimeMessage = JSON.parse(event.data);
      this.processMessage(message);
    } catch (error) {
      console.error('Failed to parse message:', error);
    }
  }

  private handleError(error: Event): void {
    console.error('WebSocket error:', error);
    this.emit('error', error);
  }

  private processMessage(message: RealtimeMessage): void {
    switch (message.type) {
      case RealtimeMessageType.USER_JOIN:
        this.handleUserJoin(message.data);
        break;
      case RealtimeMessageType.USER_LEAVE:
        this.handleUserLeave(message.data);
        break;
      case RealtimeMessageType.USER_LIST:
        this.handleUserList(message.data);
        break;
      case RealtimeMessageType.CURSOR_UPDATE:
        this.handleCursorUpdate(message.data);
        break;
      case RealtimeMessageType.SELECTION_UPDATE:
        this.handleSelectionUpdate(message.data);
        break;
      case RealtimeMessageType.EDITING_ZONE_ENTER:
        this.handleEditingZoneEnter(message.data);
        break;
      case RealtimeMessageType.EDITING_ZONE_EXIT:
        this.handleEditingZoneExit(message.data);
        break;
      case RealtimeMessageType.TYPING_START:
        this.handleTypingStart(message.data, message.userId);
        break;
      case RealtimeMessageType.TYPING_STOP:
        this.handleTypingStop(message.userId);
        break;
      default:
        this.emit('message', message);
    }
  }

  private handleUserJoin(user: RealtimeUser): void {
    this.users.set(user.id, user);
    this.emit('userJoin', user);
  }

  private handleUserLeave(data: { userId: string }): void {
    const user = this.users.get(data.userId);
    if (user) {
      this.users.delete(data.userId);
      this.emit('userLeave', user);
    }
  }

  private handleUserList(users: RealtimeUser[]): void {
    this.users.clear();
    users.forEach(user => this.users.set(user.id, user));
    this.emit('userListUpdate', users);
  }

  private handleCursorUpdate(cursor: UserCursor): void {
    const user = this.users.get(cursor.userId);
    if (user) {
      user.cursor = cursor;
      this.emit('cursorUpdate', cursor);
    }
  }

  private handleSelectionUpdate(selection: UserSelection): void {
    const user = this.users.get(selection.userId);
    if (user) {
      user.selection = selection;
      this.emit('selectionUpdate', selection);
    }
  }

  private handleEditingZoneEnter(zone: EditingZone): void {
    this.editingZones.set(zone.id, zone);
    const user = this.users.get(zone.userId);
    if (user) {
      user.editingZones.push(zone);
    }
    this.emit('editingZoneEnter', zone);
  }

  private handleEditingZoneExit(data: { zoneId: string }): void {
    const zone = this.editingZones.get(data.zoneId);
    if (zone) {
      this.editingZones.delete(data.zoneId);
      const user = this.users.get(zone.userId);
      if (user) {
        user.editingZones = user.editingZones.filter(z => z.id !== data.zoneId);
      }
      this.emit('editingZoneExit', zone);
    }
  }

  private handleTypingStart(data: { location: string }, userId?: string): void {
    if (!userId) return;
    const user = this.users.get(userId);
    if (user) {
      user.isTyping = true;
      user.typingLocation = data.location;
      this.emit('typingStart', { user, location: data.location });
    }
  }

  private handleTypingStop(userId?: string): void {
    if (!userId) return;
    const user = this.users.get(userId);
    if (user) {
      user.isTyping = false;
      user.typingLocation = undefined;
      this.emit('typingStop', user);
    }
  }

  private updateUserStatus(status: UserPresenceStatus): void {
    if (!this.currentUser) return;
    
    this.currentUser.status = status;
    this.currentUser.lastSeen = Date.now();
    
    this.sendMessage(RealtimeMessageType.USER_STATUS_UPDATE, {
      status,
      lastSeen: this.currentUser.lastSeen
    });
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.sendMessage(RealtimeMessageType.HEARTBEAT, { timestamp: Date.now() });
      this.lastHeartbeat = Date.now();
    }, 30000); // 30秒心跳
  }

  private startStatusSync(): void {
    this.statusSyncInterval = setInterval(() => {
      if (this.currentUser) {
        this.currentUser.lastSeen = Date.now();
        this.sendMessage(RealtimeMessageType.USER_STATUS_UPDATE, {
          status: this.currentUser.status,
          lastSeen: this.currentUser.lastSeen
        });
      }
    }, 60000); // 1分钟同步一次状态
  }

  private cleanup(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.statusSyncInterval) {
      clearInterval(this.statusSyncInterval);
      this.statusSyncInterval = null;
    }
    
    if (this.cursorUpdateThrottle) {
      clearTimeout(this.cursorUpdateThrottle);
      this.cursorUpdateThrottle = null;
    }
  }

  private queueMessage(type: RealtimeMessageType, data: any): void {
    if (this.messageQueue.length >= 100) {
      this.messageQueue.shift(); // 移除最旧的消息
    }
    
    this.messageQueue.push({
      type,
      data,
      timestamp: Date.now(),
      messageId: this.generateMessageId(),
      userId: this.currentUser?.id
    });
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message && this.connectionManager) {
        this.connectionManager.send(message.type, message.data);
      }
    }
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateUserColor(): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  // ========== 扩展的协作功能 ==========

  /**
   * 操作转换引擎
   */
  public transformOperation(localOp: any, remoteOp: any): any {
    // 实现操作转换算法
    if (localOp.type === remoteOp.type && localOp.targetId === remoteOp.targetId) {
      // 处理并发操作冲突
      return this.resolveOperationConflict(localOp, remoteOp);
    }

    // 如果操作不冲突，直接返回本地操作
    return localOp;
  }

  /**
   * 解决操作冲突
   */
  private resolveOperationConflict(localOp: any, remoteOp: any): any {
    // 基于时间戳的冲突解决
    if (localOp.timestamp < remoteOp.timestamp) {
      // 本地操作较早，保持本地操作
      return localOp;
    } else if (localOp.timestamp > remoteOp.timestamp) {
      // 远程操作较早，采用远程操作
      return remoteOp;
    } else {
      // 时间戳相同，基于用户ID解决
      return localOp.userId < remoteOp.userId ? localOp : remoteOp;
    }
  }

  /**
   * 批量应用操作
   */
  public async applyOperationBatch(operations: any[]): Promise<void> {
    for (const operation of operations) {
      try {
        await this.applyOperation(operation);
      } catch (error) {
        console.error('Failed to apply operation:', error);
        this.emit('operationError', { operation, error });
      }
    }
  }

  /**
   * 应用单个操作
   */
  private async applyOperation(operation: any): Promise<void> {
    // 根据操作类型执行相应的操作
    switch (operation.type) {
      case 'object_create':
        await this.applyObjectCreate(operation);
        break;
      case 'object_update':
        await this.applyObjectUpdate(operation);
        break;
      case 'object_delete':
        await this.applyObjectDelete(operation);
        break;
      case 'property_update':
        await this.applyPropertyUpdate(operation);
        break;
      default:
        console.warn('Unknown operation type:', operation.type);
    }
  }

  /**
   * 应用对象创建操作
   */
  private async applyObjectCreate(operation: any): Promise<void> {
    // 实现对象创建逻辑
    this.emit('objectCreated', operation.data);
  }

  /**
   * 应用对象更新操作
   */
  private async applyObjectUpdate(operation: any): Promise<void> {
    // 实现对象更新逻辑
    this.emit('objectUpdated', operation.data);
  }

  /**
   * 应用对象删除操作
   */
  private async applyObjectDelete(operation: any): Promise<void> {
    // 实现对象删除逻辑
    this.emit('objectDeleted', operation.data);
  }

  /**
   * 应用属性更新操作
   */
  private async applyPropertyUpdate(operation: any): Promise<void> {
    // 实现属性更新逻辑
    this.emit('propertyUpdated', operation.data);
  }

  /**
   * 创建操作
   */
  public createOperation(type: string, data: any): any {
    return {
      id: `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      userId: this.currentUser?.id,
      timestamp: Date.now(),
      version: 1
    };
  }

  /**
   * 发送操作到其他用户
   */
  public async sendOperation(operation: any): Promise<void> {
    if (!this.isConnected || !this.connectionManager) {
      // 如果未连接，将操作加入队列
      this.messageQueue.push({
        type: RealtimeMessageType.OPERATION,
        data: operation,
        timestamp: Date.now(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      });
      return;
    }

    try {
      this.connectionManager.send(RealtimeMessageType.OPERATION, {
        data: operation,
        timestamp: Date.now(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      });
    } catch (error) {
      console.error('Failed to send operation:', error);
      this.emit('operationSendError', { operation, error });
    }
  }

  /**
   * 获取协作统计信息
   */
  public getCollaborationStats(): {
    connectedUsers: number;
    activeEditingZones: number;
    messageQueueSize: number;
    isConnected: boolean;
    lastHeartbeat: number;
  } {
    return {
      connectedUsers: this.users.size,
      activeEditingZones: Array.from(this.editingZones.values()).filter(zone => zone.isActive).length,
      messageQueueSize: this.messageQueue.length,
      isConnected: this.isConnected,
      lastHeartbeat: this.lastHeartbeat
    };
  }

  /**
   * 设置用户权限
   */
  public setUserPermissions(userId: string, permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canShare: boolean;
    canManage: boolean;
  }): void {
    const user = this.users.get(userId);
    if (user) {
      (user as any).permissions = permissions;
      this.emit('userPermissionsUpdated', { userId, permissions });
    }
  }

  /**
   * 检查用户权限
   */
  public checkUserPermission(userId: string, permission: string): boolean {
    const user = this.users.get(userId);
    if (!user) return false;

    const permissions = (user as any).permissions;
    return permissions ? permissions[permission] : false;
  }

  /**
   * 获取用户活动历史
   */
  public getUserActivityHistory(_userId: string, _limit: number = 50): any[] {
    // 这里应该从存储中获取用户活动历史
    // 暂时返回空数组
    return [];
  }

  /**
   * 清理非活跃用户
   */
  public cleanupInactiveUsers(): void {
    const now = Date.now();
    const inactiveThreshold = 5 * 60 * 1000; // 5分钟

    for (const [userId, user] of this.users.entries()) {
      if (now - user.lastSeen > inactiveThreshold) {
        this.users.delete(userId);
        this.emit('userInactive', user);
      }
    }
  }

  /**
   * 强制同步状态
   */
  public async forceSync(): Promise<void> {
    if (!this.isConnected || !this.connectionManager) {
      throw new Error('Not connected to collaboration server');
    }

    try {
      this.connectionManager.send(RealtimeMessageType.USER_STATUS_UPDATE, {
        user: this.currentUser,
        forceSync: true,
        timestamp: Date.now(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      });

      this.emit('syncForced');
    } catch (error) {
      console.error('Failed to force sync:', error);
      throw error;
    }
  }
}

export default RealtimeCollaborationService;
