/**
 * 视觉脚本节点注册表
 * 用于注册和管理节点类型
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Node, NodeCategory, NodeOptions } from './Node';

/**
 * 节点构造函数类型
 */
export type NodeConstructor = new (...args: any[]) => Node;

/**
 * 节点工厂函数类型
 */
export type NodeFactory = (options: NodeOptions) => Node;

/**
 * 节点验证器类型
 */
export type NodeValidator = (nodeInfo: NodeTypeInfo) => ValidationResult;

/**
 * 验证结果
 */
export interface ValidationResult {
  /** 是否有效 */
  valid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

/**
 * 节点过滤器选项
 */
export interface NodeFilterOptions {
  /** 类别过滤 */
  categories?: NodeCategory[];
  /** 标签过滤 */
  tags?: string[];
  /** 是否包含已弃用节点 */
  includeDeprecated?: boolean;
  /** 是否包含实验性节点 */
  includeExperimental?: boolean;
  /** 版本过滤 */
  version?: string;
  /** 作者过滤 */
  author?: string;
  /** 自定义过滤函数 */
  customFilter?: (info: NodeTypeInfo) => boolean;
}

/**
 * 节点统计信息
 */
export interface NodeStatistics {
  /** 创建次数 */
  creationCount: number;
  /** 使用次数 */
  usageCount: number;
  /** 最后使用时间 */
  lastUsed: Date;
  /** 平均执行时间 */
  averageExecutionTime: number;
  /** 错误次数 */
  errorCount: number;
  /** 成功率 */
  successRate: number;
}

/**
 * 节点注册表选项
 */
export interface NodeRegistryOptions {
  /** 是否启用严格模式 */
  strictMode?: boolean;
  /** 注册表版本 */
  version?: string;
  /** 注册表名称 */
  name?: string;
  /** 全局验证器 */
  globalValidators?: NodeValidator[];
  /** 是否启用统计 */
  enableStatistics?: boolean;
  /** 是否启用缓存 */
  enableCache?: boolean;
}

/**
 * 节点类型信息
 */
export interface NodeTypeInfo {
  /** 节点类型名称 */
  type: string;
  /** 节点类别 */
  category: NodeCategory;
  /** 节点构造函数 */
  constructor: NodeConstructor;
  /** 节点工厂函数（可选，优先于构造函数） */
  factory?: NodeFactory;
  /** 节点描述 */
  description?: string;
  /** 节点标签 */
  label?: string;
  /** 节点图标 */
  icon?: string;
  /** 节点颜色 */
  color?: string;
  /** 是否已弃用 */
  deprecated?: boolean;
  /** 弃用原因 */
  deprecatedReason?: string;
  /** 是否实验性 */
  experimental?: boolean;
  /** 标签列表 */
  tags?: string[];
  /** 示例代码 */
  examples?: string[];
  /** 文档链接 */
  documentationUrl?: string;
  /** 版本 */
  version?: string;
  /** 作者 */
  author?: string;
  /** 许可证 */
  license?: string;
  /** 依赖项 */
  dependencies?: string[];
  /** 最小引擎版本 */
  minEngineVersion?: string;
  /** 最大引擎版本 */
  maxEngineVersion?: string;
  /** 平台支持 */
  platforms?: string[];
  /** 性能等级（1-5，5为最高性能要求） */
  performanceLevel?: number;
  /** 内存使用等级（1-5，5为最高内存使用） */
  memoryLevel?: number;
  /** 自定义元数据 */
  [key: string]: any;
}

/**
 * 节点注册表
 * 用于注册和管理节点类型
 */
export class NodeRegistry extends EventEmitter {
  /** 节点类型映射 */
  private nodeTypes: Map<string, NodeTypeInfo> = new Map();

  /** 节点类别映射 */
  private categories: Map<NodeCategory, Set<string>> = new Map();

  /** 节点标签映射 */
  private tags: Map<string, Set<string>> = new Map();

  /** 节点验证器映射 */
  private validators: Map<string, NodeValidator[]> = new Map();

  /** 节点别名映射 */
  private aliases: Map<string, string> = new Map();

  /** 节点依赖关系映射 */
  private dependencies: Map<string, Set<string>> = new Map();

  /** 节点统计信息 */
  private statistics: Map<string, NodeStatistics> = new Map();

  /** 是否启用严格模式 */
  private strictMode: boolean = false;

  /** 注册表版本 */
  private version: string = '1.0.0';

  /** 注册表名称 */
  private name: string = 'DefaultNodeRegistry';

  /**
   * 创建节点注册表
   */
  constructor(options: NodeRegistryOptions = {}) {
    super();

    this.strictMode = options.strictMode || false;
    this.version = options.version || '1.0.0';
    this.name = options.name || 'DefaultNodeRegistry';

    // 初始化类别映射
    for (const category of Object.values(NodeCategory)) {
      this.categories.set(category, new Set());
    }

    // 初始化全局验证器
    if (options.globalValidators) {
      this.validators.set('*', options.globalValidators);
    }
  }
  
  /**
   * 注册节点类型
   * @param info 节点类型信息
   * @returns 是否注册成功
   */
  public registerNodeType(info: NodeTypeInfo): boolean {
    // 检查是否已存在
    if (this.nodeTypes.has(info.type)) {
      if (this.strictMode) {
        throw new Error(`节点类型已存在: ${info.type}`);
      }
      console.warn(`节点类型已存在: ${info.type}`);
      return false;
    }

    // 验证节点信息
    const validation = this.validateNodeInfo(info);
    if (!validation.valid) {
      if (this.strictMode) {
        throw new Error(`节点验证失败: ${validation.errors.join(', ')}`);
      }
      console.warn(`节点验证失败: ${info.type}`, validation.errors);
      return false;
    }

    // 检查依赖项
    if (info.dependencies) {
      for (const dep of info.dependencies) {
        if (!this.nodeTypes.has(dep)) {
          if (this.strictMode) {
            throw new Error(`依赖的节点类型不存在: ${dep}`);
          }
          console.warn(`依赖的节点类型不存在: ${dep}`);
        }
      }
    }

    // 注册节点类型
    this.nodeTypes.set(info.type, info);

    // 添加到类别映射
    const categorySet = this.categories.get(info.category) || new Set();
    categorySet.add(info.type);
    this.categories.set(info.category, categorySet);

    // 添加到标签映射
    if (info.tags) {
      for (const tag of info.tags) {
        const tagSet = this.tags.get(tag) || new Set();
        tagSet.add(info.type);
        this.tags.set(tag, tagSet);
      }
    }

    // 添加依赖关系
    if (info.dependencies) {
      this.dependencies.set(info.type, new Set(info.dependencies));
    }

    // 初始化统计信息
    this.statistics.set(info.type, {
      creationCount: 0,
      usageCount: 0,
      lastUsed: new Date(),
      averageExecutionTime: 0,
      errorCount: 0,
      successRate: 1.0
    });

    // 触发注册事件
    this.emit('nodeTypeRegistered', info);

    return true;
  }
  
  /**
   * 注销节点类型
   * @param type 节点类型名称
   * @returns 是否注销成功
   */
  public unregisterNodeType(type: string): boolean {
    // 检查是否存在
    const info = this.nodeTypes.get(type);
    
    if (!info) {
      return false;
    }
    
    // 从类别映射中移除
    const categorySet = this.categories.get(info.category);
    
    if (categorySet) {
      categorySet.delete(type);
    }
    
    // 从标签映射中移除
    if (info.tags) {
      for (const tag of info.tags) {
        const tagSet = this.tags.get(tag);
        
        if (tagSet) {
          tagSet.delete(type);
          
          // 如果标签集合为空，移除标签
          if (tagSet.size === 0) {
            this.tags.delete(tag);
          }
        }
      }
    }
    
    // 从节点类型映射中移除
    this.nodeTypes.delete(type);
    
    // 触发注销事件
    this.emit('nodeTypeUnregistered', info);
    
    return true;
  }
  
  /**
   * 获取节点类型
   * @param type 节点类型名称
   * @returns 节点构造函数
   */
  public getNodeType(type: string): NodeConstructor | undefined {
    const info = this.nodeTypes.get(type);
    return info ? info.constructor : undefined;
  }
  
  /**
   * 获取节点类型信息
   * @param type 节点类型名称
   * @returns 节点类型信息
   */
  public getNodeTypeInfo(type: string): NodeTypeInfo | undefined {
    return this.nodeTypes.get(type);
  }
  
  /**
   * 获取所有节点类型
   * @returns 节点类型信息列表
   */
  public getAllNodeTypes(): NodeTypeInfo[] {
    return Array.from(this.nodeTypes.values());
  }
  
  /**
   * 获取指定类别的节点类型
   * @param category 节点类别
   * @returns 节点类型信息列表
   */
  public getNodeTypesByCategory(category: NodeCategory): NodeTypeInfo[] {
    const categorySet = this.categories.get(category);
    
    if (!categorySet) {
      return [];
    }
    
    return Array.from(categorySet).map(type => this.nodeTypes.get(type)!);
  }
  
  /**
   * 获取指定标签的节点类型
   * @param tag 标签
   * @returns 节点类型信息列表
   */
  public getNodeTypesByTag(tag: string): NodeTypeInfo[] {
    const tagSet = this.tags.get(tag);
    
    if (!tagSet) {
      return [];
    }
    
    return Array.from(tagSet).map(type => this.nodeTypes.get(type)!);
  }
  
  /**
   * 获取所有类别
   * @returns 类别列表
   */
  public getAllCategories(): NodeCategory[] {
    return Array.from(this.categories.keys());
  }
  
  /**
   * 获取所有标签
   * @returns 标签列表
   */
  public getAllTags(): string[] {
    return Array.from(this.tags.keys());
  }
  
  /**
   * 搜索节点类型
   * @param query 搜索查询
   * @param options 过滤选项
   * @returns 节点类型信息列表
   */
  public searchNodeTypes(query: string, options?: NodeFilterOptions): NodeTypeInfo[] {
    let results = this.getAllNodeTypes();

    // 应用过滤器
    if (options) {
      results = this.filterNodeTypes(results, options);
    }

    if (!query) {
      return results;
    }

    const lowerQuery = query.toLowerCase();

    return results.filter(info => {
      // 匹配类型名称
      if (info.type.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      // 匹配标签
      if (info.label && info.label.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      // 匹配描述
      if (info.description && info.description.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      // 匹配标签
      if (info.tags && info.tags.some(tag => tag.toLowerCase().includes(lowerQuery))) {
        return true;
      }

      return false;
    });
  }

  /**
   * 过滤节点类型
   * @param nodeTypes 节点类型列表
   * @param options 过滤选项
   * @returns 过滤后的节点类型列表
   */
  private filterNodeTypes(nodeTypes: NodeTypeInfo[], options: NodeFilterOptions): NodeTypeInfo[] {
    return nodeTypes.filter(info => {
      // 类别过滤
      if (options.categories && !options.categories.includes(info.category)) {
        return false;
      }

      // 标签过滤
      if (options.tags && options.tags.length > 0) {
        if (!info.tags || !options.tags.some(tag => info.tags!.includes(tag))) {
          return false;
        }
      }

      // 弃用节点过滤
      if (!options.includeDeprecated && info.deprecated) {
        return false;
      }

      // 实验性节点过滤
      if (!options.includeExperimental && info.experimental) {
        return false;
      }

      // 版本过滤
      if (options.version && info.version !== options.version) {
        return false;
      }

      // 作者过滤
      if (options.author && info.author !== options.author) {
        return false;
      }

      // 自定义过滤
      if (options.customFilter && !options.customFilter(info)) {
        return false;
      }

      return true;
    });
  }
  
  /**
   * 验证节点信息
   * @param info 节点类型信息
   * @returns 验证结果
   */
  private validateNodeInfo(info: NodeTypeInfo): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证必需字段
    if (!info.type || info.type.trim() === '') {
      errors.push('节点类型名称不能为空');
    }

    if (!info.constructor && !info.factory) {
      errors.push('必须提供构造函数或工厂函数');
    }

    if (!info.category) {
      errors.push('必须指定节点类别');
    }

    // 验证类型名称格式
    if (info.type && !/^[a-zA-Z][a-zA-Z0-9_/.-]*$/.test(info.type)) {
      errors.push('节点类型名称格式无效');
    }

    // 验证版本格式
    if (info.version && !/^\d+\.\d+\.\d+/.test(info.version)) {
      warnings.push('版本号格式建议使用语义化版本');
    }

    // 验证依赖项
    if (info.dependencies) {
      for (const dep of info.dependencies) {
        if (!dep || dep.trim() === '') {
          errors.push('依赖项名称不能为空');
        }
      }
    }

    // 运行自定义验证器
    const typeValidators = this.validators.get(info.type) || [];
    const globalValidators = this.validators.get('*') || [];

    for (const validator of [...globalValidators, ...typeValidators]) {
      const result = validator(info);
      errors.push(...result.errors);
      warnings.push(...result.warnings);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 添加节点验证器
   * @param type 节点类型（使用 '*' 表示全局验证器）
   * @param validator 验证器函数
   */
  public addValidator(type: string, validator: NodeValidator): void {
    const validators = this.validators.get(type) || [];
    validators.push(validator);
    this.validators.set(type, validators);
  }

  /**
   * 移除节点验证器
   * @param type 节点类型
   * @param validator 验证器函数
   */
  public removeValidator(type: string, validator: NodeValidator): void {
    const validators = this.validators.get(type) || [];
    const index = validators.indexOf(validator);
    if (index !== -1) {
      validators.splice(index, 1);
      this.validators.set(type, validators);
    }
  }

  /**
   * 创建节点实例
   * @param type 节点类型
   * @param options 节点选项
   * @returns 节点实例
   */
  public createNode(type: string, options: NodeOptions): Node | null {
    const info = this.nodeTypes.get(type);
    if (!info) {
      console.warn(`未找到节点类型: ${type}`);
      return null;
    }

    try {
      let node: Node;

      // 优先使用工厂函数
      if (info.factory) {
        node = info.factory(options);
      } else {
        node = new info.constructor(options);
      }

      // 更新统计信息
      const stats = this.statistics.get(type);
      if (stats) {
        stats.creationCount++;
        stats.lastUsed = new Date();
      }

      this.emit('nodeCreated', type, node);
      return node;
    } catch (error) {
      console.error(`创建节点失败: ${type}`, error);

      // 更新错误统计
      const stats = this.statistics.get(type);
      if (stats) {
        stats.errorCount++;
        stats.successRate = stats.creationCount / (stats.creationCount + stats.errorCount);
      }

      this.emit('nodeCreationFailed', type, error);
      return null;
    }
  }

  /**
   * 添加节点别名
   * @param alias 别名
   * @param type 实际节点类型
   */
  public addAlias(alias: string, type: string): void {
    if (!this.nodeTypes.has(type)) {
      throw new Error(`节点类型不存在: ${type}`);
    }
    this.aliases.set(alias, type);
    this.emit('aliasAdded', alias, type);
  }

  /**
   * 移除节点别名
   * @param alias 别名
   */
  public removeAlias(alias: string): void {
    this.aliases.delete(alias);
    this.emit('aliasRemoved', alias);
  }

  /**
   * 解析节点类型（处理别名）
   * @param typeOrAlias 节点类型或别名
   * @returns 实际节点类型
   */
  public resolveType(typeOrAlias: string): string {
    return this.aliases.get(typeOrAlias) || typeOrAlias;
  }

  /**
   * 获取节点统计信息
   * @param type 节点类型
   * @returns 统计信息
   */
  public getStatistics(type: string): NodeStatistics | undefined {
    return this.statistics.get(type);
  }

  /**
   * 获取所有统计信息
   * @returns 统计信息映射
   */
  public getAllStatistics(): Map<string, NodeStatistics> {
    return new Map(this.statistics);
  }

  /**
   * 重置统计信息
   * @param type 节点类型（可选，不提供则重置所有）
   */
  public resetStatistics(type?: string): void {
    if (type) {
      const stats = this.statistics.get(type);
      if (stats) {
        stats.creationCount = 0;
        stats.usageCount = 0;
        stats.averageExecutionTime = 0;
        stats.errorCount = 0;
        stats.successRate = 1.0;
        stats.lastUsed = new Date();
      }
    } else {
      for (const stats of this.statistics.values()) {
        stats.creationCount = 0;
        stats.usageCount = 0;
        stats.averageExecutionTime = 0;
        stats.errorCount = 0;
        stats.successRate = 1.0;
        stats.lastUsed = new Date();
      }
    }
    this.emit('statisticsReset', type);
  }

  /**
   * 清空注册表
   */
  public clear(): void {
    // 清空节点类型映射
    this.nodeTypes.clear();

    // 清空类别映射
    for (const category of Object.values(NodeCategory)) {
      this.categories.set(category, new Set());
    }

    // 清空标签映射
    this.tags.clear();

    // 清空验证器
    this.validators.clear();

    // 清空别名
    this.aliases.clear();

    // 清空依赖关系
    this.dependencies.clear();

    // 清空统计信息
    this.statistics.clear();

    // 触发清空事件
    this.emit('cleared');
  }

  /**
   * 检查节点类型是否存在
   * @param type 节点类型
   * @returns 是否存在
   */
  public hasNodeType(type: string): boolean {
    return this.nodeTypes.has(type) || this.aliases.has(type);
  }

  /**
   * 获取节点依赖关系
   * @param type 节点类型
   * @returns 依赖的节点类型集合
   */
  public getDependencies(type: string): Set<string> {
    return this.dependencies.get(type) || new Set();
  }

  /**
   * 获取依赖于指定节点的节点类型
   * @param type 节点类型
   * @returns 依赖于该节点的类型列表
   */
  public getDependents(type: string): string[] {
    const dependents: string[] = [];
    for (const [nodeType, deps] of this.dependencies.entries()) {
      if (deps.has(type)) {
        dependents.push(nodeType);
      }
    }
    return dependents;
  }

  /**
   * 检查节点类型兼容性
   * @param type 节点类型
   * @param engineVersion 引擎版本
   * @param platform 平台
   * @returns 是否兼容
   */
  public isCompatible(type: string, engineVersion?: string, platform?: string): boolean {
    const info = this.nodeTypes.get(type);
    if (!info) {
      return false;
    }

    // 检查引擎版本兼容性
    if (engineVersion) {
      if (info.minEngineVersion && this.compareVersions(engineVersion, info.minEngineVersion) < 0) {
        return false;
      }
      if (info.maxEngineVersion && this.compareVersions(engineVersion, info.maxEngineVersion) > 0) {
        return false;
      }
    }

    // 检查平台兼容性
    if (platform && info.platforms && !info.platforms.includes(platform)) {
      return false;
    }

    return true;
  }

  /**
   * 比较版本号
   * @param version1 版本1
   * @param version2 版本2
   * @returns 比较结果（-1: version1 < version2, 0: 相等, 1: version1 > version2）
   */
  private compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);

    const maxLength = Math.max(v1Parts.length, v2Parts.length);

    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part < v2Part) return -1;
      if (v1Part > v2Part) return 1;
    }

    return 0;
  }

  /**
   * 导出注册表配置
   * @returns 注册表配置JSON
   */
  public exportConfig(): any {
    const config = {
      version: this.version,
      name: this.name,
      strictMode: this.strictMode,
      nodeTypes: Array.from(this.nodeTypes.entries()).map(([type, info]) => ({
        type,
        category: info.category,
        description: info.description,
        label: info.label,
        icon: info.icon,
        color: info.color,
        deprecated: info.deprecated,
        deprecatedReason: info.deprecatedReason,
        experimental: info.experimental,
        tags: info.tags,
        examples: info.examples,
        documentationUrl: info.documentationUrl,
        version: info.version,
        author: info.author,
        license: info.license,
        dependencies: info.dependencies,
        minEngineVersion: info.minEngineVersion,
        maxEngineVersion: info.maxEngineVersion,
        platforms: info.platforms,
        performanceLevel: info.performanceLevel,
        memoryLevel: info.memoryLevel
      })),
      aliases: Array.from(this.aliases.entries()),
      statistics: Array.from(this.statistics.entries())
    };

    return config;
  }

  /**
   * 导入注册表配置
   * @param config 注册表配置JSON
   * @param merge 是否合并（默认为false，即清空后导入）
   */
  public importConfig(config: any, merge: boolean = false): void {
    if (!merge) {
      this.clear();
    }

    if (config.version) this.version = config.version;
    if (config.name) this.name = config.name;
    if (config.strictMode !== undefined) this.strictMode = config.strictMode;

    // 导入别名
    if (config.aliases) {
      for (const [alias, type] of config.aliases) {
        this.aliases.set(alias, type);
      }
    }

    // 导入统计信息
    if (config.statistics) {
      for (const [type, stats] of config.statistics) {
        this.statistics.set(type, {
          ...stats,
          lastUsed: new Date(stats.lastUsed)
        });
      }
    }

    this.emit('configImported', config);
  }

  /**
   * 获取注册表信息
   * @returns 注册表信息
   */
  public getRegistryInfo(): any {
    return {
      name: this.name,
      version: this.version,
      strictMode: this.strictMode,
      nodeTypeCount: this.nodeTypes.size,
      categoryCount: this.categories.size,
      tagCount: this.tags.size,
      aliasCount: this.aliases.size,
      validatorCount: Array.from(this.validators.values()).reduce((sum, validators) => sum + validators.length, 0)
    };
  }

  /**
   * 克隆注册表
   * @param name 新注册表名称
   * @returns 克隆的注册表
   */
  public clone(name?: string): NodeRegistry {
    const cloned = new NodeRegistry({
      name: name || `${this.name}_clone`,
      version: this.version,
      strictMode: this.strictMode
    });

    // 复制节点类型
    for (const [, info] of this.nodeTypes.entries()) {
      cloned.registerNodeType({ ...info });
    }

    // 复制别名
    for (const [alias, type] of this.aliases.entries()) {
      cloned.addAlias(alias, type);
    }

    return cloned;
  }

  /**
   * 简化的注册方法，兼容旧版本API
   * @param type 节点类型名称
   * @param constructor 节点构造函数
   */
  public register(type: string, constructor: NodeConstructor): void {
    // 从类型名称推断类别
    const category = this.inferCategoryFromType(type);

    const info: NodeTypeInfo = {
      type,
      category,
      constructor,
      description: `${type} 节点`,
      label: type.split('/').pop() || type
    };

    this.registerNodeType(info);
  }

  /**
   * 从类型名称推断节点类别
   * @param type 节点类型名称
   * @returns 节点类别
   */
  private inferCategoryFromType(type: string): NodeCategory {
    const typePrefix = type.split('/')[0].toLowerCase();

    switch (typePrefix) {
      case 'core':
        return NodeCategory.FLOW;
      case 'math':
        return NodeCategory.MATH;
      case 'debug':
        return NodeCategory.DEBUG;
      case 'ai':
        return NodeCategory.AI;
      case 'network':
        return NodeCategory.NETWORK;
      case 'ui':
        return NodeCategory.UI;
      case 'audio':
        return NodeCategory.AUDIO;
      case 'animation':
        return NodeCategory.ANIMATION;
      case 'physics':
        return NodeCategory.PHYSICS;
      case 'avatar':
      case 'medical':
      case 'industrial':
        return NodeCategory.INDUSTRIAL;
      default:
        return NodeCategory.CUSTOM;
    }
  }

  /**
   * 获取已注册节点数量
   * @returns 节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.nodeTypes.size;
  }

  /**
   * 根据类别获取节点类型名称列表
   * @param category 类别名称
   * @returns 节点类型名称列表
   */
  public getNodesByCategory(category: string): string[] {
    const result: string[] = [];

    for (const [type, info] of this.nodeTypes.entries()) {
      if (info.category.toString().toLowerCase() === category.toLowerCase()) {
        result.push(type);
      }
    }

    return result;
  }

  /**
   * 获取所有已注册的类别名称
   * @returns 类别名称列表
   */
  public getRegisteredCategories(): string[] {
    const categories = new Set<string>();

    for (const info of this.nodeTypes.values()) {
      categories.add(info.category.toString().toLowerCase());
    }

    return Array.from(categories);
  }
}
