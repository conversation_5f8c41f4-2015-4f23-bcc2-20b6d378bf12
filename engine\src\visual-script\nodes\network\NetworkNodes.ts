/**
 * 网络通信节点集合
 * 提供WebRTC、WebSocket、HTTP等网络通信功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 网络连接状态枚举
 */
export enum NetworkConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * WebRTC连接类型枚举
 */
export enum WebRTCConnectionType {
  PEER_TO_PEER = 'peer-to-peer',
  MESH = 'mesh',
  STAR = 'star',
  SFU = 'sfu',
  MCU = 'mcu'
}

/**
 * HTTP方法枚举
 */
export enum HTTPMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
  HEAD = 'HEAD',
  OPTIONS = 'OPTIONS'
}

/**
 * 网络连接配置接口
 */
export interface NetworkConnectionConfig {
  url: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  autoReconnect: boolean;
  heartbeatInterval: number;
  compression: boolean;
  encryption: boolean;
}

/**
 * WebRTC配置接口
 */
export interface WebRTCConfig {
  iceServers: RTCIceServer[];
  connectionType: WebRTCConnectionType;
  dataChannelConfig: RTCDataChannelInit;
  mediaConstraints: MediaStreamConstraints;
  codecPreferences: string[];
  bandwidth: {
    audio: number;
    video: number;
    data: number;
  };
}

/**
 * HTTP请求配置接口
 */
export interface HTTPRequestConfig {
  method: HTTPMethod;
  headers: { [key: string]: string };
  body?: any;
  timeout: number;
  retries: number;
  cache: boolean;
  credentials: 'omit' | 'same-origin' | 'include';
}

/**
 * 网络管理器
 */
class NetworkManager {
  private connections: Map<string, any> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 创建WebSocket连接
   */
  createWebSocketConnection(id: string, url: string, config: NetworkConnectionConfig): Promise<WebSocket> {
    return new Promise((resolve, reject) => {
      try {
        const ws = new WebSocket(url);
        
        ws.onopen = () => {
          this.connections.set(id, ws);
          this.emit('connectionOpened', { id, type: 'websocket' });
          resolve(ws);
        };

        ws.onclose = (event) => {
          this.connections.delete(id);
          this.emit('connectionClosed', { id, code: event.code, reason: event.reason });
        };

        ws.onerror = (error) => {
          this.emit('connectionError', { id, error });
          reject(error);
        };

        ws.onmessage = (event) => {
          this.emit('messageReceived', { id, data: event.data });
        };

        // 设置超时
        setTimeout(() => {
          if (ws.readyState === WebSocket.CONNECTING) {
            ws.close();
            reject(new Error('WebSocket连接超时'));
          }
        }, config.timeout);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 创建WebRTC连接
   */
  async createWebRTCConnection(id: string, config: WebRTCConfig): Promise<RTCPeerConnection> {
    try {
      const pc = new RTCPeerConnection({
        iceServers: config.iceServers
      });

      // 设置事件监听器
      pc.onicecandidate = (event) => {
        if (event.candidate) {
          this.emit('iceCandidate', { id, candidate: event.candidate });
        }
      };

      pc.onconnectionstatechange = () => {
        this.emit('connectionStateChange', { id, state: pc.connectionState });
      };

      pc.ondatachannel = (event) => {
        const channel = event.channel;
        this.setupDataChannel(id, channel);
      };

      // 添加getDataChannels方法到连接对象
      (pc as any).getDataChannels = () => {
        return (pc as any)._dataChannels || [];
      };
      (pc as any)._dataChannels = [];

      this.connections.set(id, pc);
      Debug.log('NetworkManager', `WebRTC连接创建: ${id}`);

      return pc;

    } catch (error) {
      Debug.error('NetworkManager', 'WebRTC连接创建失败', error);
      throw error;
    }
  }

  /**
   * 发送HTTP请求
   */
  async sendHTTPRequest(url: string, config: HTTPRequestConfig): Promise<Response> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), config.timeout);

      const requestInit: RequestInit = {
        method: config.method,
        headers: config.headers,
        body: config.body ? JSON.stringify(config.body) : undefined,
        signal: controller.signal,
        credentials: config.credentials,
        cache: config.cache ? 'default' : 'no-cache'
      };

      const response = await fetch(url, requestInit);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      Debug.log('NetworkManager', `HTTP请求成功: ${config.method} ${url}`);
      return response;

    } catch (error) {
      Debug.error('NetworkManager', 'HTTP请求失败', error);
      throw error;
    }
  }

  /**
   * 设置数据通道
   */
  private setupDataChannel(connectionId: string, channel: RTCDataChannel): void {
    channel.onopen = () => {
      this.emit('dataChannelOpen', { connectionId, channelLabel: channel.label });
    };

    channel.onclose = () => {
      this.emit('dataChannelClose', { connectionId, channelLabel: channel.label });
    };

    channel.onmessage = (event) => {
      this.emit('dataChannelMessage', { connectionId, channelLabel: channel.label, data: event.data });
    };

    channel.onerror = (error) => {
      this.emit('dataChannelError', { connectionId, channelLabel: channel.label, error });
    };

    // 将数据通道添加到连接的数据通道列表
    const connection = this.connections.get(connectionId);
    if (connection && (connection as any)._dataChannels) {
      (connection as any)._dataChannels.push(channel);
    }
  }

  /**
   * 发送消息
   */
  sendMessage(connectionId: string, data: any): boolean {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      Debug.warn('NetworkManager', `连接不存在: ${connectionId}`);
      return false;
    }

    try {
      if (connection instanceof WebSocket) {
        if (connection.readyState === WebSocket.OPEN) {
          connection.send(typeof data === 'string' ? data : JSON.stringify(data));
          return true;
        }
      } else if (connection instanceof RTCPeerConnection) {
        // 通过数据通道发送
        const channels = (connection as any)._dataChannels || [];
        for (const channel of channels) {
          if (channel.readyState === 'open') {
            channel.send(typeof data === 'string' ? data : JSON.stringify(data));
            return true;
          }
        }
      }
      return false;
    } catch (error) {
      Debug.error('NetworkManager', '消息发送失败', error);
      return false;
    }
  }

  /**
   * 关闭连接
   */
  closeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      try {
        if (connection instanceof WebSocket) {
          connection.close();
        } else if (connection instanceof RTCPeerConnection) {
          connection.close();
        }
        this.connections.delete(connectionId);
        Debug.log('NetworkManager', `连接关闭: ${connectionId}`);
      } catch (error) {
        Debug.error('NetworkManager', '连接关闭失败', error);
      }
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionState(connectionId: string): NetworkConnectionState {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return NetworkConnectionState.DISCONNECTED;
    }

    if (connection instanceof WebSocket) {
      switch (connection.readyState) {
        case WebSocket.CONNECTING:
          return NetworkConnectionState.CONNECTING;
        case WebSocket.OPEN:
          return NetworkConnectionState.CONNECTED;
        case WebSocket.CLOSING:
        case WebSocket.CLOSED:
          return NetworkConnectionState.DISCONNECTED;
        default:
          return NetworkConnectionState.ERROR;
      }
    } else if (connection instanceof RTCPeerConnection) {
      switch (connection.connectionState) {
        case 'connecting':
          return NetworkConnectionState.CONNECTING;
        case 'connected':
          return NetworkConnectionState.CONNECTED;
        case 'disconnected':
          return NetworkConnectionState.DISCONNECTED;
        case 'failed':
          return NetworkConnectionState.ERROR;
        default:
          return NetworkConnectionState.DISCONNECTED;
      }
    }

    return NetworkConnectionState.DISCONNECTED;
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('NetworkManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 获取连接对象
   */
  getConnection(connectionId: string): any {
    return this.connections.get(connectionId);
  }

  /**
   * 获取所有连接ID
   */
  getAllConnections(): string[] {
    return Array.from(this.connections.keys());
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 关闭所有连接
    for (const [id, connection] of this.connections) {
      this.closeConnection(id);
    }
    this.connections.clear();
    this.eventListeners.clear();
  }
}

/**
 * WebSocket连接节点
 */
export class WebSocketNode extends VisualScriptNode {
  public static readonly TYPE = 'WebSocket';
  public static readonly NAME = 'WebSocket连接';
  public static readonly DESCRIPTION = '创建和管理WebSocket连接';

  private static networkManager: NetworkManager = new NetworkManager();

  constructor(nodeType: string = WebSocketNode.TYPE, name: string = WebSocketNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('connect', 'trigger', '连接');
    this.addInput('disconnect', 'trigger', '断开连接');
    this.addInput('send', 'trigger', '发送消息');
    this.addInput('url', 'string', 'WebSocket URL');
    this.addInput('message', 'any', '消息内容');
    this.addInput('timeout', 'number', '连接超时');
    this.addInput('autoReconnect', 'boolean', '自动重连');

    // 输出端口
    this.addOutput('connection', 'object', '连接对象');
    this.addOutput('connectionId', 'string', '连接ID');
    this.addOutput('state', 'string', '连接状态');
    this.addOutput('receivedMessage', 'any', '接收的消息');
    this.addOutput('onConnected', 'trigger', '连接成功');
    this.addOutput('onDisconnected', 'trigger', '连接断开');
    this.addOutput('onMessage', 'trigger', '消息接收');
    this.addOutput('onError', 'trigger', '连接错误');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const connectTrigger = inputs?.connect;
      const disconnectTrigger = inputs?.disconnect;
      const sendTrigger = inputs?.send;

      if (connectTrigger) {
        return await this.connect(inputs);
      } else if (disconnectTrigger) {
        return this.disconnect(inputs);
      } else if (sendTrigger) {
        return this.sendMessage(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('WebSocketNode', 'WebSocket操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private async connect(inputs: any): Promise<any> {
    const url = inputs?.url as string;
    const timeout = inputs?.timeout as number || 5000;
    const autoReconnect = inputs?.autoReconnect as boolean || false;

    if (!url) {
      throw new Error('未提供WebSocket URL');
    }

    const connectionId = this.generateConnectionId();
    const config: NetworkConnectionConfig = {
      url,
      timeout,
      retryAttempts: autoReconnect ? 3 : 0,
      retryDelay: 1000,
      autoReconnect,
      heartbeatInterval: 30000,
      compression: false,
      encryption: false
    };

    const connection = await WebSocketNode.networkManager.createWebSocketConnection(connectionId, url, config);

    Debug.log('WebSocketNode', `WebSocket连接成功: ${connectionId}`);

    return {
      connection,
      connectionId,
      state: NetworkConnectionState.CONNECTED,
      receivedMessage: null,
      onConnected: true,
      onDisconnected: false,
      onMessage: false,
      onError: false
    };
  }

  private disconnect(inputs: any): any {
    const connectionId = inputs?.connectionId as string || this.getStoredConnectionId();

    if (connectionId) {
      WebSocketNode.networkManager.closeConnection(connectionId);
      Debug.log('WebSocketNode', `WebSocket连接断开: ${connectionId}`);
    }

    return {
      connection: null,
      connectionId: '',
      state: NetworkConnectionState.DISCONNECTED,
      receivedMessage: null,
      onConnected: false,
      onDisconnected: true,
      onMessage: false,
      onError: false
    };
  }

  private sendMessage(inputs: any): any {
    const connectionId = inputs?.connectionId as string || this.getStoredConnectionId();
    const message = inputs?.message;

    if (!connectionId) {
      throw new Error('未提供连接ID');
    }

    if (message === undefined) {
      throw new Error('未提供消息内容');
    }

    const success = WebSocketNode.networkManager.sendMessage(connectionId, message);

    if (success) {
      Debug.log('WebSocketNode', `消息发送成功: ${connectionId}`);
    } else {
      throw new Error('消息发送失败');
    }

    return {
      connection: null,
      connectionId,
      state: WebSocketNode.networkManager.getConnectionState(connectionId),
      receivedMessage: null,
      onConnected: false,
      onDisconnected: false,
      onMessage: false,
      onError: false
    };
  }

  private generateConnectionId(): string {
    return 'ws_' + Math.random().toString(36).substring(2, 11);
  }

  private getStoredConnectionId(): string {
    // 从节点状态中获取存储的连接ID
    return (this as any).connectionId || '';
  }

  private getDefaultOutputs(): any {
    return {
      connection: null,
      connectionId: '',
      state: NetworkConnectionState.DISCONNECTED,
      receivedMessage: null,
      onConnected: false,
      onDisconnected: false,
      onMessage: false,
      onError: false
    };
  }
}

/**
 * WebRTC连接节点
 */
export class WebRTCNode extends VisualScriptNode {
  public static readonly TYPE = 'WebRTC';
  public static readonly NAME = 'WebRTC连接';
  public static readonly DESCRIPTION = '创建和管理WebRTC点对点连接';

  private static networkManager: NetworkManager = new NetworkManager();

  constructor(nodeType: string = WebRTCNode.TYPE, name: string = WebRTCNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('createConnection', 'trigger', '创建连接');
    this.addInput('createOffer', 'trigger', '创建Offer');
    this.addInput('createAnswer', 'trigger', '创建Answer');
    this.addInput('setRemoteDescription', 'trigger', '设置远程描述');
    this.addInput('addIceCandidate', 'trigger', '添加ICE候选');
    this.addInput('sendData', 'trigger', '发送数据');
    this.addInput('iceServers', 'array', 'ICE服务器');
    this.addInput('offer', 'object', 'Offer描述');
    this.addInput('answer', 'object', 'Answer描述');
    this.addInput('candidate', 'object', 'ICE候选');
    this.addInput('data', 'any', '发送数据');
    this.addInput('channelLabel', 'string', '数据通道标签');

    // 输出端口
    this.addOutput('connection', 'object', 'WebRTC连接');
    this.addOutput('connectionId', 'string', '连接ID');
    this.addOutput('localDescription', 'object', '本地描述');
    this.addOutput('iceCandidate', 'object', 'ICE候选');
    this.addOutput('receivedData', 'any', '接收的数据');
    this.addOutput('connectionState', 'string', '连接状态');
    this.addOutput('onConnectionCreated', 'trigger', '连接创建');
    this.addOutput('onOfferCreated', 'trigger', 'Offer创建');
    this.addOutput('onAnswerCreated', 'trigger', 'Answer创建');
    this.addOutput('onIceCandidate', 'trigger', 'ICE候选生成');
    this.addOutput('onDataReceived', 'trigger', '数据接收');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const createConnectionTrigger = inputs?.createConnection;
      const createOfferTrigger = inputs?.createOffer;
      const createAnswerTrigger = inputs?.createAnswer;
      const setRemoteDescriptionTrigger = inputs?.setRemoteDescription;
      const addIceCandidateTrigger = inputs?.addIceCandidate;
      const sendDataTrigger = inputs?.sendData;

      if (createConnectionTrigger) {
        return await this.createConnection(inputs);
      } else if (createOfferTrigger) {
        return await this.createOffer(inputs);
      } else if (createAnswerTrigger) {
        return await this.createAnswer(inputs);
      } else if (setRemoteDescriptionTrigger) {
        return await this.setRemoteDescription(inputs);
      } else if (addIceCandidateTrigger) {
        return await this.addIceCandidate(inputs);
      } else if (sendDataTrigger) {
        return this.sendData(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('WebRTCNode', 'WebRTC操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private async createConnection(inputs: any): Promise<any> {
    const iceServers = inputs?.iceServers as RTCIceServer[] || [
      { urls: 'stun:stun.l.google.com:19302' }
    ];

    const connectionId = this.generateConnectionId();
    const config: WebRTCConfig = {
      iceServers,
      connectionType: WebRTCConnectionType.PEER_TO_PEER,
      dataChannelConfig: {
        ordered: true,
        maxRetransmits: 3
      },
      mediaConstraints: {
        audio: false,
        video: false
      },
      codecPreferences: [],
      bandwidth: {
        audio: 64000,
        video: 1000000,
        data: 1000000
      }
    };

    const connection = await WebRTCNode.networkManager.createWebRTCConnection(connectionId, config);

    Debug.log('WebRTCNode', `WebRTC连接创建: ${connectionId}`);

    return {
      connection,
      connectionId,
      localDescription: null,
      iceCandidate: null,
      receivedData: null,
      connectionState: connection.connectionState,
      onConnectionCreated: true,
      onOfferCreated: false,
      onAnswerCreated: false,
      onIceCandidate: false,
      onDataReceived: false,
      onError: false
    };
  }

  private async createOffer(inputs: any): Promise<any> {
    const connectionId = inputs?.connectionId as string || this.getStoredConnectionId();
    const connection = WebRTCNode.networkManager.getConnection(connectionId) as RTCPeerConnection;

    if (!connection) {
      throw new Error('WebRTC连接不存在');
    }

    // 创建数据通道
    const channelLabel = inputs?.channelLabel as string || 'dataChannel';
    const dataChannel = connection.createDataChannel(channelLabel);

    const offer = await connection.createOffer();
    await connection.setLocalDescription(offer);

    Debug.log('WebRTCNode', `Offer创建成功: ${connectionId}`);

    return {
      connection,
      connectionId,
      localDescription: offer,
      iceCandidate: null,
      receivedData: null,
      connectionState: connection.connectionState,
      onConnectionCreated: false,
      onOfferCreated: true,
      onAnswerCreated: false,
      onIceCandidate: false,
      onDataReceived: false,
      onError: false
    };
  }

  private async createAnswer(inputs: any): Promise<any> {
    const connectionId = inputs?.connectionId as string || this.getStoredConnectionId();
    const connection = WebRTCNode.networkManager.getConnection(connectionId) as RTCPeerConnection;

    if (!connection) {
      throw new Error('WebRTC连接不存在');
    }

    const answer = await connection.createAnswer();
    await connection.setLocalDescription(answer);

    Debug.log('WebRTCNode', `Answer创建成功: ${connectionId}`);

    return {
      connection,
      connectionId,
      localDescription: answer,
      iceCandidate: null,
      receivedData: null,
      connectionState: connection.connectionState,
      onConnectionCreated: false,
      onOfferCreated: false,
      onAnswerCreated: true,
      onIceCandidate: false,
      onDataReceived: false,
      onError: false
    };
  }

  private async setRemoteDescription(inputs: any): Promise<any> {
    const connectionId = inputs?.connectionId as string || this.getStoredConnectionId();
    const connection = WebRTCNode.networkManager.getConnection(connectionId) as RTCPeerConnection;
    const description = inputs?.offer || inputs?.answer;

    if (!connection) {
      throw new Error('WebRTC连接不存在');
    }

    if (!description) {
      throw new Error('未提供远程描述');
    }

    await connection.setRemoteDescription(description);

    Debug.log('WebRTCNode', `远程描述设置成功: ${connectionId}`);

    return {
      connection,
      connectionId,
      localDescription: connection.localDescription,
      iceCandidate: null,
      receivedData: null,
      connectionState: connection.connectionState,
      onConnectionCreated: false,
      onOfferCreated: false,
      onAnswerCreated: false,
      onIceCandidate: false,
      onDataReceived: false,
      onError: false
    };
  }

  private async addIceCandidate(inputs: any): Promise<any> {
    const connectionId = inputs?.connectionId as string || this.getStoredConnectionId();
    const connection = WebRTCNode.networkManager.getConnection(connectionId) as RTCPeerConnection;
    const candidate = inputs?.candidate;

    if (!connection) {
      throw new Error('WebRTC连接不存在');
    }

    if (!candidate) {
      throw new Error('未提供ICE候选');
    }

    await connection.addIceCandidate(candidate);

    Debug.log('WebRTCNode', `ICE候选添加成功: ${connectionId}`);

    return {
      connection,
      connectionId,
      localDescription: connection.localDescription,
      iceCandidate: candidate,
      receivedData: null,
      connectionState: connection.connectionState,
      onConnectionCreated: false,
      onOfferCreated: false,
      onAnswerCreated: false,
      onIceCandidate: true,
      onDataReceived: false,
      onError: false
    };
  }

  private sendData(inputs: any): any {
    const connectionId = inputs?.connectionId as string || this.getStoredConnectionId();
    const data = inputs?.data;

    if (!connectionId) {
      throw new Error('未提供连接ID');
    }

    if (data === undefined) {
      throw new Error('未提供发送数据');
    }

    const success = WebRTCNode.networkManager.sendMessage(connectionId, data);

    if (!success) {
      throw new Error('数据发送失败');
    }

    Debug.log('WebRTCNode', `数据发送成功: ${connectionId}`);

    return {
      connection: null,
      connectionId,
      localDescription: null,
      iceCandidate: null,
      receivedData: null,
      connectionState: WebRTCNode.networkManager.getConnectionState(connectionId),
      onConnectionCreated: false,
      onOfferCreated: false,
      onAnswerCreated: false,
      onIceCandidate: false,
      onDataReceived: false,
      onError: false
    };
  }

  private generateConnectionId(): string {
    return 'rtc_' + Math.random().toString(36).substring(2, 11);
  }

  private getStoredConnectionId(): string {
    return (this as any).connectionId || '';
  }

  private getDefaultOutputs(): any {
    return {
      connection: null,
      connectionId: '',
      localDescription: null,
      iceCandidate: null,
      receivedData: null,
      connectionState: 'new',
      onConnectionCreated: false,
      onOfferCreated: false,
      onAnswerCreated: false,
      onIceCandidate: false,
      onDataReceived: false,
      onError: false
    };
  }
}

/**
 * HTTP请求节点
 */
export class HTTPRequestNode extends VisualScriptNode {
  public static readonly TYPE = 'HTTPRequest';
  public static readonly NAME = 'HTTP请求';
  public static readonly DESCRIPTION = '发送HTTP请求并处理响应';

  private static networkManager: NetworkManager = new NetworkManager();

  constructor(nodeType: string = HTTPRequestNode.TYPE, name: string = HTTPRequestNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('send', 'trigger', '发送请求');
    this.addInput('url', 'string', '请求URL');
    this.addInput('method', 'string', 'HTTP方法');
    this.addInput('headers', 'object', '请求头');
    this.addInput('body', 'any', '请求体');
    this.addInput('timeout', 'number', '超时时间');
    this.addInput('retries', 'number', '重试次数');
    this.addInput('credentials', 'string', '凭据模式');

    // 输出端口
    this.addOutput('response', 'object', '响应对象');
    this.addOutput('data', 'any', '响应数据');
    this.addOutput('status', 'number', '状态码');
    this.addOutput('statusText', 'string', '状态文本');
    this.addOutput('headers', 'object', '响应头');
    this.addOutput('ok', 'boolean', '请求成功');
    this.addOutput('onSuccess', 'trigger', '请求成功');
    this.addOutput('onError', 'trigger', '请求失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const sendTrigger = inputs?.send;
      if (!sendTrigger) {
        return this.getDefaultOutputs();
      }

      const url = inputs?.url as string;
      const method = inputs?.method as string || 'GET';
      const headers = inputs?.headers as { [key: string]: string } || {};
      const body = inputs?.body;
      const timeout = inputs?.timeout as number || 10000;
      const retries = inputs?.retries as number || 0;
      const credentials = inputs?.credentials as 'omit' | 'same-origin' | 'include' || 'same-origin';

      if (!url) {
        throw new Error('未提供请求URL');
      }

      // 设置默认请求头
      const defaultHeaders = {
        'Content-Type': 'application/json',
        ...headers
      };

      const config: HTTPRequestConfig = {
        method: method as HTTPMethod,
        headers: defaultHeaders,
        body,
        timeout,
        retries,
        cache: false,
        credentials
      };

      const response = await HTTPRequestNode.networkManager.sendHTTPRequest(url, config);

      // 解析响应数据
      let responseData: any = null;
      const contentType = response.headers.get('content-type') || '';

      if (contentType.includes('application/json')) {
        responseData = await response.json();
      } else if (contentType.includes('text/')) {
        responseData = await response.text();
      } else {
        responseData = await response.blob();
      }

      // 转换响应头为对象
      const responseHeaders: { [key: string]: string } = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      Debug.log('HTTPRequestNode', `HTTP请求成功: ${method} ${url} - ${response.status}`);

      return {
        response,
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
        ok: response.ok,
        onSuccess: true,
        onError: false
      };

    } catch (error) {
      Debug.error('HTTPRequestNode', 'HTTP请求失败', error);

      // 尝试从错误中提取状态信息
      let status = 0;
      let statusText = 'Network Error';

      if (error instanceof TypeError) {
        statusText = 'Network Error';
      } else if (error.message.includes('HTTP错误')) {
        const match = error.message.match(/HTTP错误: (\d+) (.+)/);
        if (match) {
          status = parseInt(match[1]);
          statusText = match[2];
        }
      }

      return {
        response: null,
        data: null,
        status,
        statusText,
        headers: {},
        ok: false,
        onSuccess: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      response: null,
      data: null,
      status: 0,
      statusText: '',
      headers: {},
      ok: false,
      onSuccess: false,
      onError: false
    };
  }
}

/**
 * 网络同步节点
 */
export class NetworkSyncNode extends VisualScriptNode {
  public static readonly TYPE = 'NetworkSync';
  public static readonly NAME = '网络同步';
  public static readonly DESCRIPTION = '实现多客户端状态同步';

  private static networkManager: NetworkManager = new NetworkManager();
  private syncDataMap: Map<string, any> = new Map();
  private lastSyncTime: number = 0;

  constructor(nodeType: string = NetworkSyncNode.TYPE, name: string = NetworkSyncNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('sync', 'trigger', '同步数据');
    this.addInput('broadcast', 'trigger', '广播数据');
    this.addInput('connectionId', 'string', '连接ID');
    this.addInput('data', 'any', '同步数据');
    this.addInput('key', 'string', '数据键');
    this.addInput('syncInterval', 'number', '同步间隔');
    this.addInput('conflictResolution', 'string', '冲突解决策略');

    // 输出端口
    this.addOutput('syncedData', 'any', '同步后数据');
    this.addOutput('conflicts', 'array', '冲突列表');
    this.addOutput('lastSyncTime', 'number', '最后同步时间');
    this.addOutput('onSynced', 'trigger', '同步完成');
    this.addOutput('onConflict', 'trigger', '发现冲突');
    this.addOutput('onBroadcast', 'trigger', '广播完成');
    this.addOutput('onError', 'trigger', '同步失败');
  }

  public execute(inputs?: any): any {
    try {
      const syncTrigger = inputs?.sync;
      const broadcastTrigger = inputs?.broadcast;

      if (syncTrigger) {
        return this.performSyncData(inputs);
      } else if (broadcastTrigger) {
        return this.broadcastData(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('NetworkSyncNode', '网络同步失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private performSyncData(inputs: any): any {
    const connectionId = inputs?.connectionId as string;
    const data = inputs?.data;
    const key = inputs?.key as string || 'default';
    const syncInterval = inputs?.syncInterval as number || 1000;
    const conflictResolution = inputs?.conflictResolution as string || 'lastWrite';

    if (!connectionId) {
      throw new Error('未提供连接ID');
    }

    const currentTime = Date.now();

    // 检查同步间隔
    if (currentTime - this.lastSyncTime < syncInterval) {
      return this.getDefaultOutputs();
    }

    // 存储数据
    const syncEntry = {
      data,
      timestamp: currentTime,
      source: connectionId
    };

    // 检查冲突
    const conflicts: any[] = [];
    const existingData = this.syncDataMap.get(key);

    if (existingData && existingData.timestamp > currentTime - syncInterval) {
      conflicts.push({
        key,
        existing: existingData,
        incoming: syncEntry,
        resolution: conflictResolution
      });
    }

    // 应用冲突解决策略
    let finalData = data;
    if (conflicts.length > 0) {
      finalData = this.resolveConflict(conflicts[0], conflictResolution);
    }

    this.syncDataMap.set(key, {
      data: finalData,
      timestamp: currentTime,
      source: connectionId
    });

    this.lastSyncTime = currentTime;

    // 发送同步数据
    const success = NetworkSyncNode.networkManager.sendMessage(connectionId, {
      type: 'sync',
      key,
      data: finalData,
      timestamp: currentTime
    });

    if (!success) {
      throw new Error('同步数据发送失败');
    }

    Debug.log('NetworkSyncNode', `数据同步成功: ${key}`);

    return {
      syncedData: finalData,
      conflicts,
      lastSyncTime: currentTime,
      onSynced: true,
      onConflict: conflicts.length > 0,
      onBroadcast: false,
      onError: false
    };
  }

  private broadcastData(inputs: any): any {
    const data = inputs?.data;
    const key = inputs?.key as string || 'broadcast';

    if (data === undefined) {
      throw new Error('未提供广播数据');
    }

    const broadcastMessage = {
      type: 'broadcast',
      key,
      data,
      timestamp: Date.now()
    };

    // 向所有连接广播
    let successCount = 0;
    const connections = NetworkSyncNode.networkManager.getAllConnections();

    for (const connectionId of connections) {
      const success = NetworkSyncNode.networkManager.sendMessage(connectionId, broadcastMessage);
      if (success) {
        successCount++;
      }
    }

    Debug.log('NetworkSyncNode', `数据广播完成: ${successCount}/${connections.length} 连接`);

    return {
      syncedData: data,
      conflicts: [],
      lastSyncTime: Date.now(),
      onSynced: false,
      onConflict: false,
      onBroadcast: true,
      onError: successCount === 0 && connections.length > 0
    };
  }

  private resolveConflict(conflict: any, strategy: string): any {
    switch (strategy) {
      case 'lastWrite':
        return conflict.incoming.timestamp > conflict.existing.timestamp
          ? conflict.incoming.data
          : conflict.existing.data;

      case 'firstWrite':
        return conflict.existing.timestamp < conflict.incoming.timestamp
          ? conflict.existing.data
          : conflict.incoming.data;

      case 'merge':
        if (typeof conflict.existing.data === 'object' && typeof conflict.incoming.data === 'object') {
          return { ...conflict.existing.data, ...conflict.incoming.data };
        }
        return conflict.incoming.data;

      default:
        return conflict.incoming.data;
    }
  }

  private getDefaultOutputs(): any {
    return {
      syncedData: null,
      conflicts: [],
      lastSyncTime: this.lastSyncTime,
      onSynced: false,
      onConflict: false,
      onBroadcast: false,
      onError: false
    };
  }
}
