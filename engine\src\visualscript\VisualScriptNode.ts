/**
 * 可视化脚本节点基类
 */

export interface NodeInput {
  name: string;
  type: string;
  label: string;
  defaultValue?: any;
}

export interface NodeOutput {
  name: string;
  type: string;
  label: string;
}

export interface NodeConnection {
  fromNode: string;
  fromOutput: string;
  toNode: string;
  toInput: string;
}

export interface VisualScriptContext {
  deltaTime?: number;
  time?: number;
  [key: string]: any;
}

export abstract class VisualScriptNode {
  /** 节点ID */
  public readonly id: string;
  
  /** 节点类型 */
  public readonly nodeType: string;
  
  /** 节点名称 */
  public name: string;
  
  /** 输入端口 */
  protected inputs: Map<string, NodeInput> = new Map();
  
  /** 输出端口 */
  protected outputs: Map<string, NodeOutput> = new Map();
  
  /** 执行上下文 */
  private context: VisualScriptContext | null = null;

  constructor(nodeType: string, name: string, id?: string) {
    this.nodeType = nodeType;
    this.name = name;
    this.id = id || this.generateId();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 添加输入端口
   */
  protected addInput(name: string, type: string, label: string, defaultValue?: any): void {
    this.inputs.set(name, { name, type, label, defaultValue });
  }

  /**
   * 添加输出端口
   */
  protected addOutput(name: string, type: string, label: string): void {
    this.outputs.set(name, { name, type, label });
  }

  /**
   * 获取输入端口
   */
  public getInputs(): NodeInput[] {
    return Array.from(this.inputs.values());
  }

  /**
   * 获取输出端口
   */
  public getOutputs(): NodeOutput[] {
    return Array.from(this.outputs.values());
  }

  /**
   * 获取输入端口
   */
  public getInput(name: string): NodeInput | null {
    return this.inputs.get(name) || null;
  }

  /**
   * 获取输出端口
   */
  public getOutput(name: string): NodeOutput | null {
    return this.outputs.get(name) || null;
  }

  /**
   * 设置执行上下文
   */
  public setContext(context: VisualScriptContext): void {
    this.context = context;
  }

  /**
   * 获取执行上下文
   */
  public getContext(): VisualScriptContext | null {
    return this.context;
  }

  /**
   * 执行节点
   * @param inputs 输入值
   * @returns 输出值
   */
  public abstract execute(inputs?: any): any;

  /**
   * 验证输入
   */
  public validateInputs(inputs: any): boolean {
    for (const input of this.inputs.values()) {
      if (input.defaultValue === undefined && inputs[input.name] === undefined) {
        console.warn(`节点 ${this.name} 缺少必需的输入: ${input.name}`);
        return false;
      }
    }
    return true;
  }

  /**
   * 获取输入值（包含默认值）
   */
  protected getInputValue(inputs: any, name: string): any {
    const input = this.inputs.get(name);
    if (!input) return undefined;
    
    return inputs[name] !== undefined ? inputs[name] : input.defaultValue;
  }

  /**
   * 克隆节点
   */
  public clone(): VisualScriptNode {
    // 这是一个抽象方法，子类应该实现具体的克隆逻辑
    throw new Error('克隆方法必须在子类中实现');
  }

  /**
   * 序列化节点
   */
  public serialize(): any {
    return {
      id: this.id,
      nodeType: this.nodeType,
      name: this.name,
      inputs: Array.from(this.inputs.values()),
      outputs: Array.from(this.outputs.values())
    };
  }

  /**
   * 反序列化节点
   */
  public static deserialize(data: any): VisualScriptNode {
    // 这是一个静态方法，需要在具体的节点类中实现
    throw new Error('反序列化方法必须在具体的节点类中实现');
  }

  /**
   * 销毁节点
   * 清理节点资源，子类可以重写此方法进行特定的清理操作
   */
  public destroy(): void {
    // 清理输入输出端口
    this.inputs.clear();
    this.outputs.clear();

    // 清理执行上下文
    this.context = null;
  }
}
