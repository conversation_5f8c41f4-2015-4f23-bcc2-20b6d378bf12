/**
 * 区块链面板 - 显示钱包连接状态和区块链相关信息
 */

import React, { useState } from 'react';
import { Card, Button, Space, Typography, Divider, Tag, Tooltip, Alert, Modal } from 'antd';
import {
  WalletOutlined,
  LinkOutlined,
  DisconnectOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useBlockchain } from '../../hooks/useBlockchain';
// import { WalletConnectionModal } from './WalletConnectionModal';
// import { NetworkSwitcher } from './NetworkSwitcher';
// import { TransactionHistory } from './TransactionHistory';
import './BlockchainPanel.less';

const { Title, Text, Paragraph } = Typography;

export interface BlockchainPanelProps {
  className?: string;
  style?: React.CSSProperties;
}

export const BlockchainPanel: React.FC<BlockchainPanelProps> = ({
  className,
  style
}) => {
  const {
    isConnected,
    currentAccount,
    currentNetwork,
    balance,
    isLoading,
    error,
    connectWallet,
    disconnectWallet,
    switchNetwork,
    refreshBalance
  } = useBlockchain();

  const [showConnectionModal, setShowConnectionModal] = useState(false);
  const [showTransactionHistory, setShowTransactionHistory] = useState(false);

  // 格式化地址显示
  const formatAddress = (address: string) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  // 格式化余额显示
  const formatBalance = (balance: string) => {
    const num = parseFloat(balance);
    if (num === 0) return '0';
    if (num < 0.001) return '< 0.001';
    return num.toFixed(4);
  };

  // 获取网络状态颜色
  const getNetworkStatusColor = () => {
    if (!isConnected) return 'default';
    if (currentNetwork?.testnet) return 'orange';
    return 'green';
  };

  // 处理钱包连接
  const handleConnectWallet = async (walletType?: string) => {
    try {
      await connectWallet(walletType);
      setShowConnectionModal(false);
    } catch (error) {
      console.error('连接钱包失败:', error);
    }
  };

  // 处理钱包断开
  const handleDisconnectWallet = async () => {
    try {
      await disconnectWallet();
    } catch (error) {
      console.error('断开钱包失败:', error);
    }
  };

  // 处理网络切换
  const handleNetworkSwitch = async (networkId: string) => {
    try {
      await switchNetwork(networkId);
    } catch (error) {
      console.error('切换网络失败:', error);
    }
  };

  // 处理余额刷新
  const handleRefreshBalance = async () => {
    try {
      await refreshBalance();
    } catch (error) {
      console.error('刷新余额失败:', error);
    }
  };

  return (
    <div className={`blockchain-panel ${className || ''}`} style={style}>
      <Card
        title={
          <Space>
            <WalletOutlined />
            <span>区块链钱包</span>
          </Space>
        }
        extra={
          <Space>
            {isConnected && (
              <Tooltip title="刷新余额">
                <Button
                  type="text"
                  icon={<ReloadOutlined />}
                  loading={isLoading}
                  onClick={handleRefreshBalance}
                />
              </Tooltip>
            )}
            <Tooltip title="设置">
              <Button
                type="text"
                icon={<SettingOutlined />}
                onClick={() => setShowTransactionHistory(!showTransactionHistory)}
              />
            </Tooltip>
          </Space>
        }
        size="small"
      >
        {error && (
          <Alert
            message="连接错误"
            description={error}
            type="error"
            showIcon
            closable
            style={{ marginBottom: 16 }}
          />
        )}

        {!isConnected ? (
          <div className="wallet-disconnected">
            <div className="wallet-status">
              <DisconnectOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
              <Title level={4} type="secondary">钱包未连接</Title>
              <Paragraph type="secondary">
                连接Web3钱包以使用NFT和数字资产功能
              </Paragraph>
            </div>
            <Button
              type="primary"
              icon={<LinkOutlined />}
              size="large"
              loading={isLoading}
              onClick={() => setShowConnectionModal(true)}
            >
              连接钱包
            </Button>
          </div>
        ) : (
          <div className="wallet-connected">
            <Space direction="vertical" style={{ width: '100%' }}>
              {/* 账户信息 */}
              <div className="account-info">
                <Space>
                  <Text strong>账户:</Text>
                  <Tooltip title={currentAccount}>
                    <Tag color="blue">{formatAddress(currentAccount || '')}</Tag>
                  </Tooltip>
                </Space>
              </div>

              {/* 网络信息 */}
              <div className="network-info">
                <Space>
                  <Text strong>网络:</Text>
                  <Tag color={getNetworkStatusColor()}>
                    {currentNetwork?.name || '未知网络'}
                  </Tag>
                  {currentNetwork?.testnet && (
                    <Tag color="orange">测试网</Tag>
                  )}
                </Space>
              </div>

              {/* 余额信息 */}
              <div className="balance-info">
                <Space>
                  <Text strong>余额:</Text>
                  <Text code>
                    {formatBalance(balance || '0')} {currentNetwork?.nativeCurrency?.symbol || 'ETH'}
                  </Text>
                </Space>
              </div>

              <Divider style={{ margin: '12px 0' }} />

              {/* 操作按钮 */}
              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                <Button
                  type="default"
                  onClick={() => handleNetworkSwitch('mainnet')}
                >
                  切换网络
                </Button>
                <Button
                  type="default"
                  icon={<DisconnectOutlined />}
                  onClick={handleDisconnectWallet}
                >
                  断开连接
                </Button>
              </Space>
            </Space>
          </div>
        )}

        {/* 交易历史 */}
        {showTransactionHistory && isConnected && (
          <>
            <Divider />
            <div>
              <Text>交易历史功能开发中...</Text>
            </div>
          </>
        )}
      </Card>

      {/* 钱包连接模态框 */}
      <Modal
        title="连接钱包"
        open={showConnectionModal}
        onCancel={() => setShowConnectionModal(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowConnectionModal(false)}>
            取消
          </Button>,
          <Button key="connect" type="primary" loading={isLoading} onClick={() => handleConnectWallet()}>
            连接
          </Button>
        ]}
      >
        <div>
          <Text>请选择要连接的钱包类型</Text>
        </div>
      </Modal>
    </div>
  );
};
