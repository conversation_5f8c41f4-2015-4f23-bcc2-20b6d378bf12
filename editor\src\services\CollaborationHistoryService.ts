/**
 * 协作历史和版本控制服务
 * 管理协作编辑的历史记录、版本控制和变更追踪
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Operation } from './OperationalTransform';

// 版本信息接口
export interface Version {
  id: string;
  number: number;
  timestamp: number;
  authorId: string;
  authorName: string;
  message: string;
  operations: Operation[];
  snapshot?: any; // 完整状态快照
  parentVersionId?: string;
  tags: string[];
  metadata: Record<string, any>;
}

// 变更记录接口
export interface ChangeRecord {
  id: string;
  versionId: string;
  operation: Operation;
  timestamp: number;
  authorId: string;
  authorName: string;
  description: string;
  affectedEntities: string[];
  changeType: ChangeType;
  metadata: Record<string, any>;
}

// 变更类型枚举
export enum ChangeType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  MOVE = 'move',
  RENAME = 'rename',
  PROPERTY_CHANGE = 'property_change',
  COMPONENT_ADD = 'component_add',
  COMPONENT_REMOVE = 'component_remove',
  COMPONENT_UPDATE = 'component_update'
}

// 分支信息接口
export interface Branch {
  id: string;
  name: string;
  baseVersionId: string;
  headVersionId: string;
  createdBy: string;
  createdAt: number;
  description: string;
  isActive: boolean;
  isProtected: boolean;
}

// 合并请求接口
export interface MergeRequest {
  id: string;
  sourceBranchId: string;
  targetBranchId: string;
  title: string;
  description: string;
  authorId: string;
  createdAt: number;
  status: MergeStatus;
  reviewers: string[];
  conflicts: string[];
  approvals: MergeApproval[];
}

// 合并状态枚举
export enum MergeStatus {
  OPEN = 'open',
  APPROVED = 'approved',
  MERGED = 'merged',
  CLOSED = 'closed',
  CONFLICTED = 'conflicted'
}

// 合并审批接口
export interface MergeApproval {
  reviewerId: string;
  approved: boolean;
  comment: string;
  timestamp: number;
}

// 协作会话接口
export interface CollaborationSession {
  id: string;
  startTime: number;
  endTime?: number;
  participants: string[];
  operations: Operation[];
  summary: string;
  achievements: string[];
}

/**
 * 协作历史和版本控制服务类
 */
export class CollaborationHistoryService extends EventEmitter {
  private static instance: CollaborationHistoryService;
  private versions: Map<string, Version> = new Map();
  private changes: Map<string, ChangeRecord> = new Map();
  private branches: Map<string, Branch> = new Map();
  private mergeRequests: Map<string, MergeRequest> = new Map();
  private sessions: Map<string, CollaborationSession> = new Map();
  private currentVersionId: string = '';
  private currentBranchId: string = 'main';
  private versionCounter: number = 1;

  private constructor() {
    super();
    this.initializeMainBranch();
  }

  public static getInstance(): CollaborationHistoryService {
    if (!CollaborationHistoryService.instance) {
      CollaborationHistoryService.instance = new CollaborationHistoryService();
    }
    return CollaborationHistoryService.instance;
  }

  /**
   * 初始化主分支
   */
  private initializeMainBranch(): void {
    const mainBranch: Branch = {
      id: 'main',
      name: 'main',
      baseVersionId: '',
      headVersionId: '',
      createdBy: 'system',
      createdAt: Date.now(),
      description: 'Main branch',
      isActive: true,
      isProtected: true
    };

    this.branches.set('main', mainBranch);
  }

  /**
   * 创建新版本
   */
  public createVersion(
    operations: Operation[],
    authorId: string,
    authorName: string,
    message: string,
    snapshot?: any
  ): Version {
    const versionId = `v${this.versionCounter++}_${Date.now()}`;
    
    const version: Version = {
      id: versionId,
      number: this.versionCounter - 1,
      timestamp: Date.now(),
      authorId,
      authorName,
      message,
      operations,
      snapshot,
      parentVersionId: this.currentVersionId || undefined,
      tags: [],
      metadata: {}
    };

    this.versions.set(versionId, version);
    this.currentVersionId = versionId;

    // 更新当前分支的头版本
    const currentBranch = this.branches.get(this.currentBranchId);
    if (currentBranch) {
      currentBranch.headVersionId = versionId;
    }

    // 记录变更
    operations.forEach(operation => {
      this.recordChange(versionId, operation, authorId, authorName);
    });

    this.emit('versionCreated', version);
    return version;
  }

  /**
   * 记录变更
   */
  private recordChange(
    versionId: string,
    operation: Operation,
    authorId: string,
    authorName: string
  ): void {
    const changeId = `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const change: ChangeRecord = {
      id: changeId,
      versionId,
      operation,
      timestamp: Date.now(),
      authorId,
      authorName,
      description: this.generateChangeDescription(operation),
      affectedEntities: this.extractAffectedEntities(operation),
      changeType: this.determineChangeType(operation),
      metadata: {}
    };

    this.changes.set(changeId, change);
    this.emit('changeRecorded', change);
  }

  /**
   * 获取版本历史
   */
  public getVersionHistory(branchId?: string): Version[] {
    const targetBranchId = branchId || this.currentBranchId;
    const branch = this.branches.get(targetBranchId);
    
    if (!branch) return [];

    const versions: Version[] = [];
    let currentVersionId = branch.headVersionId;

    while (currentVersionId) {
      const version = this.versions.get(currentVersionId);
      if (version) {
        versions.push(version);
        currentVersionId = version.parentVersionId || '';
      } else {
        break;
      }
    }

    return versions;
  }

  /**
   * 获取变更历史
   */
  public getChangeHistory(entityId?: string, limit?: number): ChangeRecord[] {
    let changes = Array.from(this.changes.values());

    if (entityId) {
      changes = changes.filter(change => 
        change.affectedEntities.includes(entityId)
      );
    }

    changes.sort((a, b) => b.timestamp - a.timestamp);

    if (limit) {
      changes = changes.slice(0, limit);
    }

    return changes;
  }

  /**
   * 回滚到指定版本
   */
  public async revertToVersion(versionId: string, authorId: string, authorName: string): Promise<boolean> {
    const version = this.versions.get(versionId);
    if (!version) {
      throw new Error(`Version not found: ${versionId}`);
    }

    try {
      // 创建回滚操作
      const revertOperations = this.generateRevertOperations(versionId);
      
      // 创建新版本
      this.createVersion(
        revertOperations,
        authorId,
        authorName,
        `Revert to version ${version.number}`,
        version.snapshot
      );

      this.emit('versionReverted', { versionId, authorId });
      return true;
    } catch (error) {
      console.error('Failed to revert to version:', error);
      return false;
    }
  }

  /**
   * 创建分支
   */
  public createBranch(
    name: string,
    baseVersionId: string,
    createdBy: string,
    description: string = ''
  ): Branch {
    const branchId = `branch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const branch: Branch = {
      id: branchId,
      name,
      baseVersionId,
      headVersionId: baseVersionId,
      createdBy,
      createdAt: Date.now(),
      description,
      isActive: true,
      isProtected: false
    };

    this.branches.set(branchId, branch);
    this.emit('branchCreated', branch);
    return branch;
  }

  /**
   * 切换分支
   */
  public switchBranch(branchId: string): boolean {
    const branch = this.branches.get(branchId);
    if (!branch || !branch.isActive) {
      return false;
    }

    this.currentBranchId = branchId;
    this.currentVersionId = branch.headVersionId;
    this.emit('branchSwitched', { branchId, versionId: this.currentVersionId });
    return true;
  }

  /**
   * 创建合并请求
   */
  public createMergeRequest(
    sourceBranchId: string,
    targetBranchId: string,
    title: string,
    description: string,
    authorId: string,
    reviewers: string[] = []
  ): MergeRequest {
    const mergeRequestId = `mr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const mergeRequest: MergeRequest = {
      id: mergeRequestId,
      sourceBranchId,
      targetBranchId,
      title,
      description,
      authorId,
      createdAt: Date.now(),
      status: MergeStatus.OPEN,
      reviewers,
      conflicts: [],
      approvals: []
    };

    // 检查冲突
    mergeRequest.conflicts = this.detectMergeConflicts(sourceBranchId, targetBranchId);
    if (mergeRequest.conflicts.length > 0) {
      mergeRequest.status = MergeStatus.CONFLICTED;
    }

    this.mergeRequests.set(mergeRequestId, mergeRequest);
    this.emit('mergeRequestCreated', mergeRequest);
    return mergeRequest;
  }

  /**
   * 审批合并请求
   */
  public approveMergeRequest(
    mergeRequestId: string,
    reviewerId: string,
    approved: boolean,
    comment: string = ''
  ): boolean {
    const mergeRequest = this.mergeRequests.get(mergeRequestId);
    if (!mergeRequest || mergeRequest.status !== MergeStatus.OPEN) {
      return false;
    }

    const approval: MergeApproval = {
      reviewerId,
      approved,
      comment,
      timestamp: Date.now()
    };

    mergeRequest.approvals.push(approval);

    // 检查是否所有审批者都已审批
    const allReviewersApproved = mergeRequest.reviewers.every(reviewerId =>
      mergeRequest.approvals.some(approval => 
        approval.reviewerId === reviewerId && approval.approved
      )
    );

    if (allReviewersApproved) {
      mergeRequest.status = MergeStatus.APPROVED;
    }

    this.emit('mergeRequestApproved', { mergeRequestId, approval });
    return true;
  }

  /**
   * 执行合并
   */
  public async executeMerge(
    mergeRequestId: string,
    executorId: string,
    executorName: string
  ): Promise<boolean> {
    const mergeRequest = this.mergeRequests.get(mergeRequestId);
    if (!mergeRequest || mergeRequest.status !== MergeStatus.APPROVED) {
      return false;
    }

    try {
      // 执行合并逻辑
      const mergeOperations = this.generateMergeOperations(
        mergeRequest.sourceBranchId,
        mergeRequest.targetBranchId
      );

      // 切换到目标分支
      this.switchBranch(mergeRequest.targetBranchId);

      // 创建合并版本
      this.createVersion(
        mergeOperations,
        executorId,
        executorName,
        `Merge ${mergeRequest.sourceBranchId} into ${mergeRequest.targetBranchId}`
      );

      mergeRequest.status = MergeStatus.MERGED;
      this.emit('mergeExecuted', mergeRequest);
      return true;
    } catch (error) {
      console.error('Failed to execute merge:', error);
      return false;
    }
  }

  /**
   * 开始协作会话
   */
  public startCollaborationSession(participants: string[]): string {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session: CollaborationSession = {
      id: sessionId,
      startTime: Date.now(),
      participants,
      operations: [],
      summary: '',
      achievements: []
    };

    this.sessions.set(sessionId, session);
    this.emit('sessionStarted', session);
    return sessionId;
  }

  /**
   * 结束协作会话
   */
  public endCollaborationSession(sessionId: string, summary: string = ''): boolean {
    const session = this.sessions.get(sessionId);
    if (!session || session.endTime) {
      return false;
    }

    session.endTime = Date.now();
    session.summary = summary;
    session.achievements = this.generateSessionAchievements(session);

    this.emit('sessionEnded', session);
    return true;
  }

  /**
   * 添加操作到会话
   */
  public addOperationToSession(sessionId: string, operation: Operation): void {
    const session = this.sessions.get(sessionId);
    if (session && !session.endTime) {
      session.operations.push(operation);
    }
  }

  /**
   * 获取当前版本
   */
  public getCurrentVersion(): Version | null {
    return this.versions.get(this.currentVersionId) || null;
  }

  /**
   * 获取所有分支
   */
  public getAllBranches(): Branch[] {
    return Array.from(this.branches.values());
  }

  /**
   * 获取合并请求
   */
  public getMergeRequests(status?: MergeStatus): MergeRequest[] {
    let requests = Array.from(this.mergeRequests.values());
    if (status) {
      requests = requests.filter(req => req.status === status);
    }
    return requests.sort((a, b) => b.createdAt - a.createdAt);
  }

  /**
   * 获取协作会话
   */
  public getCollaborationSessions(active?: boolean): CollaborationSession[] {
    let sessions = Array.from(this.sessions.values());
    if (active !== undefined) {
      sessions = sessions.filter(session => active ? !session.endTime : !!session.endTime);
    }
    return sessions.sort((a, b) => b.startTime - a.startTime);
  }

  /**
   * 获取协作统计
   */
  public getCollaborationStats(): {
    totalVersions: number;
    totalChanges: number;
    totalBranches: number;
    activeSessions: number;
    topContributors: { userId: string; contributions: number }[];
  } {
    const contributorMap = new Map<string, number>();
    
    // 统计贡献者
    for (const version of this.versions.values()) {
      const count = contributorMap.get(version.authorId) || 0;
      contributorMap.set(version.authorId, count + 1);
    }

    const topContributors = Array.from(contributorMap.entries())
      .map(([userId, contributions]) => ({ userId, contributions }))
      .sort((a, b) => b.contributions - a.contributions)
      .slice(0, 10);

    const activeSessions = Array.from(this.sessions.values())
      .filter(session => !session.endTime).length;

    return {
      totalVersions: this.versions.size,
      totalChanges: this.changes.size,
      totalBranches: this.branches.size,
      activeSessions,
      topContributors
    };
  }

  // 私有辅助方法

  private generateChangeDescription(operation: Operation): string {
    switch (operation.type) {
      case 'create_entity':
        return `Created entity ${operation.entityId}`;
      case 'delete_entity':
        return `Deleted entity ${operation.entityId}`;
      case 'update_property':
        return `Updated property ${(operation as any).propertyPath} of ${operation.entityId}`;
      default:
        return `Performed ${operation.type} operation`;
    }
  }

  private extractAffectedEntities(operation: Operation): string[] {
    const entities: string[] = [];

    // 根据操作类型提取实体ID
    if ('entityId' in operation && operation.entityId) {
      entities.push(operation.entityId);
    }

    // 对于有数据属性的操作类型，从数据中提取
    if ('data' in operation && operation.data) {
      if (operation.data.entityId) {
        entities.push(operation.data.entityId);
      }
      if (operation.data.componentId) {
        entities.push(operation.data.componentId);
      }
    }

    return entities;
  }

  private determineChangeType(operation: Operation): ChangeType {
    switch (operation.type) {
      case 'create_entity':
        return ChangeType.CREATE;
      case 'delete_entity':
        return ChangeType.DELETE;
      case 'update_property':
        return ChangeType.PROPERTY_CHANGE;
      case 'move_entity':
        return ChangeType.MOVE;
      case 'add_component':
        return ChangeType.COMPONENT_ADD;
      case 'remove_component':
        return ChangeType.COMPONENT_REMOVE;
      case 'update_component':
        return ChangeType.COMPONENT_UPDATE;
      default:
        return ChangeType.UPDATE;
    }
  }

  private generateRevertOperations(versionId: string): Operation[] {
    // 生成回滚操作的逻辑
    // 这里需要根据具体的操作类型生成相应的逆操作
    console.log(`生成版本 ${versionId} 的回滚操作`);
    return [];
  }

  private detectMergeConflicts(_sourceBranchId: string, _targetBranchId: string): string[] {
    // 检测合并冲突的逻辑
    // 比较两个分支的变更，找出冲突的实体或属性
    return [];
  }

  private generateMergeOperations(_sourceBranchId: string, _targetBranchId: string): Operation[] {
    // 生成合并操作的逻辑
    // 将源分支的变更应用到目标分支
    return [];
  }

  private generateSessionAchievements(session: CollaborationSession): string[] {
    const achievements: string[] = [];
    
    if (session.operations.length > 100) {
      achievements.push('Productive Session');
    }
    
    if (session.participants.length > 5) {
      achievements.push('Team Collaboration');
    }
    
    const duration = (session.endTime || Date.now()) - session.startTime;
    if (duration > 3600000) { // 1 hour
      achievements.push('Marathon Session');
    }
    
    return achievements;
  }
}

export default CollaborationHistoryService;
