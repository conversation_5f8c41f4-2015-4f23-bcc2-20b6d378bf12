/**
 * 软体物理节点集合
 * 提供布料系统、软体模拟、弹簧约束等软体物理功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Mesh, BufferGeometry } from 'three';

/**
 * 软体类型枚举
 */
export enum SoftBodyType {
  CLOTH = 'cloth',
  ROPE = 'rope',
  SOFT_BODY = 'soft_body',
  FLUID = 'fluid'
}

/**
 * 约束类型枚举
 */
export enum ConstraintType {
  DISTANCE = 'distance',
  SPRING = 'spring',
  BEND = 'bend',
  VOLUME = 'volume'
}

/**
 * 软体配置接口
 */
export interface SoftBodyConfig {
  type: SoftBodyType;
  mass: number;
  stiffness: number;
  damping: number;
  friction: number;
  restitution: number;
  iterations: number;
  gravity: Vector3;
  windForce: Vector3;
  selfCollision: boolean;
}

/**
 * 粒子接口
 */
export interface Particle {
  id: string;
  position: Vector3;
  velocity: Vector3;
  force: Vector3;
  mass: number;
  pinned: boolean;
  userData?: any;
}

/**
 * 约束接口
 */
export interface Constraint {
  id: string;
  type: ConstraintType;
  particleA: string;
  particleB: string;
  restLength: number;
  stiffness: number;
  damping: number;
  enabled: boolean;
}

/**
 * 软体物理管理器
 */
class SoftBodyManager {
  private softBodies: Map<string, SoftBody> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 创建软体
   */
  createSoftBody(bodyId: string, config: SoftBodyConfig, geometry?: BufferGeometry): SoftBody {
    const softBody = new SoftBody(bodyId, config, geometry);
    this.softBodies.set(bodyId, softBody);
    this.emit('softBodyCreated', { bodyId, softBody });
    
    Debug.log('SoftBodyManager', `软体创建: ${bodyId} (${config.type})`);
    return softBody;
  }

  /**
   * 更新软体物理
   */
  update(deltaTime: number): void {
    for (const softBody of this.softBodies.values()) {
      if (softBody.enabled) {
        softBody.update(deltaTime);
      }
    }
  }

  /**
   * 获取软体
   */
  getSoftBody(bodyId: string): SoftBody | undefined {
    return this.softBodies.get(bodyId);
  }

  /**
   * 移除软体
   */
  removeSoftBody(bodyId: string): boolean {
    const removed = this.softBodies.delete(bodyId);
    if (removed) {
      this.emit('softBodyRemoved', { bodyId });
      Debug.log('SoftBodyManager', `软体移除: ${bodyId}`);
    }
    return removed;
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('SoftBodyManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.softBodies.clear();
    this.eventListeners.clear();
  }
}

/**
 * 软体类
 */
class SoftBody {
  public id: string;
  public config: SoftBodyConfig;
  public particles: Map<string, Particle> = new Map();
  public constraints: Map<string, Constraint> = new Map();
  public enabled: boolean = true;
  public mesh?: Mesh;

  constructor(id: string, config: SoftBodyConfig, geometry?: BufferGeometry) {
    this.id = id;
    this.config = config;
    
    if (geometry) {
      this.initializeFromGeometry(geometry);
    }
  }

  /**
   * 从几何体初始化
   */
  private initializeFromGeometry(geometry: BufferGeometry): void {
    const positions = geometry.attributes.position.array as Float32Array;
    const particleMass = this.config.mass / (positions.length / 3);

    // 创建粒子
    for (let i = 0; i < positions.length; i += 3) {
      const particleId = `particle_${i / 3}`;
      const particle: Particle = {
        id: particleId,
        position: new Vector3(positions[i], positions[i + 1], positions[i + 2]),
        velocity: new Vector3(),
        force: new Vector3(),
        mass: particleMass,
        pinned: false
      };
      this.particles.set(particleId, particle);
    }

    // 创建约束（简化实现）
    this.createConstraints();
  }

  /**
   * 创建约束
   */
  private createConstraints(): void {
    const particleArray = Array.from(this.particles.values());
    
    // 创建距离约束
    for (let i = 0; i < particleArray.length - 1; i++) {
      const particleA = particleArray[i];
      const particleB = particleArray[i + 1];
      
      const constraintId = `constraint_${i}`;
      const constraint: Constraint = {
        id: constraintId,
        type: ConstraintType.DISTANCE,
        particleA: particleA.id,
        particleB: particleB.id,
        restLength: particleA.position.distanceTo(particleB.position),
        stiffness: this.config.stiffness,
        damping: this.config.damping,
        enabled: true
      };
      
      this.constraints.set(constraintId, constraint);
    }
  }

  /**
   * 更新软体
   */
  update(deltaTime: number): void {
    // 应用力
    this.applyForces(deltaTime);
    
    // 积分
    this.integrate(deltaTime);
    
    // 满足约束
    for (let i = 0; i < this.config.iterations; i++) {
      this.satisfyConstraints();
    }
    
    // 更新网格
    this.updateMesh();
  }

  /**
   * 应用力
   */
  private applyForces(deltaTime: number): void {
    for (const particle of this.particles.values()) {
      if (particle.pinned) continue;
      
      // 重力
      particle.force.add(this.config.gravity.clone().multiplyScalar(particle.mass));
      
      // 风力
      particle.force.add(this.config.windForce);
      
      // 阻尼
      const dampingForce = particle.velocity.clone().multiplyScalar(-this.config.damping);
      particle.force.add(dampingForce);
    }
  }

  /**
   * 积分
   */
  private integrate(deltaTime: number): void {
    for (const particle of this.particles.values()) {
      if (particle.pinned) continue;
      
      // Verlet积分
      const acceleration = particle.force.clone().divideScalar(particle.mass);
      particle.velocity.add(acceleration.multiplyScalar(deltaTime));
      particle.position.add(particle.velocity.clone().multiplyScalar(deltaTime));
      
      // 清除力
      particle.force.set(0, 0, 0);
    }
  }

  /**
   * 满足约束
   */
  private satisfyConstraints(): void {
    for (const constraint of this.constraints.values()) {
      if (!constraint.enabled) continue;
      
      const particleA = this.particles.get(constraint.particleA);
      const particleB = this.particles.get(constraint.particleB);
      
      if (!particleA || !particleB) continue;
      
      this.satisfyDistanceConstraint(particleA, particleB, constraint);
    }
  }

  /**
   * 满足距离约束
   */
  private satisfyDistanceConstraint(particleA: Particle, particleB: Particle, constraint: Constraint): void {
    const delta = particleB.position.clone().sub(particleA.position);
    const distance = delta.length();
    const difference = (distance - constraint.restLength) / distance;
    
    const correction = delta.multiplyScalar(difference * constraint.stiffness * 0.5);
    
    if (!particleA.pinned) {
      particleA.position.add(correction);
    }
    if (!particleB.pinned) {
      particleB.position.sub(correction);
    }
  }

  /**
   * 更新网格
   */
  private updateMesh(): void {
    if (!this.mesh || !this.mesh.geometry) return;

    const positions = this.mesh.geometry.attributes.position.array as Float32Array;
    const particles = Array.from(this.particles.values());

    for (let i = 0; i < particles.length && i * 3 < positions.length; i++) {
      const particle = particles[i];
      positions[i * 3] = particle.position.x;
      positions[i * 3 + 1] = particle.position.y;
      positions[i * 3 + 2] = particle.position.z;
    }

    this.mesh.geometry.attributes.position.needsUpdate = true;
    this.mesh.geometry.computeVertexNormals();
  }

  /**
   * 固定粒子
   */
  pinParticle(particleId: string, pinned: boolean = true): boolean {
    const particle = this.particles.get(particleId);
    if (particle) {
      particle.pinned = pinned;
      return true;
    }
    return false;
  }

  /**
   * 应用外力
   */
  applyForce(force: Vector3, particleId?: string): void {
    if (particleId) {
      const particle = this.particles.get(particleId);
      if (particle) {
        particle.force.add(force);
      }
    } else {
      // 应用到所有粒子
      for (const particle of this.particles.values()) {
        particle.force.add(force);
      }
    }
  }
}

/**
 * 布料系统节点
 */
export class ClothSystemNode extends VisualScriptNode {
  public static readonly TYPE = 'ClothSystem';
  public static readonly NAME = '布料系统';
  public static readonly DESCRIPTION = '布料物理模拟';

  private static softBodyManager: SoftBodyManager = new SoftBodyManager();

  constructor(nodeType: string = ClothSystemNode.TYPE, name: string = ClothSystemNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建布料');
    this.addInput('update', 'trigger', '更新物理');
    this.addInput('applyForce', 'trigger', '应用力');
    this.addInput('pinVertex', 'trigger', '固定顶点');
    this.addInput('clothId', 'string', '布料ID');
    this.addInput('geometry', 'object', '几何体');
    this.addInput('stiffness', 'number', '刚度');
    this.addInput('damping', 'number', '阻尼');
    this.addInput('mass', 'number', '质量');
    this.addInput('force', 'object', '外力');
    this.addInput('vertexIndex', 'number', '顶点索引');

    // 输出端口
    this.addOutput('cloth', 'object', '布料对象');
    this.addOutput('clothId', 'string', '布料ID');
    this.addOutput('particleCount', 'number', '粒子数量');
    this.addOutput('constraintCount', 'number', '约束数量');
    this.addOutput('onCreated', 'trigger', '布料创建完成');
    this.addOutput('onUpdated', 'trigger', '物理更新完成');
    this.addOutput('onForceApplied', 'trigger', '力应用完成');
    this.addOutput('onVertexPinned', 'trigger', '顶点固定完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;
      const applyForceTrigger = inputs?.applyForce;
      const pinVertexTrigger = inputs?.pinVertex;

      if (createTrigger) {
        return this.createCloth(inputs);
      } else if (updateTrigger) {
        return this.updatePhysics(inputs);
      } else if (applyForceTrigger) {
        return this.applyForce(inputs);
      } else if (pinVertexTrigger) {
        return this.pinVertex(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('ClothSystemNode', '布料系统操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createCloth(inputs: any): any {
    const clothId = inputs?.clothId as string || this.generateClothId();
    const geometry = inputs?.geometry as BufferGeometry;
    const stiffness = inputs?.stiffness as number || 0.8;
    const damping = inputs?.damping as number || 0.1;
    const mass = inputs?.mass as number || 1.0;

    if (!geometry) {
      throw new Error('未提供几何体');
    }

    const config: SoftBodyConfig = {
      type: SoftBodyType.CLOTH,
      mass,
      stiffness,
      damping,
      friction: 0.3,
      restitution: 0.1,
      iterations: 3,
      gravity: new Vector3(0, -9.81, 0),
      windForce: new Vector3(0, 0, 0),
      selfCollision: false
    };

    const cloth = ClothSystemNode.softBodyManager.createSoftBody(clothId, config, geometry);

    Debug.log('ClothSystemNode', `布料创建: ${clothId}`);

    return {
      cloth,
      clothId,
      particleCount: cloth.particles.size,
      constraintCount: cloth.constraints.size,
      onCreated: true,
      onUpdated: false,
      onForceApplied: false,
      onVertexPinned: false,
      onError: false
    };
  }

  private updatePhysics(inputs: any): any {
    const deltaTime = 1/60; // 固定时间步长
    ClothSystemNode.softBodyManager.update(deltaTime);

    Debug.log('ClothSystemNode', '布料物理更新完成');

    return {
      cloth: null,
      clothId: '',
      particleCount: 0,
      constraintCount: 0,
      onCreated: false,
      onUpdated: true,
      onForceApplied: false,
      onVertexPinned: false,
      onError: false
    };
  }

  private applyForce(inputs: any): any {
    const clothId = inputs?.clothId as string;
    const force = inputs?.force as Vector3;

    if (!clothId || !force) {
      throw new Error('未提供布料ID或力向量');
    }

    const cloth = ClothSystemNode.softBodyManager.getSoftBody(clothId);
    if (!cloth) {
      throw new Error('布料不存在');
    }

    cloth.applyForce(force);

    Debug.log('ClothSystemNode', `力应用到布料: ${clothId}`);

    return {
      cloth,
      clothId,
      particleCount: cloth.particles.size,
      constraintCount: cloth.constraints.size,
      onCreated: false,
      onUpdated: false,
      onForceApplied: true,
      onVertexPinned: false,
      onError: false
    };
  }

  private pinVertex(inputs: any): any {
    const clothId = inputs?.clothId as string;
    const vertexIndex = inputs?.vertexIndex as number;

    if (!clothId || vertexIndex === undefined) {
      throw new Error('未提供布料ID或顶点索引');
    }

    const cloth = ClothSystemNode.softBodyManager.getSoftBody(clothId);
    if (!cloth) {
      throw new Error('布料不存在');
    }

    const particleId = `particle_${vertexIndex}`;
    const success = cloth.pinParticle(particleId, true);

    if (!success) {
      throw new Error('顶点固定失败');
    }

    Debug.log('ClothSystemNode', `顶点固定: ${clothId}, 索引=${vertexIndex}`);

    return {
      cloth,
      clothId,
      particleCount: cloth.particles.size,
      constraintCount: cloth.constraints.size,
      onCreated: false,
      onUpdated: false,
      onForceApplied: false,
      onVertexPinned: true,
      onError: false
    };
  }

  private generateClothId(): string {
    return 'cloth_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      cloth: null,
      clothId: '',
      particleCount: 0,
      constraintCount: 0,
      onCreated: false,
      onUpdated: false,
      onForceApplied: false,
      onVertexPinned: false,
      onError: false
    };
  }
}
