import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  root: '.',
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      external: [],
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'antd'],
          three: ['three'],
          physics: ['cannon-es']
        }
      },
      onwarn(warning, warn) {
        // 忽略模块导出相关的警告
        if (warning.code === 'UNRESOLVED_IMPORT' ||
            warning.code === 'MISSING_EXPORT' ||
            (warning.message && (
              warning.message.includes('cannon-es') ||
              warning.message.includes('GLTFLoader') ||
              warning.message.includes('DRACOLoader') ||
              warning.message.includes('KTX2Loader') ||
              warning.message.includes('GLTFExporter') ||
              warning.message.includes('Compound')
            ))) {
          return;
        }
        warn(warning);
      }
    }
  },
  optimizeDeps: {
    include: [
      'cannon-es',
      'three',
      'three/examples/jsm/loaders/GLTFLoader',
      'three/examples/jsm/loaders/DRACOLoader',
      'three/examples/jsm/loaders/KTX2Loader',
      'three/examples/jsm/exporters/GLTFExporter'
    ],
    exclude: []
  },
  ssr: {
    noExternal: ['cannon-es', 'three']
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        additionalData: '@import "./src/styles/variables.less";',
      },
    },
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
        ws: true,
      },
      '/ws': {
        target: 'ws://localhost:3007',
        ws: true,
        changeOrigin: true,
      },
    },
  },
});
