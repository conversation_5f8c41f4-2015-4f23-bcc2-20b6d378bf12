/**
 * 虚拟交互节点
 * 将检测到的手势和动作映射到虚拟环境中的交互，支持复杂的虚拟现实交互
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Quaternion, Raycaster, Object3D } from 'three';

/**
 * 手势类型枚举
 */
export enum GestureType {
  UNKNOWN = 'unknown',
  OPEN_HAND = 'open_hand',
  FIST = 'fist',
  POINTING = 'pointing',
  THUMBS_UP = 'thumbs_up',
  THUMBS_DOWN = 'thumbs_down',
  PEACE = 'peace',
  OK = 'ok',
  GRAB = 'grab',
  PINCH = 'pinch',
  ROCK = 'rock',
  PAPER = 'paper',
  SCISSORS = 'scissors',
  CALL_ME = 'call_me',
  LOVE_YOU = 'love_you'
}

/**
 * 交互动作枚举
 */
export enum InteractionAction {
  GRAB_OBJECT = 'grab_object',
  RELEASE_OBJECT = 'release_object',
  POINT_AT_OBJECT = 'point_at_object',
  GESTURE_COMMAND = 'gesture_command',
  WAVE_HAND = 'wave_hand',
  PINCH_ZOOM = 'pinch_zoom',
  ROTATE_OBJECT = 'rotate_object',
  SCALE_OBJECT = 'scale_object',
  TELEPORT = 'teleport',
  MENU_ACTIVATE = 'menu_activate',
  CONFIRM_ACTION = 'confirm_action',
  CANCEL_ACTION = 'cancel_action',
  NAVIGATE = 'navigate',
  SELECT = 'select',
  DESELECT = 'deselect'
}

/**
 * 手势识别结果
 */
export interface GestureResult {
  type: GestureType;
  confidence: number;
  hand: 'left' | 'right';
  position: Vector3;
  timestamp: number;
  fingerStates?: FingerState[];
  handOrientation?: HandOrientation;
}

/**
 * 手指状态
 */
export interface FingerState {
  finger: 'thumb' | 'index' | 'middle' | 'ring' | 'pinky';
  extended: boolean;
  confidence: number;
  angle: number;
}

/**
 * 手部方向
 */
export interface HandOrientation {
  pitch: number;
  yaw: number;
  roll: number;
}

/**
 * 虚拟对象接口
 */
export interface VirtualObject {
  id: string;
  interactable: boolean;
  grabbable: boolean;
  scalable: boolean;
  rotatable: boolean;
  onGrab?: (hand: 'left' | 'right') => void;
  onRelease?: (hand: 'left' | 'right') => void;
  onHover?: (hand: 'left' | 'right') => void;
  onPoint?: (hand: 'left' | 'right') => void;
}

/**
 * 检查对象是否为虚拟对象
 */
export function isVirtualObject(object: Object3D): object is Object3D & VirtualObject {
  return 'interactable' in object && typeof (object as any).interactable === 'boolean';
}

/**
 * 交互状态
 */
export interface InteractionState {
  isGrabbing: boolean;
  grabbedObject?: Object3D & VirtualObject;
  isPointing: boolean;
  pointedObject?: Object3D & VirtualObject;
  isHovering: boolean;
  hoveredObject?: Object3D & VirtualObject;
  lastInteractionTime: number;
}

/**
 * 虚拟交互节点配置
 */
export interface VirtualInteractionNodeConfig {
  /** 是否启用物体交互 */
  enableObjectInteraction: boolean;
  /** 是否启用手势命令 */
  enableGestureCommands: boolean;
  /** 是否启用双手协作 */
  enableBimanualInteraction: boolean;
  /** 交互距离阈值 */
  interactionDistance: number;
  /** 抓取阈值 */
  grabThreshold: number;
  /** 释放阈值 */
  releaseThreshold: number;
  /** 手势置信度阈值 */
  gestureConfidenceThreshold: number;
  /** 射线检测距离 */
  raycastDistance: number;
  /** 是否启用物理交互 */
  enablePhysicsInteraction: boolean;
  /** 是否启用触觉反馈 */
  enableHapticFeedback: boolean;
  /** 是否启用调试模式 */
  debug: boolean;
}

/**
 * 交互事件数据
 */
export interface InteractionEventData {
  action: InteractionAction;
  targetObject?: Object3D & VirtualObject;
  position: Vector3;
  gesture?: GestureResult;
  confidence: number;
  timestamp: number;
  hand: 'left' | 'right';
  duration?: number;
  force?: number;
  metadata?: any;
}

/**
 * 高级虚拟交互管理器
 */
class AdvancedVirtualInteractionManager {
  private scene: Object3D;
  private raycaster: Raycaster;
  private interactableObjects: (Object3D & VirtualObject)[] = [];
  private leftHandState: InteractionState;
  private rightHandState: InteractionState;
  private config: VirtualInteractionNodeConfig;
  private eventListeners: Map<string, Function[]> = new Map();

  constructor(scene: Object3D, config: VirtualInteractionNodeConfig) {
    this.scene = scene;
    this.config = config;
    this.raycaster = new Raycaster();
    this.raycaster.far = config.raycastDistance;

    this.leftHandState = this.createInitialInteractionState();
    this.rightHandState = this.createInitialInteractionState();

    this.updateInteractableObjects();
  }

  /**
   * 创建初始交互状态
   */
  private createInitialInteractionState(): InteractionState {
    return {
      isGrabbing: false,
      isPointing: false,
      isHovering: false,
      lastInteractionTime: 0
    };
  }

  /**
   * 更新可交互对象列表
   */
  private updateInteractableObjects(): void {
    this.interactableObjects = [];
    this.scene.traverse((object) => {
      if (isVirtualObject(object)) {
        this.interactableObjects.push(object);
      }
    });
  }

  /**
   * 处理手势交互
   */
  processGestureInteraction(gesture: GestureResult): InteractionEventData[] {
    const events: InteractionEventData[] = [];
    const handState = gesture.hand === 'left' ? this.leftHandState : this.rightHandState;

    // 更新射线检测
    const intersectedObject = this.performRaycast(gesture.position, gesture.handOrientation);

    // 处理不同手势类型
    switch (gesture.type) {
      case GestureType.GRAB:
        events.push(...this.handleGrabGesture(gesture, handState, intersectedObject));
        break;

      case GestureType.OPEN_HAND:
        events.push(...this.handleOpenHandGesture(gesture, handState));
        break;

      case GestureType.POINTING:
        events.push(...this.handlePointingGesture(gesture, handState, intersectedObject));
        break;

      case GestureType.PINCH:
        events.push(...this.handlePinchGesture(gesture, handState, intersectedObject));
        break;

      case GestureType.THUMBS_UP:
        events.push(...this.handleThumbsUpGesture(gesture, handState));
        break;

      case GestureType.OK:
        events.push(...this.handleOKGesture(gesture, handState));
        break;

      default:
        // 处理其他手势
        events.push(...this.handleGenericGesture(gesture, handState, intersectedObject));
        break;
    }

    // 更新悬停状态
    this.updateHoverState(gesture, handState, intersectedObject);

    return events;
  }

  /**
   * 执行射线检测
   */
  private performRaycast(position: Vector3, orientation?: HandOrientation): (Object3D & VirtualObject) | null {
    // 计算射线方向
    const direction = new Vector3(0, 0, -1); // 默认向前

    if (orientation) {
      // 根据手部方向调整射线方向
      const quaternion = new Quaternion();
      quaternion.setFromEuler({
        x: orientation.pitch * Math.PI / 180,
        y: orientation.yaw * Math.PI / 180,
        z: orientation.roll * Math.PI / 180
      } as any);
      direction.applyQuaternion(quaternion);
    }

    this.raycaster.set(position, direction);
    const intersects = this.raycaster.intersectObjects(this.interactableObjects, true);

    if (intersects.length > 0) {
      const object = intersects[0].object;
      if (isVirtualObject(object)) {
        return object;
      }
    }

    return null;
  }

  /**
   * 处理抓取手势
   */
  private handleGrabGesture(gesture: GestureResult, handState: InteractionState, target?: (Object3D & VirtualObject) | null): InteractionEventData[] {
    const events: InteractionEventData[] = [];

    if (target && target.grabbable && !handState.isGrabbing) {
      // 开始抓取
      handState.isGrabbing = true;
      handState.grabbedObject = target;
      handState.lastInteractionTime = Date.now();

      // 调用对象的抓取回调
      if (target.onGrab) {
        target.onGrab(gesture.hand);
      }

      events.push({
        action: InteractionAction.GRAB_OBJECT,
        targetObject: target,
        position: gesture.position,
        gesture,
        confidence: gesture.confidence,
        timestamp: Date.now(),
        hand: gesture.hand
      });

      this.emit('objectGrabbed', { object: target, hand: gesture.hand });
    }

    return events;
  }

  /**
   * 处理张开手势
   */
  private handleOpenHandGesture(gesture: GestureResult, handState: InteractionState): InteractionEventData[] {
    const events: InteractionEventData[] = [];

    if (handState.isGrabbing && handState.grabbedObject) {
      // 释放对象
      const releasedObject = handState.grabbedObject;
      handState.isGrabbing = false;
      handState.grabbedObject = undefined;

      // 调用对象的释放回调
      if (releasedObject.onRelease) {
        releasedObject.onRelease(gesture.hand);
      }

      events.push({
        action: InteractionAction.RELEASE_OBJECT,
        targetObject: releasedObject,
        position: gesture.position,
        gesture,
        confidence: gesture.confidence,
        timestamp: Date.now(),
        hand: gesture.hand,
        duration: Date.now() - handState.lastInteractionTime
      });

      this.emit('objectReleased', { object: releasedObject, hand: gesture.hand });
    }

    return events;
  }

  /**
   * 处理指向手势
   */
  private handlePointingGesture(gesture: GestureResult, handState: InteractionState, target?: (Object3D & VirtualObject) | null): InteractionEventData[] {
    const events: InteractionEventData[] = [];

    if (target && !handState.isPointing) {
      handState.isPointing = true;
      handState.pointedObject = target;

      // 调用对象的指向回调
      if (target.onPoint) {
        target.onPoint(gesture.hand);
      }

      events.push({
        action: InteractionAction.POINT_AT_OBJECT,
        targetObject: target,
        position: gesture.position,
        gesture,
        confidence: gesture.confidence,
        timestamp: Date.now(),
        hand: gesture.hand
      });

      this.emit('objectPointed', { object: target, hand: gesture.hand });
    } else if (!target && handState.isPointing) {
      // 停止指向
      handState.isPointing = false;
      handState.pointedObject = undefined;
    }

    return events;
  }

  /**
   * 处理捏取手势
   */
  private handlePinchGesture(gesture: GestureResult, _handState: InteractionState, target?: (Object3D & VirtualObject) | null): InteractionEventData[] {
    const events: InteractionEventData[] = [];

    if (target && target.scalable) {
      events.push({
        action: InteractionAction.PINCH_ZOOM,
        targetObject: target,
        position: gesture.position,
        gesture,
        confidence: gesture.confidence,
        timestamp: Date.now(),
        hand: gesture.hand
      });

      this.emit('objectPinched', { object: target, hand: gesture.hand });
    }

    return events;
  }

  /**
   * 处理竖拇指手势
   */
  private handleThumbsUpGesture(gesture: GestureResult, _handState: InteractionState): InteractionEventData[] {
    const events: InteractionEventData[] = [];

    events.push({
      action: InteractionAction.CONFIRM_ACTION,
      position: gesture.position,
      gesture,
      confidence: gesture.confidence,
      timestamp: Date.now(),
      hand: gesture.hand
    });

    this.emit('actionConfirmed', { hand: gesture.hand });
    return events;
  }

  /**
   * 处理OK手势
   */
  private handleOKGesture(gesture: GestureResult, _handState: InteractionState): InteractionEventData[] {
    const events: InteractionEventData[] = [];

    events.push({
      action: InteractionAction.MENU_ACTIVATE,
      position: gesture.position,
      gesture,
      confidence: gesture.confidence,
      timestamp: Date.now(),
      hand: gesture.hand
    });

    this.emit('menuActivated', { hand: gesture.hand });
    return events;
  }

  /**
   * 处理通用手势
   */
  private handleGenericGesture(gesture: GestureResult, _handState: InteractionState, target?: (Object3D & VirtualObject) | null): InteractionEventData[] {
    const events: InteractionEventData[] = [];

    events.push({
      action: InteractionAction.GESTURE_COMMAND,
      targetObject: target,
      position: gesture.position,
      gesture,
      confidence: gesture.confidence,
      timestamp: Date.now(),
      hand: gesture.hand
    });

    return events;
  }

  /**
   * 更新悬停状态
   */
  private updateHoverState(gesture: GestureResult, handState: InteractionState, target?: (Object3D & VirtualObject) | null): void {
    if (target && !handState.isHovering) {
      handState.isHovering = true;
      handState.hoveredObject = target;

      if (target.onHover) {
        target.onHover(gesture.hand);
      }

      this.emit('objectHovered', { object: target, hand: gesture.hand });
    } else if (!target && handState.isHovering) {
      handState.isHovering = false;
      handState.hoveredObject = undefined;
      this.emit('objectUnhovered', { hand: gesture.hand });
    }
  }

  /**
   * 事件监听
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * 发出事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('VirtualInteractionManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 获取交互状态
   */
  getInteractionState(hand: 'left' | 'right'): InteractionState {
    return hand === 'left' ? this.leftHandState : this.rightHandState;
  }

  /**
   * 添加可交互对象
   */
  addInteractableObject(object: Object3D & VirtualObject): void {
    if (!this.interactableObjects.includes(object)) {
      this.interactableObjects.push(object);
    }
  }

  /**
   * 移除可交互对象
   */
  removeInteractableObject(object: Object3D & VirtualObject): void {
    const index = this.interactableObjects.indexOf(object);
    if (index > -1) {
      this.interactableObjects.splice(index, 1);
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<VirtualInteractionNodeConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.raycaster.far = this.config.raycastDistance;
  }
}

/**
 * 虚拟交互节点
 */
export class VirtualInteractionNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'VirtualInteraction';

  /** 节点名称 */
  public static readonly NAME = '虚拟交互';

  /** 节点描述 */
  public static readonly DESCRIPTION = '将手势和动作映射到虚拟环境交互，支持复杂的VR/AR交互';

  private config: VirtualInteractionNodeConfig;
  private interactionManager: AdvancedVirtualInteractionManager | null = null;
  private lastGestures: { left?: GestureResult; right?: GestureResult } = {};
  private interactionHistory: InteractionEventData[] = [];

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: VirtualInteractionNodeConfig = {
    enableObjectInteraction: true,
    enableGestureCommands: true,
    enableBimanualInteraction: false,
    interactionDistance: 2.0,
    grabThreshold: 0.7,
    releaseThreshold: 0.3,
    gestureConfidenceThreshold: 0.6,
    raycastDistance: 10.0,
    enablePhysicsInteraction: true,
    enableHapticFeedback: false,
    debug: false
  };

  constructor(nodeType: string = VirtualInteractionNode.TYPE, name: string = VirtualInteractionNode.NAME, id?: string) {
    super(nodeType, name, id);

    this.config = { ...VirtualInteractionNode.DEFAULT_CONFIG };
    this.setupPorts();
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput('leftGesture', 'object', '左手手势');
    this.addInput('rightGesture', 'object', '右手手势');
    this.addInput('leftHandPosition', 'object', '左手位置');
    this.addInput('rightHandPosition', 'object', '右手位置');
    this.addInput('scene', 'object', '场景对象');
    this.addInput('targetObjects', 'array', '目标物体');
    this.addInput('process', 'trigger', '处理');
    this.addInput('initialize', 'trigger', '初始化');
    this.addInput('reset', 'trigger', '重置');

    // 配置输入端口
    this.addInput('interactionDistance', 'number', '交互距离');
    this.addInput('grabThreshold', 'number', '抓取阈值');
    this.addInput('releaseThreshold', 'number', '释放阈值');
    this.addInput('gestureConfidenceThreshold', 'number', '手势置信度阈值');
    this.addInput('enableObjectInteraction', 'boolean', '启用物体交互');
    this.addInput('enableGestureCommands', 'boolean', '启用手势命令');
    this.addInput('enableBimanualInteraction', 'boolean', '启用双手协作');

    // 输出端口
    this.addOutput('leftGrabbedObject', 'object', '左手抓取物体');
    this.addOutput('rightGrabbedObject', 'object', '右手抓取物体');
    this.addOutput('leftPointedObject', 'object', '左手指向物体');
    this.addOutput('rightPointedObject', 'object', '右手指向物体');
    this.addOutput('leftHoveredObject', 'object', '左手悬停物体');
    this.addOutput('rightHoveredObject', 'object', '右手悬停物体');

    this.addOutput('interactionAction', 'string', '交互动作');
    this.addOutput('interactionTarget', 'object', '交互目标');
    this.addOutput('interactionPosition', 'object', '交互位置');
    this.addOutput('interactionEvents', 'array', '交互事件列表');

    this.addOutput('isLeftGrabbing', 'boolean', '左手正在抓取');
    this.addOutput('isRightGrabbing', 'boolean', '右手正在抓取');
    this.addOutput('isLeftPointing', 'boolean', '左手正在指向');
    this.addOutput('isRightPointing', 'boolean', '右手正在指向');
    this.addOutput('isLeftHovering', 'boolean', '左手正在悬停');
    this.addOutput('isRightHovering', 'boolean', '右手正在悬停');

    // 事件输出端口
    this.addOutput('onGrab', 'trigger', '抓取事件');
    this.addOutput('onRelease', 'trigger', '释放事件');
    this.addOutput('onPoint', 'trigger', '指向事件');
    this.addOutput('onHover', 'trigger', '悬停事件');
    this.addOutput('onGestureCommand', 'trigger', '手势命令');
    this.addOutput('onInteraction', 'trigger', '交互事件');
    this.addOutput('onConfirm', 'trigger', '确认事件');
    this.addOutput('onCancel', 'trigger', '取消事件');
    this.addOutput('onMenuActivate', 'trigger', '菜单激活');

    // 手势输出端口
    this.addOutput('leftGestureType', 'string', '左手手势类型');
    this.addOutput('rightGestureType', 'string', '右手手势类型');
    this.addOutput('leftGestureConfidence', 'number', '左手手势置信度');
    this.addOutput('rightGestureConfidence', 'number', '右手手势置信度');

    // 统计输出端口
    this.addOutput('totalInteractions', 'number', '总交互次数');
    this.addOutput('interactionHistory', 'array', '交互历史');
    this.addOutput('isInitialized', 'boolean', '已初始化');
  }

  /**
   * 执行节点
   */
  public async execute(inputs?: any): Promise<any> {
    try {
      // 获取输入
      const leftGesture = inputs?.leftGesture as GestureResult;
      const rightGesture = inputs?.rightGesture as GestureResult;
      const scene = inputs?.scene as Object3D;
      const targetObjects = inputs?.targetObjects as VirtualObject[];
      const processTrigger = inputs?.process;
      const initializeTrigger = inputs?.initialize;
      const resetTrigger = inputs?.reset;

      // 更新配置
      const interactionDistance = inputs?.interactionDistance as number;
      const grabThreshold = inputs?.grabThreshold as number;
      const releaseThreshold = inputs?.releaseThreshold as number;
      const gestureConfidenceThreshold = inputs?.gestureConfidenceThreshold as number;
      const enableObjectInteraction = inputs?.enableObjectInteraction as boolean;
      const enableGestureCommands = inputs?.enableGestureCommands as boolean;
      const enableBimanualInteraction = inputs?.enableBimanualInteraction as boolean;

      let configChanged = false;
      if (interactionDistance !== undefined && interactionDistance !== this.config.interactionDistance) {
        this.config.interactionDistance = interactionDistance;
        configChanged = true;
      }
      if (grabThreshold !== undefined && grabThreshold !== this.config.grabThreshold) {
        this.config.grabThreshold = grabThreshold;
        configChanged = true;
      }
      if (releaseThreshold !== undefined && releaseThreshold !== this.config.releaseThreshold) {
        this.config.releaseThreshold = releaseThreshold;
        configChanged = true;
      }
      if (gestureConfidenceThreshold !== undefined && gestureConfidenceThreshold !== this.config.gestureConfidenceThreshold) {
        this.config.gestureConfidenceThreshold = gestureConfidenceThreshold;
        configChanged = true;
      }
      if (enableObjectInteraction !== undefined && enableObjectInteraction !== this.config.enableObjectInteraction) {
        this.config.enableObjectInteraction = enableObjectInteraction;
        configChanged = true;
      }
      if (enableGestureCommands !== undefined && enableGestureCommands !== this.config.enableGestureCommands) {
        this.config.enableGestureCommands = enableGestureCommands;
        configChanged = true;
      }
      if (enableBimanualInteraction !== undefined && enableBimanualInteraction !== this.config.enableBimanualInteraction) {
        this.config.enableBimanualInteraction = enableBimanualInteraction;
        configChanged = true;
      }

      // 处理初始化
      if (initializeTrigger || (scene && !this.interactionManager)) {
        await this.initializeInteractionManager(scene);
      }

      // 处理重置
      if (resetTrigger) {
        this.resetInteractionState();
      }

      // 更新配置
      if (configChanged && this.interactionManager) {
        this.interactionManager.updateConfig(this.config);
      }

      // 处理交互
      if (processTrigger && this.interactionManager) {
        await this.processInteractions(leftGesture, rightGesture, targetObjects);
      }

      // 返回输出
      return this.getNodeOutputs();

    } catch (error) {
      Debug.error('VirtualInteractionNode', '节点执行失败', String(error));
      return {
        onError: true,
        isInitialized: !!this.interactionManager
      };
    }
  }

  /**
   * 初始化交互管理器
   */
  private async initializeInteractionManager(scene: Object3D): Promise<void> {
    try {
      if (!scene) {
        throw new Error('场景对象未提供');
      }

      this.interactionManager = new AdvancedVirtualInteractionManager(scene, this.config);

      // 设置事件监听
      this.setupInteractionEvents();

      Debug.log('VirtualInteractionNode', '交互管理器初始化成功');

    } catch (error) {
      Debug.error('VirtualInteractionNode', '初始化交互管理器失败', error);
      throw error;
    }
  }

  /**
   * 重置交互状态
   */
  private resetInteractionState(): void {
    this.lastGestures = {};
    this.interactionHistory = [];

    if (this.interactionManager) {
      // 重置交互管理器状态
      const leftState = this.interactionManager.getInteractionState('left');
      const rightState = this.interactionManager.getInteractionState('right');

      leftState.isGrabbing = false;
      leftState.isPointing = false;
      leftState.isHovering = false;
      leftState.grabbedObject = undefined;
      leftState.pointedObject = undefined;
      leftState.hoveredObject = undefined;

      rightState.isGrabbing = false;
      rightState.isPointing = false;
      rightState.isHovering = false;
      rightState.grabbedObject = undefined;
      rightState.pointedObject = undefined;
      rightState.hoveredObject = undefined;
    }

    Debug.log('VirtualInteractionNode', '交互状态已重置');
  }

  /**
   * 设置交互事件监听
   */
  private setupInteractionEvents(): void {
    if (!this.interactionManager) return;

    this.interactionManager.on('objectGrabbed', (data: any) => {
      Debug.log('VirtualInteractionNode', `对象被抓取: ${data.object.id} by ${data.hand}`);
    });

    this.interactionManager.on('objectReleased', (data: any) => {
      Debug.log('VirtualInteractionNode', `对象被释放: ${data.object.id} by ${data.hand}`);
    });

    this.interactionManager.on('objectPointed', (data: any) => {
      Debug.log('VirtualInteractionNode', `对象被指向: ${data.object.id} by ${data.hand}`);
    });

    this.interactionManager.on('objectHovered', (data: any) => {
      Debug.log('VirtualInteractionNode', `对象被悬停: ${data.object.id} by ${data.hand}`);
    });

    this.interactionManager.on('actionConfirmed', (data: any) => {
      Debug.log('VirtualInteractionNode', `动作确认 by ${data.hand}`);
    });

    this.interactionManager.on('menuActivated', (data: any) => {
      Debug.log('VirtualInteractionNode', `菜单激活 by ${data.hand}`);
    });
  }

  /**
   * 处理交互
   */
  private async processInteractions(
    leftGesture?: GestureResult,
    rightGesture?: GestureResult,
    targetObjects?: VirtualObject[]
  ): Promise<void> {
    if (!this.interactionManager) {
      Debug.warn('VirtualInteractionNode', '交互管理器未初始化');
      return;
    }

    const events: InteractionEventData[] = [];

    // 更新目标对象
    if (targetObjects) {
      targetObjects.forEach(obj => {
        if (isVirtualObject(obj as any)) {
          this.interactionManager!.addInteractableObject(obj as Object3D & VirtualObject);
        }
      });
    }

    // 处理左手手势
    if (leftGesture && leftGesture.confidence > this.config.gestureConfidenceThreshold) {
      this.lastGestures.left = leftGesture;
      const leftEvents = this.interactionManager.processGestureInteraction(leftGesture);
      events.push(...leftEvents);
    }

    // 处理右手手势
    if (rightGesture && rightGesture.confidence > this.config.gestureConfidenceThreshold) {
      this.lastGestures.right = rightGesture;
      const rightEvents = this.interactionManager.processGestureInteraction(rightGesture);
      events.push(...rightEvents);
    }

    // 处理双手协作手势
    if (this.config.enableBimanualInteraction && leftGesture && rightGesture) {
      const bimanualEvents = this.processBimanualInteraction(leftGesture, rightGesture);
      events.push(...bimanualEvents);
    }

    // 添加到历史记录
    this.interactionHistory.push(...events);

    // 限制历史记录长度
    if (this.interactionHistory.length > 100) {
      this.interactionHistory = this.interactionHistory.slice(-100);
    }
  }

  /**
   * 处理双手协作交互
   */
  private processBimanualInteraction(leftGesture: GestureResult, rightGesture: GestureResult): InteractionEventData[] {
    const events: InteractionEventData[] = [];

    // 双手抓取手势
    if (leftGesture.type === GestureType.GRAB && rightGesture.type === GestureType.GRAB) {
      const distance = leftGesture.position.distanceTo(rightGesture.position);

      if (distance < 0.3) { // 双手靠近
        events.push({
          action: InteractionAction.SCALE_OBJECT,
          position: leftGesture.position.clone().lerp(rightGesture.position, 0.5),
          gesture: leftGesture,
          confidence: Math.min(leftGesture.confidence, rightGesture.confidence),
          timestamp: Date.now(),
          hand: 'left',
          metadata: { bimanual: true, distance }
        });
      }
    }

    // 双手张开手势
    if (leftGesture.type === GestureType.OPEN_HAND && rightGesture.type === GestureType.OPEN_HAND) {
      events.push({
        action: InteractionAction.CONFIRM_ACTION,
        position: leftGesture.position.clone().lerp(rightGesture.position, 0.5),
        gesture: leftGesture,
        confidence: Math.min(leftGesture.confidence, rightGesture.confidence),
        timestamp: Date.now(),
        hand: 'left',
        metadata: { bimanual: true }
      });
    }

    return events;
  }

  /**
   * 获取节点输出值
   */
  private getNodeOutputs(): any {
    const baseOutputs = {
      leftGrabbedObject: null,
      rightGrabbedObject: null,
      leftPointedObject: null,
      rightPointedObject: null,
      leftHoveredObject: null,
      rightHoveredObject: null,
      interactionAction: '',
      interactionTarget: null,
      interactionPosition: new Vector3(),
      interactionEvents: [],
      isLeftGrabbing: false,
      isRightGrabbing: false,
      isLeftPointing: false,
      isRightPointing: false,
      isLeftHovering: false,
      isRightHovering: false,
      onGrab: false,
      onRelease: false,
      onPoint: false,
      onHover: false,
      onGestureCommand: false,
      onInteraction: false,
      onConfirm: false,
      onCancel: false,
      onMenuActivate: false,
      leftGestureType: '',
      rightGestureType: '',
      leftGestureConfidence: 0,
      rightGestureConfidence: 0,
      totalInteractions: this.interactionHistory.length,
      interactionHistory: [...this.interactionHistory],
      isInitialized: !!this.interactionManager
    };

    if (!this.interactionManager) {
      return baseOutputs;
    }

    // 获取交互状态
    const leftState = this.interactionManager.getInteractionState('left');
    const rightState = this.interactionManager.getInteractionState('right');

    // 获取最后的交互事件
    let lastInteraction = null;
    if (this.interactionHistory.length > 0) {
      lastInteraction = this.interactionHistory[this.interactionHistory.length - 1];
    }

    return {
      leftGrabbedObject: leftState.grabbedObject || null,
      rightGrabbedObject: rightState.grabbedObject || null,
      leftPointedObject: leftState.pointedObject || null,
      rightPointedObject: rightState.pointedObject || null,
      leftHoveredObject: leftState.hoveredObject || null,
      rightHoveredObject: rightState.hoveredObject || null,

      interactionAction: lastInteraction?.action || '',
      interactionTarget: lastInteraction?.targetObject || null,
      interactionPosition: lastInteraction?.position || new Vector3(),
      interactionEvents: this.interactionHistory.slice(-10), // 最近10个事件

      isLeftGrabbing: leftState.isGrabbing,
      isRightGrabbing: rightState.isGrabbing,
      isLeftPointing: leftState.isPointing,
      isRightPointing: rightState.isPointing,
      isLeftHovering: leftState.isHovering,
      isRightHovering: rightState.isHovering,

      onGrab: false,
      onRelease: false,
      onPoint: false,
      onHover: false,
      onGestureCommand: false,
      onInteraction: false,
      onConfirm: false,
      onCancel: false,
      onMenuActivate: false,

      leftGestureType: this.lastGestures.left?.type || '',
      rightGestureType: this.lastGestures.right?.type || '',
      leftGestureConfidence: this.lastGestures.left?.confidence || 0,
      rightGestureConfidence: this.lastGestures.right?.confidence || 0,

      totalInteractions: this.interactionHistory.length,
      interactionHistory: [...this.interactionHistory],
      isInitialized: true
    };
  }

  /**
   * 获取节点配置
   */
  public getConfig(): VirtualInteractionNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public async updateConfig(newConfig: Partial<VirtualInteractionNodeConfig>): Promise<void> {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // 如果交互管理器存在，更新其配置
    if (this.interactionManager) {
      this.interactionManager.updateConfig(this.config);
    }

    Debug.log('VirtualInteractionNode', '配置已更新', { oldConfig, newConfig: this.config });
  }

  /**
   * 获取交互历史
   */
  public getInteractionHistory(): InteractionEventData[] {
    return [...this.interactionHistory];
  }

  /**
   * 清除交互历史
   */
  public clearInteractionHistory(): void {
    this.interactionHistory = [];
    Debug.log('VirtualInteractionNode', '交互历史已清除');
  }

  /**
   * 获取当前交互状态
   */
  public getInteractionStates(): { left: InteractionState; right: InteractionState } | null {
    if (!this.interactionManager) {
      return null;
    }

    return {
      left: this.interactionManager.getInteractionState('left'),
      right: this.interactionManager.getInteractionState('right')
    };
  }

  /**
   * 添加可交互对象
   */
  public addInteractableObject(object: Object3D & VirtualObject): void {
    if (this.interactionManager) {
      this.interactionManager.addInteractableObject(object);
    }
  }

  /**
   * 移除可交互对象
   */
  public removeInteractableObject(object: Object3D & VirtualObject): void {
    if (this.interactionManager) {
      this.interactionManager.removeInteractableObject(object);
    }
  }

  /**
   * 获取最后的手势
   */
  public getLastGestures(): { left?: GestureResult; right?: GestureResult } {
    return { ...this.lastGestures };
  }

  /**
   * 检查是否已初始化
   */
  public get initialized(): boolean {
    return !!this.interactionManager;
  }

  /**
   * 获取支持的手势类型
   */
  public static getSupportedGestures(): GestureType[] {
    return Object.values(GestureType);
  }

  /**
   * 获取支持的交互动作
   */
  public static getSupportedActions(): InteractionAction[] {
    return Object.values(InteractionAction);
  }

  /**
   * 销毁节点
   */
  public destroy(): void {
    this.interactionManager = null;
    this.lastGestures = {};
    this.interactionHistory = [];

    super.destroy();
  }

}
