/**
 * VR/AR输入节点集合
 * 提供VR控制器、手势追踪、语音识别等VR/AR输入功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Quaternion } from 'three';

/**
 * VR控制器类型枚举
 */
export enum VRControllerType {
  OCULUS_TOUCH = 'oculus_touch',
  VIVE_CONTROLLER = 'vive_controller',
  VALVE_INDEX = 'valve_index',
  PICO_CONTROLLER = 'pico_controller',
  GENERIC = 'generic'
}

/**
 * 手势类型枚举
 */
export enum GestureType {
  POINT = 'point',
  GRAB = 'grab',
  PINCH = 'pinch',
  THUMBS_UP = 'thumbs_up',
  PEACE = 'peace',
  FIST = 'fist',
  OPEN_PALM = 'open_palm',
  CUSTOM = 'custom'
}

/**
 * 语音命令状态枚举
 */
export enum VoiceCommandState {
  LISTENING = 'listening',
  PROCESSING = 'processing',
  RECOGNIZED = 'recognized',
  ERROR = 'error',
  IDLE = 'idle'
}

/**
 * VR控制器状态接口
 */
export interface VRControllerState {
  id: string;
  type: VRControllerType;
  connected: boolean;
  position: Vector3;
  rotation: Quaternion;
  buttons: { [key: string]: boolean };
  axes: { [key: string]: number };
  hapticIntensity: number;
  battery: number;
}

/**
 * 手势识别结果接口
 */
export interface GestureRecognitionResult {
  gesture: GestureType;
  confidence: number;
  handedness: 'left' | 'right';
  landmarks: Vector3[];
  timestamp: number;
}

/**
 * 语音识别结果接口
 */
export interface VoiceRecognitionResult {
  text: string;
  confidence: number;
  language: string;
  timestamp: number;
  alternatives: string[];
}

/**
 * VR/AR输入管理器
 */
class VRInputManager {
  private controllers: Map<string, VRControllerState> = new Map();
  private gestureRecognizer: any = null;
  private voiceRecognizer: any = null;
  private eventListeners: Map<string, Function[]> = new Map();
  private vrSession: any = null;

  /**
   * 初始化VR输入系统
   */
  async initialize(): Promise<boolean> {
    try {
      // 检查WebXR支持
      if (!navigator.xr) {
        Debug.warn('VRInputManager', 'WebXR不支持');
        return false;
      }

      // 初始化手势识别
      await this.initializeGestureRecognition();

      // 初始化语音识别
      await this.initializeVoiceRecognition();

      Debug.log('VRInputManager', 'VR输入系统初始化完成');
      return true;
    } catch (error) {
      Debug.error('VRInputManager', 'VR输入系统初始化失败', error);
      return false;
    }
  }

  /**
   * 启动VR会话
   */
  async startVRSession(): Promise<boolean> {
    try {
      if (!navigator.xr) {
        throw new Error('WebXR不支持');
      }

      const isSupported = await navigator.xr.isSessionSupported('immersive-vr');
      if (!isSupported) {
        throw new Error('VR会话不支持');
      }

      this.vrSession = await navigator.xr.requestSession('immersive-vr', {
        requiredFeatures: ['local-floor'],
        optionalFeatures: ['hand-tracking', 'bounded-floor']
      });

      this.vrSession.addEventListener('inputsourceschange', this.onInputSourcesChange.bind(this));
      this.vrSession.addEventListener('end', this.onSessionEnd.bind(this));

      this.emit('vrSessionStarted', { session: this.vrSession });
      Debug.log('VRInputManager', 'VR会话启动成功');
      return true;
    } catch (error) {
      Debug.error('VRInputManager', 'VR会话启动失败', error);
      return false;
    }
  }

  /**
   * 停止VR会话
   */
  async stopVRSession(): Promise<void> {
    if (this.vrSession) {
      await this.vrSession.end();
      this.vrSession = null;
      this.emit('vrSessionStopped', {});
      Debug.log('VRInputManager', 'VR会话停止');
    }
  }

  /**
   * 更新控制器状态
   */
  updateControllers(frame: any, referenceSpace: any): void {
    if (!this.vrSession) return;

    for (const inputSource of this.vrSession.inputSources) {
      const controllerId = inputSource.handedness || 'unknown';
      
      if (inputSource.gripSpace) {
        const gripPose = frame.getPose(inputSource.gripSpace, referenceSpace);
        if (gripPose) {
          const controller: VRControllerState = {
            id: controllerId,
            type: this.detectControllerType(inputSource),
            connected: true,
            position: new Vector3().fromArray(gripPose.transform.position),
            rotation: new Quaternion().fromArray(gripPose.transform.orientation),
            buttons: this.getButtonStates(inputSource.gamepad),
            axes: this.getAxesStates(inputSource.gamepad),
            hapticIntensity: 0,
            battery: 1.0
          };

          this.controllers.set(controllerId, controller);
          this.emit('controllerUpdated', { controllerId, controller });
        }
      }
    }
  }

  /**
   * 检测控制器类型
   */
  private detectControllerType(inputSource: any): VRControllerType {
    const profiles = inputSource.profiles || [];
    
    if (profiles.includes('oculus-touch')) {
      return VRControllerType.OCULUS_TOUCH;
    } else if (profiles.includes('htc-vive-controller')) {
      return VRControllerType.VIVE_CONTROLLER;
    } else if (profiles.includes('valve-index-controller')) {
      return VRControllerType.VALVE_INDEX;
    } else if (profiles.includes('pico-controller')) {
      return VRControllerType.PICO_CONTROLLER;
    }
    
    return VRControllerType.GENERIC;
  }

  /**
   * 获取按钮状态
   */
  private getButtonStates(gamepad: any): { [key: string]: boolean } {
    const buttons: { [key: string]: boolean } = {};
    
    if (gamepad && gamepad.buttons) {
      gamepad.buttons.forEach((button: any, index: number) => {
        buttons[`button_${index}`] = button.pressed;
      });
    }
    
    return buttons;
  }

  /**
   * 获取轴状态
   */
  private getAxesStates(gamepad: any): { [key: string]: number } {
    const axes: { [key: string]: number } = {};
    
    if (gamepad && gamepad.axes) {
      gamepad.axes.forEach((axis: number, index: number) => {
        axes[`axis_${index}`] = axis;
      });
    }
    
    return axes;
  }

  /**
   * 初始化手势识别
   */
  private async initializeGestureRecognition(): Promise<void> {
    // 模拟手势识别初始化
    this.gestureRecognizer = {
      initialized: true,
      recognizeGesture: (handData: any) => this.recognizeGesture(handData)
    };
    
    Debug.log('VRInputManager', '手势识别初始化完成');
  }

  /**
   * 识别手势
   */
  recognizeGesture(handData: any): GestureRecognitionResult | null {
    // 简化的手势识别逻辑
    const landmarks = handData.landmarks || [];
    const handedness = handData.handedness || 'right';
    
    // 模拟手势识别
    const gestures = [GestureType.POINT, GestureType.GRAB, GestureType.PINCH, GestureType.OPEN_PALM];
    const gesture = gestures[Math.floor(Math.random() * gestures.length)];
    const confidence = 0.8 + Math.random() * 0.2;
    
    return {
      gesture,
      confidence,
      handedness,
      landmarks,
      timestamp: Date.now()
    };
  }

  /**
   * 初始化语音识别
   */
  private async initializeVoiceRecognition(): Promise<void> {
    try {
      // 检查语音识别支持
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      
      if (!SpeechRecognition) {
        Debug.warn('VRInputManager', '语音识别不支持');
        return;
      }

      this.voiceRecognizer = new SpeechRecognition();
      this.voiceRecognizer.continuous = true;
      this.voiceRecognizer.interimResults = true;
      this.voiceRecognizer.lang = 'zh-CN';

      this.voiceRecognizer.onresult = this.onVoiceRecognitionResult.bind(this);
      this.voiceRecognizer.onerror = this.onVoiceRecognitionError.bind(this);

      Debug.log('VRInputManager', '语音识别初始化完成');
    } catch (error) {
      Debug.error('VRInputManager', '语音识别初始化失败', error);
    }
  }

  /**
   * 开始语音识别
   */
  startVoiceRecognition(): boolean {
    if (!this.voiceRecognizer) {
      Debug.warn('VRInputManager', '语音识别器未初始化');
      return false;
    }

    try {
      this.voiceRecognizer.start();
      this.emit('voiceRecognitionStarted', {});
      Debug.log('VRInputManager', '语音识别开始');
      return true;
    } catch (error) {
      Debug.error('VRInputManager', '语音识别启动失败', error);
      return false;
    }
  }

  /**
   * 停止语音识别
   */
  stopVoiceRecognition(): void {
    if (this.voiceRecognizer) {
      this.voiceRecognizer.stop();
      this.emit('voiceRecognitionStopped', {});
      Debug.log('VRInputManager', '语音识别停止');
    }
  }

  /**
   * 语音识别结果处理
   */
  private onVoiceRecognitionResult(event: any): void {
    const results = event.results;
    const lastResult = results[results.length - 1];
    
    if (lastResult.isFinal) {
      const result: VoiceRecognitionResult = {
        text: lastResult[0].transcript,
        confidence: lastResult[0].confidence,
        language: this.voiceRecognizer.lang,
        timestamp: Date.now(),
        alternatives: Array.from(lastResult).map((alt: any) => alt.transcript)
      };

      this.emit('voiceRecognized', { result });
      Debug.log('VRInputManager', `语音识别结果: ${result.text}`);
    }
  }

  /**
   * 语音识别错误处理
   */
  private onVoiceRecognitionError(event: any): void {
    Debug.error('VRInputManager', '语音识别错误', event.error);
    this.emit('voiceRecognitionError', { error: event.error });
  }

  /**
   * 输入源变化处理
   */
  private onInputSourcesChange(event: any): void {
    Debug.log('VRInputManager', '输入源变化', event);
    this.emit('inputSourcesChanged', { event });
  }

  /**
   * 会话结束处理
   */
  private onSessionEnd(): void {
    this.vrSession = null;
    this.controllers.clear();
    this.emit('vrSessionEnded', {});
    Debug.log('VRInputManager', 'VR会话结束');
  }

  /**
   * 触发控制器震动
   */
  triggerHapticFeedback(controllerId: string, intensity: number, duration: number): boolean {
    const controller = this.controllers.get(controllerId);
    if (!controller) {
      return false;
    }

    // 模拟触觉反馈
    controller.hapticIntensity = intensity;
    setTimeout(() => {
      controller.hapticIntensity = 0;
    }, duration);

    this.emit('hapticFeedbackTriggered', { controllerId, intensity, duration });
    Debug.log('VRInputManager', `触觉反馈触发: ${controllerId}, 强度=${intensity}, 持续时间=${duration}ms`);
    return true;
  }

  /**
   * 获取控制器状态
   */
  getController(controllerId: string): VRControllerState | undefined {
    return this.controllers.get(controllerId);
  }

  /**
   * 获取所有控制器
   */
  getAllControllers(): VRControllerState[] {
    return Array.from(this.controllers.values());
  }

  /**
   * 检查VR支持
   */
  async checkVRSupport(): Promise<boolean> {
    if (!navigator.xr) {
      return false;
    }

    try {
      return await navigator.xr.isSessionSupported('immersive-vr');
    } catch (error) {
      return false;
    }
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('VRInputManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopVRSession();
    this.stopVoiceRecognition();
    this.controllers.clear();
    this.eventListeners.clear();
    this.gestureRecognizer = null;
    this.voiceRecognizer = null;
  }
}

/**
 * VR控制器节点
 */
export class VRControllerNode extends VisualScriptNode {
  public static readonly TYPE = 'VRController';
  public static readonly NAME = 'VR控制器';
  public static readonly DESCRIPTION = 'VR控制器输入处理';

  private static vrInputManager: VRInputManager = new VRInputManager();

  constructor(nodeType: string = VRControllerNode.TYPE, name: string = VRControllerNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('initialize', 'trigger', '初始化VR');
    this.addInput('startSession', 'trigger', '启动VR会话');
    this.addInput('stopSession', 'trigger', '停止VR会话');
    this.addInput('getController', 'trigger', '获取控制器');
    this.addInput('triggerHaptic', 'trigger', '触发震动');
    this.addInput('controllerId', 'string', '控制器ID');
    this.addInput('intensity', 'number', '震动强度');
    this.addInput('duration', 'number', '震动持续时间');

    // 输出端口
    this.addOutput('controller', 'object', '控制器状态');
    this.addOutput('controllerId', 'string', '控制器ID');
    this.addOutput('position', 'object', '控制器位置');
    this.addOutput('rotation', 'object', '控制器旋转');
    this.addOutput('buttons', 'object', '按钮状态');
    this.addOutput('axes', 'object', '轴状态');
    this.addOutput('connected', 'boolean', '连接状态');
    this.addOutput('vrSupported', 'boolean', 'VR支持状态');
    this.addOutput('onInitialized', 'trigger', '初始化完成');
    this.addOutput('onSessionStarted', 'trigger', 'VR会话启动');
    this.addOutput('onSessionStopped', 'trigger', 'VR会话停止');
    this.addOutput('onControllerUpdated', 'trigger', '控制器更新');
    this.addOutput('onHapticTriggered', 'trigger', '震动触发');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const initializeTrigger = inputs?.initialize;
      const startSessionTrigger = inputs?.startSession;
      const stopSessionTrigger = inputs?.stopSession;
      const getControllerTrigger = inputs?.getController;
      const triggerHapticTrigger = inputs?.triggerHaptic;

      if (initializeTrigger) {
        return await this.initializeVR(inputs);
      } else if (startSessionTrigger) {
        return await this.startVRSession(inputs);
      } else if (stopSessionTrigger) {
        return await this.stopVRSession(inputs);
      } else if (getControllerTrigger) {
        return this.getController(inputs);
      } else if (triggerHapticTrigger) {
        return this.triggerHaptic(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('VRControllerNode', 'VR控制器操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private async initializeVR(inputs: any): Promise<any> {
    const success = await VRControllerNode.vrInputManager.initialize();
    const vrSupported = await VRControllerNode.vrInputManager.checkVRSupport();

    Debug.log('VRControllerNode', `VR初始化${success ? '成功' : '失败'}, 支持状态: ${vrSupported}`);

    return {
      controller: null,
      controllerId: '',
      position: null,
      rotation: null,
      buttons: {},
      axes: {},
      connected: false,
      vrSupported,
      onInitialized: success,
      onSessionStarted: false,
      onSessionStopped: false,
      onControllerUpdated: false,
      onHapticTriggered: false,
      onError: !success
    };
  }

  private async startVRSession(inputs: any): Promise<any> {
    const success = await VRControllerNode.vrInputManager.startVRSession();

    Debug.log('VRControllerNode', `VR会话启动${success ? '成功' : '失败'}`);

    return {
      controller: null,
      controllerId: '',
      position: null,
      rotation: null,
      buttons: {},
      axes: {},
      connected: false,
      vrSupported: true,
      onInitialized: false,
      onSessionStarted: success,
      onSessionStopped: false,
      onControllerUpdated: false,
      onHapticTriggered: false,
      onError: !success
    };
  }

  private async stopVRSession(inputs: any): Promise<any> {
    await VRControllerNode.vrInputManager.stopVRSession();

    Debug.log('VRControllerNode', 'VR会话停止');

    return {
      controller: null,
      controllerId: '',
      position: null,
      rotation: null,
      buttons: {},
      axes: {},
      connected: false,
      vrSupported: true,
      onInitialized: false,
      onSessionStarted: false,
      onSessionStopped: true,
      onControllerUpdated: false,
      onHapticTriggered: false,
      onError: false
    };
  }

  private getController(inputs: any): any {
    const controllerId = inputs?.controllerId as string || 'right';
    const controller = VRControllerNode.vrInputManager.getController(controllerId);

    if (!controller) {
      Debug.warn('VRControllerNode', `控制器不存在: ${controllerId}`);
      return {
        ...this.getDefaultOutputs(),
        controllerId,
        onError: true
      };
    }

    Debug.log('VRControllerNode', `获取控制器状态: ${controllerId}`);

    return {
      controller,
      controllerId,
      position: controller.position,
      rotation: controller.rotation,
      buttons: controller.buttons,
      axes: controller.axes,
      connected: controller.connected,
      vrSupported: true,
      onInitialized: false,
      onSessionStarted: false,
      onSessionStopped: false,
      onControllerUpdated: true,
      onHapticTriggered: false,
      onError: false
    };
  }

  private triggerHaptic(inputs: any): any {
    const controllerId = inputs?.controllerId as string || 'right';
    const intensity = inputs?.intensity as number || 0.5;
    const duration = inputs?.duration as number || 100;

    const success = VRControllerNode.vrInputManager.triggerHapticFeedback(controllerId, intensity, duration);

    Debug.log('VRControllerNode', `触觉反馈${success ? '成功' : '失败'}: ${controllerId}`);

    return {
      controller: null,
      controllerId,
      position: null,
      rotation: null,
      buttons: {},
      axes: {},
      connected: false,
      vrSupported: true,
      onInitialized: false,
      onSessionStarted: false,
      onSessionStopped: false,
      onControllerUpdated: false,
      onHapticTriggered: success,
      onError: !success
    };
  }

  private getDefaultOutputs(): any {
    return {
      controller: null,
      controllerId: '',
      position: null,
      rotation: null,
      buttons: {},
      axes: {},
      connected: false,
      vrSupported: false,
      onInitialized: false,
      onSessionStarted: false,
      onSessionStopped: false,
      onControllerUpdated: false,
      onHapticTriggered: false,
      onError: false
    };
  }
}

/**
 * 手势识别节点
 */
export class GestureRecognitionNode extends VisualScriptNode {
  public static readonly TYPE = 'GestureRecognition';
  public static readonly NAME = '手势识别';
  public static readonly DESCRIPTION = 'VR/AR手势识别';

  private static vrInputManager: VRInputManager = new VRInputManager();

  constructor(nodeType: string = GestureRecognitionNode.TYPE, name: string = GestureRecognitionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('recognize', 'trigger', '识别手势');
    this.addInput('handData', 'object', '手部数据');
    this.addInput('handedness', 'string', '左右手');
    this.addInput('confidenceThreshold', 'number', '置信度阈值');

    // 输出端口
    this.addOutput('gesture', 'string', '识别手势');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('handedness', 'string', '左右手');
    this.addOutput('landmarks', 'array', '手部关键点');
    this.addOutput('timestamp', 'number', '时间戳');
    this.addOutput('onGestureRecognized', 'trigger', '手势识别完成');
    this.addOutput('onError', 'trigger', '识别失败');
  }

  public execute(inputs?: any): any {
    try {
      const recognizeTrigger = inputs?.recognize;

      if (recognizeTrigger) {
        return this.recognizeGesture(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('GestureRecognitionNode', '手势识别失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private recognizeGesture(inputs: any): any {
    const handData = inputs?.handData || {};
    const handedness = inputs?.handedness as string || 'right';
    const confidenceThreshold = inputs?.confidenceThreshold as number || 0.7;

    // 模拟手部数据
    const mockHandData = {
      landmarks: this.generateMockLandmarks(),
      handedness
    };

    const result = GestureRecognitionNode.vrInputManager.recognizeGesture(mockHandData);

    if (!result) {
      throw new Error('手势识别失败');
    }

    if (result.confidence < confidenceThreshold) {
      Debug.warn('GestureRecognitionNode', `手势置信度过低: ${result.confidence}`);
      return {
        ...this.getDefaultOutputs(),
        confidence: result.confidence,
        onError: true
      };
    }

    Debug.log('GestureRecognitionNode', `手势识别成功: ${result.gesture} (置信度: ${result.confidence.toFixed(2)})`);

    return {
      gesture: result.gesture,
      confidence: result.confidence,
      handedness: result.handedness,
      landmarks: result.landmarks,
      timestamp: result.timestamp,
      onGestureRecognized: true,
      onError: false
    };
  }

  private generateMockLandmarks(): Vector3[] {
    const landmarks: Vector3[] = [];

    // 生成21个手部关键点（MediaPipe Hand标准）
    for (let i = 0; i < 21; i++) {
      landmarks.push(new Vector3(
        Math.random() * 2 - 1,
        Math.random() * 2 - 1,
        Math.random() * 0.2
      ));
    }

    return landmarks;
  }

  private getDefaultOutputs(): any {
    return {
      gesture: '',
      confidence: 0,
      handedness: '',
      landmarks: [],
      timestamp: 0,
      onGestureRecognized: false,
      onError: false
    };
  }
}

/**
 * 语音识别节点
 */
export class VoiceRecognitionNode extends VisualScriptNode {
  public static readonly TYPE = 'VoiceRecognition';
  public static readonly NAME = '语音识别';
  public static readonly DESCRIPTION = '语音识别和命令处理';

  private static vrInputManager: VRInputManager = new VRInputManager();

  constructor(nodeType: string = VoiceRecognitionNode.TYPE, name: string = VoiceRecognitionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('startListening', 'trigger', '开始监听');
    this.addInput('stopListening', 'trigger', '停止监听');
    this.addInput('language', 'string', '识别语言');
    this.addInput('confidenceThreshold', 'number', '置信度阈值');
    this.addInput('commands', 'array', '语音命令列表');

    // 输出端口
    this.addOutput('text', 'string', '识别文本');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('language', 'string', '识别语言');
    this.addOutput('command', 'string', '匹配命令');
    this.addOutput('alternatives', 'array', '备选结果');
    this.addOutput('listening', 'boolean', '监听状态');
    this.addOutput('onListeningStarted', 'trigger', '开始监听');
    this.addOutput('onListeningStopped', 'trigger', '停止监听');
    this.addOutput('onTextRecognized', 'trigger', '文本识别完成');
    this.addOutput('onCommandMatched', 'trigger', '命令匹配');
    this.addOutput('onError', 'trigger', '识别失败');
  }

  public execute(inputs?: any): any {
    try {
      const startListeningTrigger = inputs?.startListening;
      const stopListeningTrigger = inputs?.stopListening;

      if (startListeningTrigger) {
        return this.startListening(inputs);
      } else if (stopListeningTrigger) {
        return this.stopListening(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('VoiceRecognitionNode', '语音识别操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private startListening(inputs: any): any {
    const language = inputs?.language as string || 'zh-CN';
    const confidenceThreshold = inputs?.confidenceThreshold as number || 0.7;
    const commands = inputs?.commands as string[] || [];

    const success = VoiceRecognitionNode.vrInputManager.startVoiceRecognition();

    if (!success) {
      throw new Error('语音识别启动失败');
    }

    // 设置语音识别事件监听
    VoiceRecognitionNode.vrInputManager.on('voiceRecognized', (data: any) => {
      this.handleVoiceRecognition(data.result, confidenceThreshold, commands);
    });

    Debug.log('VoiceRecognitionNode', `语音识别开始: 语言=${language}`);

    return {
      text: '',
      confidence: 0,
      language,
      command: '',
      alternatives: [],
      listening: true,
      onListeningStarted: true,
      onListeningStopped: false,
      onTextRecognized: false,
      onCommandMatched: false,
      onError: false
    };
  }

  private stopListening(inputs: any): any {
    VoiceRecognitionNode.vrInputManager.stopVoiceRecognition();

    Debug.log('VoiceRecognitionNode', '语音识别停止');

    return {
      text: '',
      confidence: 0,
      language: '',
      command: '',
      alternatives: [],
      listening: false,
      onListeningStarted: false,
      onListeningStopped: true,
      onTextRecognized: false,
      onCommandMatched: false,
      onError: false
    };
  }

  private handleVoiceRecognition(result: VoiceRecognitionResult, threshold: number, commands: string[]): any {
    if (result.confidence < threshold) {
      Debug.warn('VoiceRecognitionNode', `语音识别置信度过低: ${result.confidence}`);
      return {
        ...this.getDefaultOutputs(),
        confidence: result.confidence,
        onError: true
      };
    }

    // 匹配语音命令
    const matchedCommand = this.matchCommand(result.text, commands);

    Debug.log('VoiceRecognitionNode', `语音识别结果: ${result.text} (置信度: ${result.confidence.toFixed(2)})`);

    return {
      text: result.text,
      confidence: result.confidence,
      language: result.language,
      command: matchedCommand,
      alternatives: result.alternatives,
      listening: true,
      onListeningStarted: false,
      onListeningStopped: false,
      onTextRecognized: true,
      onCommandMatched: !!matchedCommand,
      onError: false
    };
  }

  private matchCommand(text: string, commands: string[]): string {
    const lowerText = text.toLowerCase();

    for (const command of commands) {
      if (lowerText.includes(command.toLowerCase())) {
        return command;
      }
    }

    return '';
  }

  private getDefaultOutputs(): any {
    return {
      text: '',
      confidence: 0,
      language: '',
      command: '',
      alternatives: [],
      listening: false,
      onListeningStarted: false,
      onListeningStopped: false,
      onTextRecognized: false,
      onCommandMatched: false,
      onError: false
    };
  }
}
