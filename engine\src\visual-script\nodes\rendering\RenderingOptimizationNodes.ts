/**
 * 渲染优化节点集合
 * 提供LOD系统、批处理、实例化渲染、视锥体剔除等渲染优化功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Object3D, Mesh, InstancedMesh, Matrix4, Vector3, Frustum, Camera } from 'three';

/**
 * LOD级别枚举
 */
export enum LODLevel {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  VERY_LOW = 'very_low'
}

/**
 * 批处理类型枚举
 */
export enum BatchType {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  INSTANCED = 'instanced'
}

/**
 * 剔除类型枚举
 */
export enum CullingType {
  FRUSTUM = 'frustum',
  OCCLUSION = 'occlusion',
  DISTANCE = 'distance',
  BACKFACE = 'backface'
}

/**
 * LOD配置接口
 */
export interface LODConfig {
  levels: {
    distance: number;
    level: LODLevel;
    mesh: Mesh;
    triangleCount: number;
  }[];
  hysteresis: number;
  enabled: boolean;
}

/**
 * 批处理配置接口
 */
export interface BatchConfig {
  type: BatchType;
  maxInstances: number;
  dynamicUpdate: boolean;
  frustumCulling: boolean;
  sortObjects: boolean;
}

/**
 * 实例化数据接口
 */
export interface InstanceData {
  matrix: Matrix4;
  color?: Vector3;
  userData?: any;
  visible: boolean;
}

/**
 * 渲染优化管理器
 */
class RenderingOptimizationManager {
  private lodObjects: Map<string, LODObject> = new Map();
  private batchGroups: Map<string, BatchGroup> = new Map();
  private instancedMeshes: Map<string, InstancedMesh> = new Map();
  private cullingManager: CullingManager = new CullingManager();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 创建LOD对象
   */
  createLODObject(objectId: string, config: LODConfig): LODObject {
    const lodObject = new LODObject(objectId, config);
    this.lodObjects.set(objectId, lodObject);
    this.emit('lodObjectCreated', { objectId, lodObject });
    
    Debug.log('RenderingOptimizationManager', `LOD对象创建: ${objectId}`);
    return lodObject;
  }

  /**
   * 更新LOD对象
   */
  updateLOD(camera: Camera): void {
    for (const lodObject of this.lodObjects.values()) {
      lodObject.update(camera);
    }
  }

  /**
   * 创建批处理组
   */
  createBatchGroup(groupId: string, config: BatchConfig): BatchGroup {
    const batchGroup = new BatchGroup(groupId, config);
    this.batchGroups.set(groupId, batchGroup);
    this.emit('batchGroupCreated', { groupId, batchGroup });
    
    Debug.log('RenderingOptimizationManager', `批处理组创建: ${groupId}`);
    return batchGroup;
  }

  /**
   * 创建实例化网格
   */
  createInstancedMesh(meshId: string, geometry: any, material: any, count: number): InstancedMesh {
    const instancedMesh = new InstancedMesh(geometry, material, count);
    this.instancedMeshes.set(meshId, instancedMesh);
    this.emit('instancedMeshCreated', { meshId, instancedMesh });
    
    Debug.log('RenderingOptimizationManager', `实例化网格创建: ${meshId} (${count}个实例)`);
    return instancedMesh;
  }

  /**
   * 更新实例化网格
   */
  updateInstancedMesh(meshId: string, instances: InstanceData[]): boolean {
    const instancedMesh = this.instancedMeshes.get(meshId);
    if (!instancedMesh) {
      return false;
    }

    for (let i = 0; i < instances.length && i < instancedMesh.count; i++) {
      const instance = instances[i];
      instancedMesh.setMatrixAt(i, instance.matrix);
      
      if (instance.color && instancedMesh.instanceColor) {
        instancedMesh.instanceColor.setXYZ(i, instance.color.x, instance.color.y, instance.color.z);
      }
    }

    instancedMesh.instanceMatrix.needsUpdate = true;
    if (instancedMesh.instanceColor) {
      instancedMesh.instanceColor.needsUpdate = true;
    }

    this.emit('instancedMeshUpdated', { meshId, instanceCount: instances.length });
    return true;
  }

  /**
   * 执行视锥体剔除
   */
  performFrustumCulling(camera: Camera, objects: Object3D[]): Object3D[] {
    return this.cullingManager.frustumCull(camera, objects);
  }

  /**
   * 执行距离剔除
   */
  performDistanceCulling(camera: Camera, objects: Object3D[], maxDistance: number): Object3D[] {
    return this.cullingManager.distanceCull(camera, objects, maxDistance);
  }

  /**
   * 获取LOD对象
   */
  getLODObject(objectId: string): LODObject | undefined {
    return this.lodObjects.get(objectId);
  }

  /**
   * 获取批处理组
   */
  getBatchGroup(groupId: string): BatchGroup | undefined {
    return this.batchGroups.get(groupId);
  }

  /**
   * 获取实例化网格
   */
  getInstancedMesh(meshId: string): InstancedMesh | undefined {
    return this.instancedMeshes.get(meshId);
  }

  /**
   * 获取渲染统计
   */
  getRenderingStats(): any {
    return {
      lodObjects: this.lodObjects.size,
      batchGroups: this.batchGroups.size,
      instancedMeshes: this.instancedMeshes.size,
      totalInstances: Array.from(this.instancedMeshes.values()).reduce((sum, mesh) => sum + mesh.count, 0)
    };
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('RenderingOptimizationManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.lodObjects.clear();
    this.batchGroups.clear();
    
    // 清理实例化网格
    for (const instancedMesh of this.instancedMeshes.values()) {
      instancedMesh.dispose();
    }
    this.instancedMeshes.clear();
    
    this.eventListeners.clear();
  }
}

/**
 * LOD对象类
 */
class LODObject {
  public id: string;
  public config: LODConfig;
  public currentLevel: LODLevel = LODLevel.HIGH;
  public currentDistance: number = 0;
  public object3D: Object3D;

  constructor(id: string, config: LODConfig) {
    this.id = id;
    this.config = config;
    this.object3D = new Object3D();
    
    // 添加所有LOD级别的网格
    for (const level of config.levels) {
      this.object3D.add(level.mesh);
      level.mesh.visible = level.level === LODLevel.HIGH;
    }
  }

  /**
   * 更新LOD级别
   */
  update(camera: Camera): void {
    if (!this.config.enabled) return;

    this.currentDistance = camera.position.distanceTo(this.object3D.position);
    
    let newLevel = LODLevel.HIGH;
    for (const level of this.config.levels) {
      if (this.currentDistance >= level.distance) {
        newLevel = level.level;
      } else {
        break;
      }
    }

    // 应用滞后效应
    if (newLevel !== this.currentLevel) {
      const hysteresis = this.config.hysteresis || 0.1;
      const threshold = this.getLevelDistance(newLevel);
      
      if (this.currentDistance > threshold + threshold * hysteresis ||
          this.currentDistance < threshold - threshold * hysteresis) {
        this.setLevel(newLevel);
      }
    }
  }

  /**
   * 设置LOD级别
   */
  private setLevel(level: LODLevel): void {
    if (level === this.currentLevel) return;

    // 隐藏当前级别
    const currentLevelMesh = this.config.levels.find(l => l.level === this.currentLevel)?.mesh;
    if (currentLevelMesh) {
      currentLevelMesh.visible = false;
    }

    // 显示新级别
    const newLevelMesh = this.config.levels.find(l => l.level === level)?.mesh;
    if (newLevelMesh) {
      newLevelMesh.visible = true;
    }

    this.currentLevel = level;
    Debug.log('LODObject', `LOD级别切换: ${this.id} -> ${level} (距离: ${this.currentDistance.toFixed(2)})`);
  }

  /**
   * 获取级别距离
   */
  private getLevelDistance(level: LODLevel): number {
    const levelConfig = this.config.levels.find(l => l.level === level);
    return levelConfig ? levelConfig.distance : 0;
  }
}

/**
 * 批处理组类
 */
class BatchGroup {
  public id: string;
  public config: BatchConfig;
  public objects: Object3D[] = [];
  public batched: boolean = false;

  constructor(id: string, config: BatchConfig) {
    this.id = id;
    this.config = config;
  }

  /**
   * 添加对象到批处理组
   */
  addObject(object: Object3D): void {
    this.objects.push(object);
    this.batched = false;
  }

  /**
   * 从批处理组移除对象
   */
  removeObject(object: Object3D): boolean {
    const index = this.objects.indexOf(object);
    if (index > -1) {
      this.objects.splice(index, 1);
      this.batched = false;
      return true;
    }
    return false;
  }

  /**
   * 执行批处理
   */
  batch(): boolean {
    if (this.objects.length === 0) {
      return false;
    }

    switch (this.config.type) {
      case BatchType.STATIC:
        return this.batchStatic();
      case BatchType.DYNAMIC:
        return this.batchDynamic();
      case BatchType.INSTANCED:
        return this.batchInstanced();
      default:
        return false;
    }
  }

  /**
   * 静态批处理
   */
  private batchStatic(): boolean {
    // 简化的静态批处理实现
    Debug.log('BatchGroup', `静态批处理: ${this.id} (${this.objects.length}个对象)`);
    this.batched = true;
    return true;
  }

  /**
   * 动态批处理
   */
  private batchDynamic(): boolean {
    // 简化的动态批处理实现
    Debug.log('BatchGroup', `动态批处理: ${this.id} (${this.objects.length}个对象)`);
    this.batched = true;
    return true;
  }

  /**
   * 实例化批处理
   */
  private batchInstanced(): boolean {
    // 简化的实例化批处理实现
    Debug.log('BatchGroup', `实例化批处理: ${this.id} (${this.objects.length}个对象)`);
    this.batched = true;
    return true;
  }
}

/**
 * 剔除管理器类
 */
class CullingManager {
  private frustum: Frustum = new Frustum();

  /**
   * 视锥体剔除
   */
  frustumCull(camera: Camera, objects: Object3D[]): Object3D[] {
    // 更新视锥体
    const matrix = new Matrix4().multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
    this.frustum.setFromProjectionMatrix(matrix);

    const visibleObjects: Object3D[] = [];
    
    for (const object of objects) {
      if (this.frustum.intersectsObject(object)) {
        visibleObjects.push(object);
      }
    }

    Debug.log('CullingManager', `视锥体剔除: ${objects.length} -> ${visibleObjects.length}`);
    return visibleObjects;
  }

  /**
   * 距离剔除
   */
  distanceCull(camera: Camera, objects: Object3D[], maxDistance: number): Object3D[] {
    const cameraPosition = camera.position;
    const visibleObjects: Object3D[] = [];
    
    for (const object of objects) {
      const distance = cameraPosition.distanceTo(object.position);
      if (distance <= maxDistance) {
        visibleObjects.push(object);
      }
    }

    Debug.log('CullingManager', `距离剔除: ${objects.length} -> ${visibleObjects.length} (最大距离: ${maxDistance})`);
    return visibleObjects;
  }

  /**
   * 遮挡剔除
   */
  occlusionCull(camera: Camera, objects: Object3D[]): Object3D[] {
    // 简化的遮挡剔除实现
    // 实际实现需要深度缓冲区和遮挡查询
    const visibleObjects = objects.filter(obj => obj.visible);

    Debug.log('CullingManager', `遮挡剔除: ${objects.length} -> ${visibleObjects.length}`);
    return visibleObjects;
  }
}

/**
 * LOD系统节点
 */
export class LODSystemNode extends VisualScriptNode {
  public static readonly TYPE = 'LODSystem';
  public static readonly NAME = 'LOD系统';
  public static readonly DESCRIPTION = '细节层次优化系统';

  private static optimizationManager: RenderingOptimizationManager = new RenderingOptimizationManager();

  constructor(nodeType: string = LODSystemNode.TYPE, name: string = LODSystemNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建LOD对象');
    this.addInput('update', 'trigger', '更新LOD');
    this.addInput('objectId', 'string', '对象ID');
    this.addInput('meshes', 'array', '网格数组');
    this.addInput('distances', 'array', '距离数组');
    this.addInput('camera', 'object', '相机对象');
    this.addInput('hysteresis', 'number', '滞后系数');
    this.addInput('enabled', 'boolean', '启用状态');

    // 输出端口
    this.addOutput('lodObject', 'object', 'LOD对象');
    this.addOutput('objectId', 'string', '对象ID');
    this.addOutput('currentLevel', 'string', '当前级别');
    this.addOutput('currentDistance', 'number', '当前距离');
    this.addOutput('triangleCount', 'number', '三角形数量');
    this.addOutput('onCreated', 'trigger', 'LOD对象创建完成');
    this.addOutput('onUpdated', 'trigger', 'LOD更新完成');
    this.addOutput('onLevelChanged', 'trigger', '级别改变');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;

      if (createTrigger) {
        return this.createLODObject(inputs);
      } else if (updateTrigger) {
        return this.updateLOD(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('LODSystemNode', 'LOD系统操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createLODObject(inputs: any): any {
    const objectId = inputs?.objectId as string || this.generateObjectId();
    const meshes = inputs?.meshes as Mesh[] || [];
    const distances = inputs?.distances as number[] || [10, 50, 100];
    const hysteresis = inputs?.hysteresis as number || 0.1;
    const enabled = inputs?.enabled as boolean ?? true;

    if (meshes.length === 0) {
      throw new Error('未提供网格数组');
    }

    const levels = meshes.map((mesh, index) => ({
      distance: distances[index] || (index + 1) * 20,
      level: this.indexToLODLevel(index),
      mesh,
      triangleCount: this.getTriangleCount(mesh)
    }));

    const config: LODConfig = {
      levels,
      hysteresis,
      enabled
    };

    const lodObject = LODSystemNode.optimizationManager.createLODObject(objectId, config);

    Debug.log('LODSystemNode', `LOD对象创建: ${objectId} (${levels.length}个级别)`);

    return {
      lodObject,
      objectId,
      currentLevel: lodObject.currentLevel,
      currentDistance: lodObject.currentDistance,
      triangleCount: levels[0].triangleCount,
      onCreated: true,
      onUpdated: false,
      onLevelChanged: false,
      onError: false
    };
  }

  private updateLOD(inputs: any): any {
    const objectId = inputs?.objectId as string;
    const camera = inputs?.camera as Camera;

    if (!objectId) {
      throw new Error('未提供对象ID');
    }

    if (!camera) {
      throw new Error('未提供相机对象');
    }

    const lodObject = LODSystemNode.optimizationManager.getLODObject(objectId);
    if (!lodObject) {
      throw new Error('LOD对象不存在');
    }

    const previousLevel = lodObject.currentLevel;
    lodObject.update(camera);
    const levelChanged = previousLevel !== lodObject.currentLevel;

    const currentLevelConfig = lodObject.config.levels.find(l => l.level === lodObject.currentLevel);
    const triangleCount = currentLevelConfig ? currentLevelConfig.triangleCount : 0;

    Debug.log('LODSystemNode', `LOD更新: ${objectId}, 级别=${lodObject.currentLevel}, 距离=${lodObject.currentDistance.toFixed(2)}`);

    return {
      lodObject,
      objectId,
      currentLevel: lodObject.currentLevel,
      currentDistance: lodObject.currentDistance,
      triangleCount,
      onCreated: false,
      onUpdated: true,
      onLevelChanged: levelChanged,
      onError: false
    };
  }

  private indexToLODLevel(index: number): LODLevel {
    const levels = [LODLevel.HIGH, LODLevel.MEDIUM, LODLevel.LOW, LODLevel.VERY_LOW];
    return levels[index] || LODLevel.VERY_LOW;
  }

  private getTriangleCount(mesh: Mesh): number {
    if (mesh.geometry && mesh.geometry.index) {
      return mesh.geometry.index.count / 3;
    } else if (mesh.geometry && mesh.geometry.attributes.position) {
      return mesh.geometry.attributes.position.count / 3;
    }
    return 0;
  }

  private generateObjectId(): string {
    return 'lod_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      lodObject: null,
      objectId: '',
      currentLevel: '',
      currentDistance: 0,
      triangleCount: 0,
      onCreated: false,
      onUpdated: false,
      onLevelChanged: false,
      onError: false
    };
  }
}

/**
 * 批处理节点
 */
export class BatchRenderingNode extends VisualScriptNode {
  public static readonly TYPE = 'BatchRendering';
  public static readonly NAME = '批处理渲染';
  public static readonly DESCRIPTION = '批处理渲染优化';

  private static optimizationManager: RenderingOptimizationManager = new RenderingOptimizationManager();

  constructor(nodeType: string = BatchRenderingNode.TYPE, name: string = BatchRenderingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('createGroup', 'trigger', '创建批处理组');
    this.addInput('addObject', 'trigger', '添加对象');
    this.addInput('removeObject', 'trigger', '移除对象');
    this.addInput('batch', 'trigger', '执行批处理');
    this.addInput('groupId', 'string', '批处理组ID');
    this.addInput('object', 'object', '3D对象');
    this.addInput('batchType', 'string', '批处理类型');
    this.addInput('maxInstances', 'number', '最大实例数');
    this.addInput('dynamicUpdate', 'boolean', '动态更新');

    // 输出端口
    this.addOutput('batchGroup', 'object', '批处理组');
    this.addOutput('groupId', 'string', '批处理组ID');
    this.addOutput('objectCount', 'number', '对象数量');
    this.addOutput('batched', 'boolean', '是否已批处理');
    this.addOutput('batchType', 'string', '批处理类型');
    this.addOutput('onGroupCreated', 'trigger', '批处理组创建完成');
    this.addOutput('onObjectAdded', 'trigger', '对象添加完成');
    this.addOutput('onObjectRemoved', 'trigger', '对象移除完成');
    this.addOutput('onBatched', 'trigger', '批处理完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createGroupTrigger = inputs?.createGroup;
      const addObjectTrigger = inputs?.addObject;
      const removeObjectTrigger = inputs?.removeObject;
      const batchTrigger = inputs?.batch;

      if (createGroupTrigger) {
        return this.createBatchGroup(inputs);
      } else if (addObjectTrigger) {
        return this.addObject(inputs);
      } else if (removeObjectTrigger) {
        return this.removeObject(inputs);
      } else if (batchTrigger) {
        return this.executeBatch(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('BatchRenderingNode', '批处理操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createBatchGroup(inputs: any): any {
    const groupId = inputs?.groupId as string || this.generateGroupId();
    const batchType = inputs?.batchType as string || 'static';
    const maxInstances = inputs?.maxInstances as number || 1000;
    const dynamicUpdate = inputs?.dynamicUpdate as boolean ?? false;

    const config: BatchConfig = {
      type: batchType as BatchType,
      maxInstances,
      dynamicUpdate,
      frustumCulling: true,
      sortObjects: true
    };

    const batchGroup = BatchRenderingNode.optimizationManager.createBatchGroup(groupId, config);

    Debug.log('BatchRenderingNode', `批处理组创建: ${groupId} (${batchType})`);

    return {
      batchGroup,
      groupId,
      objectCount: batchGroup.objects.length,
      batched: batchGroup.batched,
      batchType,
      onGroupCreated: true,
      onObjectAdded: false,
      onObjectRemoved: false,
      onBatched: false,
      onError: false
    };
  }

  private addObject(inputs: any): any {
    const groupId = inputs?.groupId as string;
    const object = inputs?.object as Object3D;

    if (!groupId || !object) {
      throw new Error('未提供批处理组ID或对象');
    }

    const batchGroup = BatchRenderingNode.optimizationManager.getBatchGroup(groupId);
    if (!batchGroup) {
      throw new Error('批处理组不存在');
    }

    batchGroup.addObject(object);

    Debug.log('BatchRenderingNode', `对象添加到批处理组: ${groupId}`);

    return {
      batchGroup,
      groupId,
      objectCount: batchGroup.objects.length,
      batched: batchGroup.batched,
      batchType: batchGroup.config.type,
      onGroupCreated: false,
      onObjectAdded: true,
      onObjectRemoved: false,
      onBatched: false,
      onError: false
    };
  }

  private removeObject(inputs: any): any {
    const groupId = inputs?.groupId as string;
    const object = inputs?.object as Object3D;

    if (!groupId || !object) {
      throw new Error('未提供批处理组ID或对象');
    }

    const batchGroup = BatchRenderingNode.optimizationManager.getBatchGroup(groupId);
    if (!batchGroup) {
      throw new Error('批处理组不存在');
    }

    const success = batchGroup.removeObject(object);
    if (!success) {
      throw new Error('对象移除失败');
    }

    Debug.log('BatchRenderingNode', `对象从批处理组移除: ${groupId}`);

    return {
      batchGroup,
      groupId,
      objectCount: batchGroup.objects.length,
      batched: batchGroup.batched,
      batchType: batchGroup.config.type,
      onGroupCreated: false,
      onObjectAdded: false,
      onObjectRemoved: true,
      onBatched: false,
      onError: false
    };
  }

  private executeBatch(inputs: any): any {
    const groupId = inputs?.groupId as string;

    if (!groupId) {
      throw new Error('未提供批处理组ID');
    }

    const batchGroup = BatchRenderingNode.optimizationManager.getBatchGroup(groupId);
    if (!batchGroup) {
      throw new Error('批处理组不存在');
    }

    const success = batchGroup.batch();
    if (!success) {
      throw new Error('批处理执行失败');
    }

    Debug.log('BatchRenderingNode', `批处理执行完成: ${groupId}`);

    return {
      batchGroup,
      groupId,
      objectCount: batchGroup.objects.length,
      batched: batchGroup.batched,
      batchType: batchGroup.config.type,
      onGroupCreated: false,
      onObjectAdded: false,
      onObjectRemoved: false,
      onBatched: true,
      onError: false
    };
  }

  private generateGroupId(): string {
    return 'batch_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      batchGroup: null,
      groupId: '',
      objectCount: 0,
      batched: false,
      batchType: '',
      onGroupCreated: false,
      onObjectAdded: false,
      onObjectRemoved: false,
      onBatched: false,
      onError: false
    };
  }
}

/**
 * 实例化渲染节点
 */
export class InstancedRenderingNode extends VisualScriptNode {
  public static readonly TYPE = 'InstancedRendering';
  public static readonly NAME = '实例化渲染';
  public static readonly DESCRIPTION = '实例化渲染优化';

  private static optimizationManager: RenderingOptimizationManager = new RenderingOptimizationManager();

  constructor(nodeType: string = InstancedRenderingNode.TYPE, name: string = InstancedRenderingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建实例化网格');
    this.addInput('update', 'trigger', '更新实例');
    this.addInput('meshId', 'string', '网格ID');
    this.addInput('geometry', 'object', '几何体');
    this.addInput('material', 'object', '材质');
    this.addInput('count', 'number', '实例数量');
    this.addInput('instances', 'array', '实例数据');

    // 输出端口
    this.addOutput('instancedMesh', 'object', '实例化网格');
    this.addOutput('meshId', 'string', '网格ID');
    this.addOutput('instanceCount', 'number', '实例数量');
    this.addOutput('maxInstances', 'number', '最大实例数');
    this.addOutput('onCreated', 'trigger', '实例化网格创建完成');
    this.addOutput('onUpdated', 'trigger', '实例更新完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;

      if (createTrigger) {
        return this.createInstancedMesh(inputs);
      } else if (updateTrigger) {
        return this.updateInstances(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('InstancedRenderingNode', '实例化渲染操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createInstancedMesh(inputs: any): any {
    const meshId = inputs?.meshId as string || this.generateMeshId();
    const geometry = inputs?.geometry;
    const material = inputs?.material;
    const count = inputs?.count as number || 100;

    if (!geometry || !material) {
      throw new Error('未提供几何体或材质');
    }

    const instancedMesh = InstancedRenderingNode.optimizationManager.createInstancedMesh(meshId, geometry, material, count);

    Debug.log('InstancedRenderingNode', `实例化网格创建: ${meshId} (${count}个实例)`);

    return {
      instancedMesh,
      meshId,
      instanceCount: 0,
      maxInstances: count,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateInstances(inputs: any): any {
    const meshId = inputs?.meshId as string;
    const instances = inputs?.instances as InstanceData[] || [];

    if (!meshId) {
      throw new Error('未提供网格ID');
    }

    const success = InstancedRenderingNode.optimizationManager.updateInstancedMesh(meshId, instances);
    if (!success) {
      throw new Error('实例更新失败');
    }

    const instancedMesh = InstancedRenderingNode.optimizationManager.getInstancedMesh(meshId);

    Debug.log('InstancedRenderingNode', `实例更新完成: ${meshId} (${instances.length}个实例)`);

    return {
      instancedMesh,
      meshId,
      instanceCount: instances.length,
      maxInstances: instancedMesh?.count || 0,
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private generateMeshId(): string {
    return 'instanced_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      instancedMesh: null,
      meshId: '',
      instanceCount: 0,
      maxInstances: 0,
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}

/**
 * 视锥体剔除节点
 */
export class FrustumCullingNode extends VisualScriptNode {
  public static readonly TYPE = 'FrustumCulling';
  public static readonly NAME = '视锥体剔除';
  public static readonly DESCRIPTION = '视锥体剔除优化';

  private static optimizationManager: RenderingOptimizationManager = new RenderingOptimizationManager();

  constructor(nodeType: string = FrustumCullingNode.TYPE, name: string = FrustumCullingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('cull', 'trigger', '执行剔除');
    this.addInput('camera', 'object', '相机对象');
    this.addInput('objects', 'array', '对象数组');
    this.addInput('cullingType', 'string', '剔除类型');
    this.addInput('maxDistance', 'number', '最大距离');

    // 输出端口
    this.addOutput('visibleObjects', 'array', '可见对象');
    this.addOutput('culledObjects', 'array', '被剔除对象');
    this.addOutput('visibleCount', 'number', '可见对象数量');
    this.addOutput('culledCount', 'number', '被剔除对象数量');
    this.addOutput('cullingRatio', 'number', '剔除比例');
    this.addOutput('onCulled', 'trigger', '剔除完成');
    this.addOutput('onError', 'trigger', '剔除失败');
  }

  public execute(inputs?: any): any {
    try {
      const cullTrigger = inputs?.cull;

      if (cullTrigger) {
        return this.performCulling(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('FrustumCullingNode', '视锥体剔除失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private performCulling(inputs: any): any {
    const camera = inputs?.camera as Camera;
    const objects = inputs?.objects as Object3D[] || [];
    const cullingType = inputs?.cullingType as string || 'frustum';
    const maxDistance = inputs?.maxDistance as number || 1000;

    if (!camera) {
      throw new Error('未提供相机对象');
    }

    if (objects.length === 0) {
      throw new Error('未提供对象数组');
    }

    let visibleObjects: Object3D[] = [];

    switch (cullingType) {
      case 'frustum':
        visibleObjects = FrustumCullingNode.optimizationManager.performFrustumCulling(camera, objects);
        break;
      case 'distance':
        visibleObjects = FrustumCullingNode.optimizationManager.performDistanceCulling(camera, objects, maxDistance);
        break;
      default:
        visibleObjects = objects;
        break;
    }

    const culledObjects = objects.filter(obj => !visibleObjects.includes(obj));
    const cullingRatio = objects.length > 0 ? culledObjects.length / objects.length : 0;

    Debug.log('FrustumCullingNode', `${cullingType}剔除完成: ${objects.length} -> ${visibleObjects.length} (剔除率: ${(cullingRatio * 100).toFixed(1)}%)`);

    return {
      visibleObjects,
      culledObjects,
      visibleCount: visibleObjects.length,
      culledCount: culledObjects.length,
      cullingRatio,
      onCulled: true,
      onError: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      visibleObjects: [],
      culledObjects: [],
      visibleCount: 0,
      culledCount: 0,
      cullingRatio: 0,
      onCulled: false,
      onError: false
    };
  }
}
