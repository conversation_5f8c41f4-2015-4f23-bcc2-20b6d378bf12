/**
 * 组件节点集合
 * 提供组件添加、移除、查询等功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Entity } from './EntityNodes';

/**
 * 基础组件接口
 */
export interface Component {
  type: string;
  enabled: boolean;
  entity?: Entity;
  [key: string]: any;
}

/**
 * 添加组件节点
 */
export class AddComponentNode extends VisualScriptNode {
  public static readonly TYPE = 'AddComponent';
  public static readonly NAME = '添加组件';
  public static readonly DESCRIPTION = '为实体添加组件';

  constructor(nodeType: string = AddComponentNode.TYPE, name: string = AddComponentNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('add', 'trigger', '添加');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('componentType', 'string', '组件类型');
    this.addInput('componentData', 'object', '组件数据');
    this.addInput('enabled', 'boolean', '启用状态');

    // 输出端口
    this.addOutput('component', 'object', '添加的组件');
    this.addOutput('entity', 'object', '实体');
    this.addOutput('success', 'boolean', '添加成功');
    this.addOutput('onAdded', 'trigger', '添加完成');
    this.addOutput('onError', 'trigger', '添加失败');
  }

  public execute(inputs?: any): any {
    try {
      const addTrigger = inputs?.add;
      if (!addTrigger) {
        return this.getDefaultOutputs();
      }

      const entity = inputs?.entity as Entity;
      const componentType = inputs?.componentType as string;
      const componentData = inputs?.componentData as any || {};
      const enabled = inputs?.enabled as boolean ?? true;

      if (!entity) {
        throw new Error('未提供目标实体');
      }

      if (!componentType) {
        throw new Error('未提供组件类型');
      }

      // 检查是否已存在该类型的组件
      if (entity.components.has(componentType)) {
        Debug.warn('AddComponentNode', `实体 ${entity.name} 已存在组件类型: ${componentType}`);
      }

      // 创建组件
      const component: Component = {
        type: componentType,
        enabled,
        entity,
        ...componentData
      };

      // 添加到实体
      entity.components.set(componentType, component);

      Debug.log('AddComponentNode', `组件添加成功: ${componentType} -> ${entity.name}`);

      return {
        component,
        entity,
        success: true,
        onAdded: true,
        onError: false
      };

    } catch (error) {
      Debug.error('AddComponentNode', '添加组件失败', error);
      return {
        component: null,
        entity: null,
        success: false,
        onAdded: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      component: null,
      entity: null,
      success: false,
      onAdded: false,
      onError: false
    };
  }
}

/**
 * 移除组件节点
 */
export class RemoveComponentNode extends VisualScriptNode {
  public static readonly TYPE = 'RemoveComponent';
  public static readonly NAME = '移除组件';
  public static readonly DESCRIPTION = '从实体移除组件';

  constructor(nodeType: string = RemoveComponentNode.TYPE, name: string = RemoveComponentNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('remove', 'trigger', '移除');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('componentType', 'string', '组件类型');

    // 输出端口
    this.addOutput('removedComponent', 'object', '被移除的组件');
    this.addOutput('entity', 'object', '实体');
    this.addOutput('success', 'boolean', '移除成功');
    this.addOutput('onRemoved', 'trigger', '移除完成');
    this.addOutput('onNotFound', 'trigger', '组件不存在');
  }

  public execute(inputs?: any): any {
    try {
      const removeTrigger = inputs?.remove;
      if (!removeTrigger) {
        return this.getDefaultOutputs();
      }

      const entity = inputs?.entity as Entity;
      const componentType = inputs?.componentType as string;

      if (!entity) {
        throw new Error('未提供目标实体');
      }

      if (!componentType) {
        throw new Error('未提供组件类型');
      }

      // 检查组件是否存在
      const component = entity.components.get(componentType);
      if (!component) {
        return {
          removedComponent: null,
          entity,
          success: false,
          onRemoved: false,
          onNotFound: true
        };
      }

      // 移除组件
      entity.components.delete(componentType);

      Debug.log('RemoveComponentNode', `组件移除成功: ${componentType} <- ${entity.name}`);

      return {
        removedComponent: component,
        entity,
        success: true,
        onRemoved: true,
        onNotFound: false
      };

    } catch (error) {
      Debug.error('RemoveComponentNode', '移除组件失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      removedComponent: null,
      entity: null,
      success: false,
      onRemoved: false,
      onNotFound: false
    };
  }
}

/**
 * 获取组件节点
 */
export class GetComponentNode extends VisualScriptNode {
  public static readonly TYPE = 'GetComponent';
  public static readonly NAME = '获取组件';
  public static readonly DESCRIPTION = '从实体获取指定类型的组件';

  constructor(nodeType: string = GetComponentNode.TYPE, name: string = GetComponentNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('entity', 'object', '目标实体');
    this.addInput('componentType', 'string', '组件类型');
    this.addInput('get', 'trigger', '获取');

    // 输出端口
    this.addOutput('component', 'object', '组件');
    this.addOutput('hasComponent', 'boolean', '是否存在');
    this.addOutput('componentEnabled', 'boolean', '组件启用状态');
    this.addOutput('onFound', 'trigger', '找到组件');
    this.addOutput('onNotFound', 'trigger', '未找到组件');
  }

  public execute(inputs?: any): any {
    try {
      const entity = inputs?.entity as Entity;
      const componentType = inputs?.componentType as string;
      const getTrigger = inputs?.get;

      if (!entity || !componentType) {
        return this.getDefaultOutputs();
      }

      const component = entity.components.get(componentType);
      const hasComponent = !!component;

      return {
        component: component || null,
        hasComponent,
        componentEnabled: component?.enabled || false,
        onFound: hasComponent && getTrigger,
        onNotFound: !hasComponent && getTrigger
      };

    } catch (error) {
      Debug.error('GetComponentNode', '获取组件失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      component: null,
      hasComponent: false,
      componentEnabled: false,
      onFound: false,
      onNotFound: false
    };
  }
}

/**
 * 组件启用状态节点
 */
export class ComponentEnabledNode extends VisualScriptNode {
  public static readonly TYPE = 'ComponentEnabled';
  public static readonly NAME = '组件启用状态';
  public static readonly DESCRIPTION = '控制组件的启用状态';

  constructor(nodeType: string = ComponentEnabledNode.TYPE, name: string = ComponentEnabledNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('component', 'object', '目标组件');
    this.addInput('setEnabled', 'trigger', '设置启用');
    this.addInput('enabled', 'boolean', '启用状态');

    // 输出端口
    this.addOutput('isEnabled', 'boolean', '当前启用状态');
    this.addOutput('component', 'object', '组件');
    this.addOutput('onEnabled', 'trigger', '启用时');
    this.addOutput('onDisabled', 'trigger', '禁用时');
    this.addOutput('onChanged', 'trigger', '状态改变');
  }

  public execute(inputs?: any): any {
    try {
      const component = inputs?.component as Component;
      const setEnabledTrigger = inputs?.setEnabled;
      const enabled = inputs?.enabled as boolean;

      if (!component) {
        return {
          isEnabled: false,
          component: null,
          onEnabled: false,
          onDisabled: false,
          onChanged: false
        };
      }

      let stateChanged = false;

      // 设置启用状态
      if (setEnabledTrigger && enabled !== undefined) {
        const oldEnabled = component.enabled;
        component.enabled = enabled;
        stateChanged = oldEnabled !== enabled;

        Debug.log('ComponentEnabledNode', `组件启用状态已更改: ${component.type} -> ${enabled}`);
      }

      return {
        isEnabled: component.enabled,
        component,
        onEnabled: stateChanged && component.enabled,
        onDisabled: stateChanged && !component.enabled,
        onChanged: stateChanged
      };

    } catch (error) {
      Debug.error('ComponentEnabledNode', '设置组件启用状态失败', error);
      return {
        isEnabled: false,
        component: null,
        onEnabled: false,
        onDisabled: false,
        onChanged: false
      };
    }
  }
}

/**
 * 获取所有组件节点
 */
export class GetAllComponentsNode extends VisualScriptNode {
  public static readonly TYPE = 'GetAllComponents';
  public static readonly NAME = '获取所有组件';
  public static readonly DESCRIPTION = '获取实体的所有组件';

  constructor(nodeType: string = GetAllComponentsNode.TYPE, name: string = GetAllComponentsNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('entity', 'object', '目标实体');
    this.addInput('get', 'trigger', '获取');
    this.addInput('enabledOnly', 'boolean', '仅启用的组件');

    // 输出端口
    this.addOutput('components', 'array', '组件列表');
    this.addOutput('componentTypes', 'array', '组件类型列表');
    this.addOutput('componentCount', 'number', '组件数量');
    this.addOutput('hasComponents', 'boolean', '是否有组件');
    this.addOutput('onGot', 'trigger', '获取完成');
  }

  public execute(inputs?: any): any {
    try {
      const entity = inputs?.entity as Entity;
      const getTrigger = inputs?.get;
      const enabledOnly = inputs?.enabledOnly as boolean || false;

      if (!entity) {
        return this.getDefaultOutputs();
      }

      // 获取组件列表
      const allComponents = Array.from(entity.components.values());
      const components = enabledOnly ? 
        allComponents.filter(comp => comp.enabled) : 
        allComponents;

      const componentTypes = components.map(comp => comp.type);
      const componentCount = components.length;
      const hasComponents = componentCount > 0;

      return {
        components,
        componentTypes,
        componentCount,
        hasComponents,
        onGot: getTrigger
      };

    } catch (error) {
      Debug.error('GetAllComponentsNode', '获取所有组件失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      components: [],
      componentTypes: [],
      componentCount: 0,
      hasComponents: false,
      onGot: false
    };
  }
}

/**
 * 组件属性节点
 */
export class ComponentPropertyNode extends VisualScriptNode {
  public static readonly TYPE = 'ComponentProperty';
  public static readonly NAME = '组件属性';
  public static readonly DESCRIPTION = '获取或设置组件属性';

  constructor(nodeType: string = ComponentPropertyNode.TYPE, name: string = ComponentPropertyNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('component', 'object', '目标组件');
    this.addInput('propertyName', 'string', '属性名称');
    this.addInput('getValue', 'trigger', '获取值');
    this.addInput('setValue', 'trigger', '设置值');
    this.addInput('value', 'any', '属性值');

    // 输出端口
    this.addOutput('currentValue', 'any', '当前值');
    this.addOutput('previousValue', 'any', '之前的值');
    this.addOutput('hasProperty', 'boolean', '是否存在属性');
    this.addOutput('component', 'object', '组件');
    this.addOutput('onValueChanged', 'trigger', '值已改变');
    this.addOutput('onGot', 'trigger', '获取完成');
    this.addOutput('onSet', 'trigger', '设置完成');
  }

  public execute(inputs?: any): any {
    try {
      const component = inputs?.component as Component;
      const propertyName = inputs?.propertyName as string;
      const getValueTrigger = inputs?.getValue;
      const setValueTrigger = inputs?.setValue;
      const value = inputs?.value;

      if (!component || !propertyName) {
        return this.getDefaultOutputs();
      }

      const hasProperty = propertyName in component;
      const currentValue = hasProperty ? component[propertyName] : undefined;
      let previousValue = currentValue;
      let valueChanged = false;

      // 设置属性值
      if (setValueTrigger && value !== undefined) {
        previousValue = component[propertyName];
        component[propertyName] = value;
        valueChanged = previousValue !== value;

        Debug.log('ComponentPropertyNode', `组件属性已设置: ${component.type}.${propertyName} = ${value}`);
      }

      return {
        currentValue: component[propertyName],
        previousValue,
        hasProperty,
        component,
        onValueChanged: valueChanged,
        onGot: getValueTrigger,
        onSet: setValueTrigger
      };

    } catch (error) {
      Debug.error('ComponentPropertyNode', '操作组件属性失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      currentValue: undefined,
      previousValue: undefined,
      hasProperty: false,
      component: null,
      onValueChanged: false,
      onGot: false,
      onSet: false
    };
  }
}
