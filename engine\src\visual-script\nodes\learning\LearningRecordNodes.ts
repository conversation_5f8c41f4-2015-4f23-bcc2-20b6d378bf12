/**
 * 学习记录系统节点集合
 * 提供学习进度跟踪、成就系统、知识图谱等功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 学习状态枚举
 */
export enum LearningStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  MASTERED = 'mastered',
  NEEDS_REVIEW = 'needs_review'
}

/**
 * 学习类型枚举
 */
export enum LearningType {
  COURSE = 'course',
  LESSON = 'lesson',
  EXERCISE = 'exercise',
  QUIZ = 'quiz',
  PROJECT = 'project',
  SKILL = 'skill',
  CONCEPT = 'concept'
}

/**
 * 难度等级枚举
 */
export enum DifficultyLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

/**
 * 学习记录接口
 */
export interface LearningRecord {
  id: string;
  userId: string;
  itemId: string;
  itemType: LearningType;
  title: string;
  description: string;
  status: LearningStatus;
  progress: number;
  score?: number;
  timeSpent: number;
  startTime: number;
  lastAccessTime: number;
  completionTime?: number;
  difficulty: DifficultyLevel;
  tags: string[];
  prerequisites: string[];
  metadata: any;
}

/**
 * 学习路径接口
 */
export interface LearningPath {
  id: string;
  name: string;
  description: string;
  items: LearningPathItem[];
  totalDuration: number;
  difficulty: DifficultyLevel;
  category: string;
  tags: string[];
}

/**
 * 学习路径项接口
 */
export interface LearningPathItem {
  id: string;
  itemId: string;
  itemType: LearningType;
  order: number;
  required: boolean;
  estimatedDuration: number;
  prerequisites: string[];
}

/**
 * 成就接口
 */
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  points: number;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  conditions: AchievementCondition[];
  unlockedAt?: number;
}

/**
 * 成就条件接口
 */
export interface AchievementCondition {
  type: 'complete_items' | 'score_threshold' | 'time_spent' | 'streak' | 'custom';
  target: number;
  current: number;
  metadata?: any;
}

/**
 * 知识点接口
 */
export interface KnowledgeNode {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: DifficultyLevel;
  prerequisites: string[];
  relatedNodes: string[];
  masteryLevel: number;
  lastReviewed: number;
  reviewCount: number;
}

/**
 * 学习统计接口
 */
export interface LearningStatistics {
  totalTimeSpent: number;
  itemsCompleted: number;
  averageScore: number;
  currentStreak: number;
  longestStreak: number;
  achievementsUnlocked: number;
  skillsAcquired: number;
  weakAreas: string[];
  strongAreas: string[];
  learningVelocity: number;
}

/**
 * 高级学习记录管理器
 */
class AdvancedLearningRecordManager {
  private learningRecords: Map<string, LearningRecord> = new Map();
  private learningPaths: Map<string, LearningPath> = new Map();
  private achievements: Map<string, Achievement> = new Map();
  private knowledgeGraph: Map<string, KnowledgeNode> = new Map();
  private userStatistics: Map<string, LearningStatistics> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 创建学习记录
   */
  createLearningRecord(userId: string, itemId: string, itemType: LearningType, title: string, description: string): LearningRecord {
    const record: LearningRecord = {
      id: this.generateRecordId(),
      userId,
      itemId,
      itemType,
      title,
      description,
      status: LearningStatus.NOT_STARTED,
      progress: 0,
      timeSpent: 0,
      startTime: Date.now(),
      lastAccessTime: Date.now(),
      difficulty: DifficultyLevel.BEGINNER,
      tags: [],
      prerequisites: [],
      metadata: {}
    };

    this.learningRecords.set(record.id, record);
    this.emit('recordCreated', { record });

    Debug.log('AdvancedLearningRecordManager', `学习记录创建: ${record.id} - ${title}`);
    return record;
  }

  /**
   * 更新学习进度
   */
  updateProgress(recordId: string, progress: number, timeSpent?: number, score?: number): LearningRecord {
    const record = this.learningRecords.get(recordId);
    if (!record) {
      throw new Error('学习记录不存在');
    }

    const oldProgress = record.progress;
    record.progress = Math.max(0, Math.min(100, progress));
    record.lastAccessTime = Date.now();

    if (timeSpent !== undefined) {
      record.timeSpent += timeSpent;
    }

    if (score !== undefined) {
      record.score = score;
    }

    // 更新状态
    if (record.progress === 0) {
      record.status = LearningStatus.NOT_STARTED;
    } else if (record.progress < 100) {
      record.status = LearningStatus.IN_PROGRESS;
    } else {
      record.status = LearningStatus.COMPLETED;
      record.completionTime = Date.now();
    }

    // 更新用户统计
    this.updateUserStatistics(record.userId);

    // 检查成就
    this.checkAchievements(record.userId);

    this.emit('progressUpdated', { record, oldProgress });

    Debug.log('AdvancedLearningRecordManager', `进度更新: ${recordId} ${oldProgress}% -> ${record.progress}%`);
    return record;
  }

  /**
   * 创建学习路径
   */
  createLearningPath(name: string, description: string, items: LearningPathItem[], difficulty: DifficultyLevel, category: string): LearningPath {
    const path: LearningPath = {
      id: this.generatePathId(),
      name,
      description,
      items: items.sort((a, b) => a.order - b.order),
      totalDuration: items.reduce((sum, item) => sum + item.estimatedDuration, 0),
      difficulty,
      category,
      tags: []
    };

    this.learningPaths.set(path.id, path);
    this.emit('pathCreated', { path });

    Debug.log('AdvancedLearningRecordManager', `学习路径创建: ${path.id} - ${name}`);
    return path;
  }

  /**
   * 获取用户学习路径进度
   */
  getPathProgress(userId: string, pathId: string): { progress: number; completedItems: number; totalItems: number } {
    const path = this.learningPaths.get(pathId);
    if (!path) {
      throw new Error('学习路径不存在');
    }

    const userRecords = Array.from(this.learningRecords.values())
      .filter(record => record.userId === userId);

    let completedItems = 0;
    let totalProgress = 0;

    for (const item of path.items) {
      const record = userRecords.find(r => r.itemId === item.itemId);
      if (record) {
        totalProgress += record.progress;
        if (record.status === LearningStatus.COMPLETED || record.status === LearningStatus.MASTERED) {
          completedItems++;
        }
      }
    }

    const progress = path.items.length > 0 ? totalProgress / path.items.length : 0;

    return {
      progress,
      completedItems,
      totalItems: path.items.length
    };
  }

  /**
   * 创建成就
   */
  createAchievement(name: string, description: string, category: string, points: number, conditions: AchievementCondition[]): Achievement {
    const achievement: Achievement = {
      id: this.generateAchievementId(),
      name,
      description,
      icon: 'default_icon',
      category,
      points,
      rarity: this.calculateRarity(points),
      conditions
    };

    this.achievements.set(achievement.id, achievement);
    this.emit('achievementCreated', { achievement });

    Debug.log('AdvancedLearningRecordManager', `成就创建: ${achievement.id} - ${name}`);
    return achievement;
  }

  /**
   * 检查用户成就
   */
  checkAchievements(userId: string): Achievement[] {
    const unlockedAchievements: Achievement[] = [];
    const userStats = this.getUserStatistics(userId);

    for (const achievement of this.achievements.values()) {
      if (achievement.unlockedAt) continue; // 已解锁

      let allConditionsMet = true;

      for (const condition of achievement.conditions) {
        if (!this.checkAchievementCondition(userId, condition, userStats)) {
          allConditionsMet = false;
          break;
        }
      }

      if (allConditionsMet) {
        achievement.unlockedAt = Date.now();
        unlockedAchievements.push(achievement);
        this.emit('achievementUnlocked', { userId, achievement });
      }
    }

    if (unlockedAchievements.length > 0) {
      Debug.log('AdvancedLearningRecordManager', `成就解锁: ${userId} 解锁了 ${unlockedAchievements.length} 个成就`);
    }

    return unlockedAchievements;
  }

  /**
   * 检查成就条件
   */
  private checkAchievementCondition(userId: string, condition: AchievementCondition, stats: LearningStatistics): boolean {
    switch (condition.type) {
      case 'complete_items':
        return stats.itemsCompleted >= condition.target;
      case 'score_threshold':
        return stats.averageScore >= condition.target;
      case 'time_spent':
        return stats.totalTimeSpent >= condition.target;
      case 'streak':
        return stats.currentStreak >= condition.target;
      default:
        return false;
    }
  }

  /**
   * 添加知识点
   */
  addKnowledgeNode(name: string, description: string, category: string, difficulty: DifficultyLevel, prerequisites: string[] = []): KnowledgeNode {
    const node: KnowledgeNode = {
      id: this.generateNodeId(),
      name,
      description,
      category,
      difficulty,
      prerequisites,
      relatedNodes: [],
      masteryLevel: 0,
      lastReviewed: 0,
      reviewCount: 0
    };

    this.knowledgeGraph.set(node.id, node);
    this.emit('knowledgeNodeAdded', { node });

    Debug.log('AdvancedLearningRecordManager', `知识点添加: ${node.id} - ${name}`);
    return node;
  }

  /**
   * 更新知识点掌握度
   */
  updateKnowledgeMastery(nodeId: string, masteryLevel: number): KnowledgeNode {
    const node = this.knowledgeGraph.get(nodeId);
    if (!node) {
      throw new Error('知识点不存在');
    }

    node.masteryLevel = Math.max(0, Math.min(100, masteryLevel));
    node.lastReviewed = Date.now();
    node.reviewCount++;

    this.emit('masteryUpdated', { node });

    Debug.log('AdvancedLearningRecordManager', `知识点掌握度更新: ${nodeId} -> ${masteryLevel}%`);
    return node;
  }

  /**
   * 获取用户统计信息
   */
  getUserStatistics(userId: string): LearningStatistics {
    let stats = this.userStatistics.get(userId);
    if (!stats) {
      stats = this.calculateUserStatistics(userId);
      this.userStatistics.set(userId, stats);
    }
    return stats;
  }

  /**
   * 计算用户统计信息
   */
  private calculateUserStatistics(userId: string): LearningStatistics {
    const userRecords = Array.from(this.learningRecords.values())
      .filter(record => record.userId === userId);

    const completedRecords = userRecords.filter(record => 
      record.status === LearningStatus.COMPLETED || record.status === LearningStatus.MASTERED
    );

    const totalTimeSpent = userRecords.reduce((sum, record) => sum + record.timeSpent, 0);
    const averageScore = this.calculateAverageScore(userRecords);
    const currentStreak = this.calculateCurrentStreak(userId);
    const longestStreak = this.calculateLongestStreak(userId);

    return {
      totalTimeSpent,
      itemsCompleted: completedRecords.length,
      averageScore,
      currentStreak,
      longestStreak,
      achievementsUnlocked: Array.from(this.achievements.values()).filter(a => a.unlockedAt).length,
      skillsAcquired: this.calculateSkillsAcquired(userId),
      weakAreas: this.identifyWeakAreas(userRecords),
      strongAreas: this.identifyStrongAreas(userRecords),
      learningVelocity: this.calculateLearningVelocity(userRecords)
    };
  }

  /**
   * 更新用户统计信息
   */
  private updateUserStatistics(userId: string): void {
    const stats = this.calculateUserStatistics(userId);
    this.userStatistics.set(userId, stats);
    this.emit('statisticsUpdated', { userId, stats });
  }

  // 辅助方法
  private calculateRarity(points: number): 'common' | 'rare' | 'epic' | 'legendary' {
    if (points >= 1000) return 'legendary';
    if (points >= 500) return 'epic';
    if (points >= 100) return 'rare';
    return 'common';
  }

  private calculateAverageScore(records: LearningRecord[]): number {
    const recordsWithScore = records.filter(r => r.score !== undefined);
    if (recordsWithScore.length === 0) return 0;
    
    const totalScore = recordsWithScore.reduce((sum, record) => sum + (record.score || 0), 0);
    return totalScore / recordsWithScore.length;
  }

  private calculateCurrentStreak(userId: string): number {
    // 简化实现：返回随机值
    return Math.floor(Math.random() * 30);
  }

  private calculateLongestStreak(userId: string): number {
    // 简化实现：返回随机值
    return Math.floor(Math.random() * 100);
  }

  private calculateSkillsAcquired(userId: string): number {
    const userRecords = Array.from(this.learningRecords.values())
      .filter(record => record.userId === userId && record.itemType === LearningType.SKILL);
    
    return userRecords.filter(record => 
      record.status === LearningStatus.COMPLETED || record.status === LearningStatus.MASTERED
    ).length;
  }

  private identifyWeakAreas(records: LearningRecord[]): string[] {
    // 简化实现：基于低分数识别薄弱领域
    const weakRecords = records.filter(record => 
      record.score !== undefined && record.score < 70
    );
    
    return [...new Set(weakRecords.map(record => record.tags).flat())].slice(0, 5);
  }

  private identifyStrongAreas(records: LearningRecord[]): string[] {
    // 简化实现：基于高分数识别强项领域
    const strongRecords = records.filter(record => 
      record.score !== undefined && record.score >= 90
    );
    
    return [...new Set(strongRecords.map(record => record.tags).flat())].slice(0, 5);
  }

  private calculateLearningVelocity(records: LearningRecord[]): number {
    // 简化实现：计算每小时完成的项目数
    const totalTime = records.reduce((sum, record) => sum + record.timeSpent, 0);
    const completedItems = records.filter(record => 
      record.status === LearningStatus.COMPLETED || record.status === LearningStatus.MASTERED
    ).length;
    
    return totalTime > 0 ? (completedItems / (totalTime / 3600)) : 0;
  }

  // ID生成方法
  private generateRecordId(): string {
    return 'record_' + Math.random().toString(36).substr(2, 9);
  }

  private generatePathId(): string {
    return 'path_' + Math.random().toString(36).substr(2, 9);
  }

  private generateAchievementId(): string {
    return 'achievement_' + Math.random().toString(36).substr(2, 9);
  }

  private generateNodeId(): string {
    return 'node_' + Math.random().toString(36).substr(2, 9);
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('AdvancedLearningRecordManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 获取学习记录
   */
  getLearningRecord(recordId: string): LearningRecord | undefined {
    return this.learningRecords.get(recordId);
  }

  /**
   * 获取用户所有学习记录
   */
  getUserLearningRecords(userId: string): LearningRecord[] {
    return Array.from(this.learningRecords.values())
      .filter(record => record.userId === userId);
  }

  /**
   * 获取学习路径
   */
  getLearningPath(pathId: string): LearningPath | undefined {
    return this.learningPaths.get(pathId);
  }

  /**
   * 获取成就
   */
  getAchievement(achievementId: string): Achievement | undefined {
    return this.achievements.get(achievementId);
  }

  /**
   * 获取知识点
   */
  getKnowledgeNode(nodeId: string): KnowledgeNode | undefined {
    return this.knowledgeGraph.get(nodeId);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.learningRecords.clear();
    this.learningPaths.clear();
    this.achievements.clear();
    this.knowledgeGraph.clear();
    this.userStatistics.clear();
    this.eventListeners.clear();
  }
}

/**
 * 学习记录节点
 */
export class LearningRecordNode extends VisualScriptNode {
  public static readonly TYPE = 'LearningRecord';
  public static readonly NAME = '学习记录';
  public static readonly DESCRIPTION = '创建和管理学习记录';

  private static learningManager: AdvancedLearningRecordManager = new AdvancedLearningRecordManager();

  constructor(nodeType: string = LearningRecordNode.TYPE, name: string = LearningRecordNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建记录');
    this.addInput('updateProgress', 'trigger', '更新进度');
    this.addInput('userId', 'string', '用户ID');
    this.addInput('itemId', 'string', '学习项ID');
    this.addInput('itemType', 'string', '学习项类型');
    this.addInput('title', 'string', '标题');
    this.addInput('description', 'string', '描述');
    this.addInput('recordId', 'string', '记录ID');
    this.addInput('progress', 'number', '进度');
    this.addInput('timeSpent', 'number', '学习时间');
    this.addInput('score', 'number', '分数');

    // 输出端口
    this.addOutput('record', 'object', '学习记录');
    this.addOutput('recordId', 'string', '记录ID');
    this.addOutput('progress', 'number', '当前进度');
    this.addOutput('status', 'string', '学习状态');
    this.addOutput('totalTime', 'number', '总学习时间');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onUpdated', 'trigger', '更新完成');
    this.addOutput('onCompleted', 'trigger', '学习完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.updateProgress;

      if (createTrigger) {
        return this.createRecord(inputs);
      } else if (updateTrigger) {
        return this.updateProgress(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('LearningRecordNode', '学习记录操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createRecord(inputs: any): any {
    const userId = inputs?.userId as string;
    const itemId = inputs?.itemId as string;
    const itemType = inputs?.itemType as string || 'lesson';
    const title = inputs?.title as string || 'Untitled';
    const description = inputs?.description as string || '';

    if (!userId || !itemId) {
      throw new Error('未提供用户ID或学习项ID');
    }

    const record = LearningRecordNode.learningManager.createLearningRecord(
      userId,
      itemId,
      itemType as LearningType,
      title,
      description
    );

    return {
      record,
      recordId: record.id,
      progress: record.progress,
      status: record.status,
      totalTime: record.timeSpent,
      onCreated: true,
      onUpdated: false,
      onCompleted: false,
      onError: false
    };
  }

  private updateProgress(inputs: any): any {
    const recordId = inputs?.recordId as string;
    const progress = inputs?.progress as number;
    const timeSpent = inputs?.timeSpent as number;
    const score = inputs?.score as number;

    if (!recordId) {
      throw new Error('未提供记录ID');
    }

    const record = LearningRecordNode.learningManager.updateProgress(
      recordId,
      progress || 0,
      timeSpent,
      score
    );

    const isCompleted = record.status === LearningStatus.COMPLETED || record.status === LearningStatus.MASTERED;

    return {
      record,
      recordId: record.id,
      progress: record.progress,
      status: record.status,
      totalTime: record.timeSpent,
      onCreated: false,
      onUpdated: true,
      onCompleted: isCompleted,
      onError: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      record: null,
      recordId: '',
      progress: 0,
      status: LearningStatus.NOT_STARTED,
      totalTime: 0,
      onCreated: false,
      onUpdated: false,
      onCompleted: false,
      onError: false
    };
  }
}

/**
 * 学习统计节点
 */
export class LearningStatisticsNode extends VisualScriptNode {
  public static readonly TYPE = 'LearningStatistics';
  public static readonly NAME = '学习统计';
  public static readonly DESCRIPTION = '获取用户学习统计信息';

  private static learningManager: AdvancedLearningRecordManager = new AdvancedLearningRecordManager();

  constructor(nodeType: string = LearningStatisticsNode.TYPE, name: string = LearningStatisticsNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('getStats', 'trigger', '获取统计');
    this.addInput('userId', 'string', '用户ID');

    // 输出端口
    this.addOutput('statistics', 'object', '统计信息');
    this.addOutput('totalTime', 'number', '总学习时间');
    this.addOutput('itemsCompleted', 'number', '完成项目数');
    this.addOutput('averageScore', 'number', '平均分数');
    this.addOutput('currentStreak', 'number', '当前连续天数');
    this.addOutput('longestStreak', 'number', '最长连续天数');
    this.addOutput('achievementsCount', 'number', '成就数量');
    this.addOutput('skillsAcquired', 'number', '获得技能数');
    this.addOutput('weakAreas', 'array', '薄弱领域');
    this.addOutput('strongAreas', 'array', '强项领域');
    this.addOutput('learningVelocity', 'number', '学习速度');
    this.addOutput('onStatsRetrieved', 'trigger', '统计获取完成');
  }

  public execute(inputs?: any): any {
    try {
      const getStatsTrigger = inputs?.getStats;
      const userId = inputs?.userId as string;

      if (!getStatsTrigger || !userId) {
        return this.getDefaultOutputs();
      }

      const statistics = LearningStatisticsNode.learningManager.getUserStatistics(userId);

      Debug.log('LearningStatisticsNode', `用户统计获取: ${userId}`);

      return {
        statistics,
        totalTime: statistics.totalTimeSpent,
        itemsCompleted: statistics.itemsCompleted,
        averageScore: statistics.averageScore,
        currentStreak: statistics.currentStreak,
        longestStreak: statistics.longestStreak,
        achievementsCount: statistics.achievementsUnlocked,
        skillsAcquired: statistics.skillsAcquired,
        weakAreas: statistics.weakAreas,
        strongAreas: statistics.strongAreas,
        learningVelocity: statistics.learningVelocity,
        onStatsRetrieved: true
      };

    } catch (error) {
      Debug.error('LearningStatisticsNode', '获取学习统计失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      statistics: null,
      totalTime: 0,
      itemsCompleted: 0,
      averageScore: 0,
      currentStreak: 0,
      longestStreak: 0,
      achievementsCount: 0,
      skillsAcquired: 0,
      weakAreas: [],
      strongAreas: [],
      learningVelocity: 0,
      onStatsRetrieved: false
    };
  }
}

/**
 * 成就系统节点
 */
export class AchievementSystemNode extends VisualScriptNode {
  public static readonly TYPE = 'AchievementSystem';
  public static readonly NAME = '成就系统';
  public static readonly DESCRIPTION = '管理学习成就和奖励';

  private static learningManager: AdvancedLearningRecordManager = new AdvancedLearningRecordManager();

  constructor(nodeType: string = AchievementSystemNode.TYPE, name: string = AchievementSystemNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('createAchievement', 'trigger', '创建成就');
    this.addInput('checkAchievements', 'trigger', '检查成就');
    this.addInput('name', 'string', '成就名称');
    this.addInput('description', 'string', '成就描述');
    this.addInput('category', 'string', '成就类别');
    this.addInput('points', 'number', '奖励点数');
    this.addInput('conditions', 'array', '解锁条件');
    this.addInput('userId', 'string', '用户ID');

    // 输出端口
    this.addOutput('achievement', 'object', '成就信息');
    this.addOutput('achievementId', 'string', '成就ID');
    this.addOutput('unlockedAchievements', 'array', '解锁的成就');
    this.addOutput('totalPoints', 'number', '总奖励点数');
    this.addOutput('rarity', 'string', '稀有度');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onUnlocked', 'trigger', '成就解锁');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.createAchievement;
      const checkTrigger = inputs?.checkAchievements;

      if (createTrigger) {
        return this.createAchievement(inputs);
      } else if (checkTrigger) {
        return this.checkAchievements(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('AchievementSystemNode', '成就系统操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createAchievement(inputs: any): any {
    const name = inputs?.name as string || 'New Achievement';
    const description = inputs?.description as string || '';
    const category = inputs?.category as string || 'general';
    const points = inputs?.points as number || 10;
    const conditions = inputs?.conditions as AchievementCondition[] || [];

    const achievement = AchievementSystemNode.learningManager.createAchievement(
      name,
      description,
      category,
      points,
      conditions
    );

    return {
      achievement,
      achievementId: achievement.id,
      unlockedAchievements: [],
      totalPoints: points,
      rarity: achievement.rarity,
      onCreated: true,
      onUnlocked: false,
      onError: false
    };
  }

  private checkAchievements(inputs: any): any {
    const userId = inputs?.userId as string;

    if (!userId) {
      throw new Error('未提供用户ID');
    }

    const unlockedAchievements = AchievementSystemNode.learningManager.checkAchievements(userId);
    const totalPoints = unlockedAchievements.reduce((sum, achievement) => sum + achievement.points, 0);

    return {
      achievement: null,
      achievementId: '',
      unlockedAchievements,
      totalPoints,
      rarity: '',
      onCreated: false,
      onUnlocked: unlockedAchievements.length > 0,
      onError: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      achievement: null,
      achievementId: '',
      unlockedAchievements: [],
      totalPoints: 0,
      rarity: '',
      onCreated: false,
      onUnlocked: false,
      onError: false
    };
  }
}
