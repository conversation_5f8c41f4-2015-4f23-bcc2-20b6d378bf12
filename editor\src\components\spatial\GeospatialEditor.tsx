/**
 * 地理数据编辑器组件
 * 提供地理空间数据的创建、编辑和管理功能
 */
import React, { useState, useCallback } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  List, 
  Modal, 
  Form, 
  Input, 
  Select, 
  InputNumber, 
  message,
  Popconfirm,
  Upload,
  Divider,
  Tag,
  Tooltip
} from 'antd';
import { 
  SelectOutlined,
  EnvironmentOutlined,
  LineOutlined,
  BorderOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  DownloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { MapView, GeographicCoordinate } from './MapView';
import './GeospatialEditor.scss';

const { Option } = Select;
const { TextArea } = Input;

/**
 * 地理空间数据接口
 */
export interface GeospatialData {
  id: string;
  name: string;
  type: 'Point' | 'LineString' | 'Polygon';
  coordinates: GeographicCoordinate[];
  properties: Record<string, any>;
  style?: {
    color?: string;
    fillColor?: string;
    weight?: number;
    opacity?: number;
    fillOpacity?: number;
    radius?: number;
  };
  visible?: boolean;
  editable?: boolean;
}

/**
 * 编辑工具类型
 */
type EditTool = 'select' | 'point' | 'line' | 'polygon';

/**
 * 地理数据编辑器属性接口
 */
export interface GeospatialEditorProps {
  data?: GeospatialData[];
  onDataChange?: (data: GeospatialData[]) => void;
  onFeatureSelect?: (feature: GeospatialData | null) => void;
  className?: string;
}

/**
 * 地理数据编辑器组件
 */
export const GeospatialEditor: React.FC<GeospatialEditorProps> = ({
  data = [],
  onDataChange,
  onFeatureSelect,
  className
}) => {
  const { } = useTranslation();
  const [selectedTool, setSelectedTool] = useState<EditTool>('select');
  const [spatialData, setSpatialData] = useState<GeospatialData[]>(data);
  const [selectedFeature, setSelectedFeature] = useState<GeospatialData | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingFeature, setEditingFeature] = useState<GeospatialData | null>(null);
  const [form] = Form.useForm();

  /**
   * 处理工具变化
   */
  const handleToolChange = useCallback((tool: EditTool) => {
    setSelectedTool(tool);
    if (tool !== 'select') {
      setSelectedFeature(null);
      onFeatureSelect?.(null);
    }
  }, [onFeatureSelect]);

  /**
   * 创建新要素
   */
  const createFeature = useCallback((type: 'Point' | 'LineString' | 'Polygon') => {
    const newFeature: GeospatialData = {
      id: `feature_${Date.now()}`,
      name: `新${type === 'Point' ? '点' : type === 'LineString' ? '线' : '面'}`,
      type,
      coordinates: [{ longitude: 116.404, latitude: 39.915 }], // 默认北京坐标
      properties: {},
      style: {
        color: type === 'Point' ? '#ff4d4f' : '#1890ff',
        fillColor: type === 'Polygon' ? '#1890ff' : undefined,
        weight: 2,
        opacity: 1,
        fillOpacity: type === 'Polygon' ? 0.3 : undefined,
        radius: type === 'Point' ? 8 : undefined
      },
      visible: true,
      editable: true
    };

    const newData = [...spatialData, newFeature];
    setSpatialData(newData);
    onDataChange?.(newData);
    
    message.success(`创建${newFeature.name}成功`);
  }, [spatialData, onDataChange]);

  /**
   * 处理要素选择
   */
  const handleFeatureSelect = useCallback((feature: GeospatialData) => {
    setSelectedFeature(feature);
    onFeatureSelect?.(feature);
  }, [onFeatureSelect]);

  /**
   * 处理要素更新
   */
  const handleFeatureUpdate = useCallback((id: string, updates: Partial<GeospatialData>) => {
    const newData = spatialData.map(item => 
      item.id === id ? { ...item, ...updates } : item
    );
    setSpatialData(newData);
    onDataChange?.(newData);
    
    if (selectedFeature?.id === id) {
      setSelectedFeature({ ...selectedFeature, ...updates });
      onFeatureSelect?.({ ...selectedFeature, ...updates });
    }
  }, [spatialData, selectedFeature, onDataChange, onFeatureSelect]);

  /**
   * 处理要素删除
   */
  const handleFeatureDelete = useCallback((id: string) => {
    const newData = spatialData.filter(item => item.id !== id);
    setSpatialData(newData);
    onDataChange?.(newData);
    
    if (selectedFeature?.id === id) {
      setSelectedFeature(null);
      onFeatureSelect?.(null);
    }
    
    message.success('删除成功');
  }, [spatialData, selectedFeature, onDataChange, onFeatureSelect]);

  /**
   * 切换要素可见性
   */
  const toggleFeatureVisibility = useCallback((id: string) => {
    const feature = spatialData.find(item => item.id === id);
    if (feature) {
      handleFeatureUpdate(id, { visible: !feature.visible });
    }
  }, [spatialData, handleFeatureUpdate]);

  /**
   * 打开编辑模态框
   */
  const openEditModal = useCallback((feature: GeospatialData) => {
    setEditingFeature(feature);
    form.setFieldsValue({
      name: feature.name,
      type: feature.type,
      properties: JSON.stringify(feature.properties, null, 2),
      style: feature.style
    });
    setIsModalVisible(true);
  }, [form]);

  /**
   * 处理编辑提交
   */
  const handleEditSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      
      if (editingFeature) {
        const updates: Partial<GeospatialData> = {
          name: values.name,
          type: values.type,
          properties: JSON.parse(values.properties || '{}'),
          style: values.style
        };
        
        handleFeatureUpdate(editingFeature.id, updates);
        setIsModalVisible(false);
        setEditingFeature(null);
        message.success('更新成功');
      }
    } catch (error) {
      console.error('编辑失败:', error);
      message.error('编辑失败，请检查输入');
    }
  }, [form, editingFeature, handleFeatureUpdate]);

  /**
   * 导出数据
   */
  const exportData = useCallback(() => {
    const geoJSON = {
      type: 'FeatureCollection',
      features: spatialData.map(feature => ({
        type: 'Feature',
        geometry: {
          type: feature.type,
          coordinates: feature.type === 'Point' 
            ? [feature.coordinates[0].longitude, feature.coordinates[0].latitude]
            : feature.coordinates.map(coord => [coord.longitude, coord.latitude])
        },
        properties: {
          ...feature.properties,
          name: feature.name,
          style: feature.style
        }
      }))
    };

    const blob = new Blob([JSON.stringify(geoJSON, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'spatial_data.geojson';
    a.click();
    URL.revokeObjectURL(url);
    
    message.success('导出成功');
  }, [spatialData]);

  /**
   * 导入数据
   */
  const importData = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const geoJSON = JSON.parse(e.target?.result as string);
        
        if (geoJSON.type === 'FeatureCollection') {
          const importedData: GeospatialData[] = geoJSON.features.map((feature: any, index: number) => ({
            id: `imported_${Date.now()}_${index}`,
            name: feature.properties?.name || `导入要素${index + 1}`,
            type: feature.geometry.type,
            coordinates: feature.geometry.type === 'Point'
              ? [{ longitude: feature.geometry.coordinates[0], latitude: feature.geometry.coordinates[1] }]
              : feature.geometry.coordinates.map((coord: number[]) => ({ longitude: coord[0], latitude: coord[1] })),
            properties: feature.properties || {},
            style: feature.properties?.style || {},
            visible: true,
            editable: true
          }));
          
          const newData = [...spatialData, ...importedData];
          setSpatialData(newData);
          onDataChange?.(newData);
          
          message.success(`导入${importedData.length}个要素成功`);
        } else {
          message.error('不支持的文件格式');
        }
      } catch (error) {
        console.error('导入失败:', error);
        message.error('文件解析失败');
      }
    };
    reader.readAsText(file);
    return false; // 阻止默认上传行为
  }, [spatialData, onDataChange]);

  return (
    <div className={`geospatial-editor ${className || ''}`}>
      <Card 
        title="地理数据编辑器"
        extra={
          <Space>
            <Upload
              beforeUpload={importData}
              showUploadList={false}
              accept=".geojson,.json"
            >
              <Button icon={<UploadOutlined />} size="small">
                导入
              </Button>
            </Upload>
            
            <Button 
              icon={<DownloadOutlined />} 
              onClick={exportData}
              size="small"
              disabled={spatialData.length === 0}
            >
              导出
            </Button>
          </Space>
        }
      >
        {/* 工具栏 */}
        <div className="toolbar">
          <Space wrap>
            <Space.Compact>
              <Button 
                type={selectedTool === 'select' ? 'primary' : 'default'}
                onClick={() => handleToolChange('select')}
                icon={<SelectOutlined />}
              >
                选择
              </Button>
              <Button 
                type={selectedTool === 'point' ? 'primary' : 'default'}
                onClick={() => handleToolChange('point')}
                icon={<EnvironmentOutlined />}
              >
                点
              </Button>
              <Button 
                type={selectedTool === 'line' ? 'primary' : 'default'}
                onClick={() => handleToolChange('line')}
                icon={<LineOutlined />}
              >
                线
              </Button>
              <Button 
                type={selectedTool === 'polygon' ? 'primary' : 'default'}
                onClick={() => handleToolChange('polygon')}
                icon={<BorderOutlined />}
              >
                面
              </Button>
            </Space.Compact>
            
            <Divider type="vertical" />
            
            <Button 
              type="dashed" 
              icon={<PlusOutlined />}
              onClick={() => createFeature('Point')}
            >
              添加点
            </Button>
            <Button 
              type="dashed" 
              icon={<PlusOutlined />}
              onClick={() => createFeature('LineString')}
            >
              添加线
            </Button>
            <Button 
              type="dashed" 
              icon={<PlusOutlined />}
              onClick={() => createFeature('Polygon')}
            >
              添加面
            </Button>
          </Space>
        </div>

        <div className="editor-content">
          {/* 地图面板 */}
          <div className="map-panel">
            <MapView 
              height={400}
              showControls={true}
              showCoordinates={true}
            />
          </div>

          {/* 要素列表面板 */}
          <div className="features-panel">
            <Card 
              title={`要素列表 (${spatialData.length})`}
              size="small"
              style={{ height: '100%' }}
            >
              <List
                size="small"
                dataSource={spatialData}
                renderItem={(feature) => (
                  <List.Item
                    className={selectedFeature?.id === feature.id ? 'selected' : ''}
                    actions={[
                      <Tooltip title={feature.visible ? '隐藏' : '显示'}>
                        <Button
                          type="text"
                          size="small"
                          icon={feature.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                          onClick={() => toggleFeatureVisibility(feature.id)}
                        />
                      </Tooltip>,
                      <Tooltip title="编辑">
                        <Button
                          type="text"
                          size="small"
                          icon={<EditOutlined />}
                          onClick={() => openEditModal(feature)}
                        />
                      </Tooltip>,
                      <Popconfirm
                        title="确定删除这个要素吗？"
                        onConfirm={() => handleFeatureDelete(feature.id)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button
                          type="text"
                          size="small"
                          danger
                          icon={<DeleteOutlined />}
                        />
                      </Popconfirm>
                    ]}
                    onClick={() => handleFeatureSelect(feature)}
                  >
                    <List.Item.Meta
                      avatar={
                        <div className={`feature-icon ${feature.type.toLowerCase()}`}>
                          {feature.type === 'Point' && <EnvironmentOutlined />}
                          {feature.type === 'LineString' && <LineOutlined />}
                          {feature.type === 'Polygon' && <BorderOutlined />}
                        </div>
                      }
                      title={
                        <Space>
                          {feature.name}
                          <Tag color={feature.type === 'Point' ? 'red' : feature.type === 'LineString' ? 'blue' : 'green'}>
                            {feature.type}
                          </Tag>
                          {!feature.visible && <Tag color="default">隐藏</Tag>}
                        </Space>
                      }
                      description={
                        <div>
                          <div>坐标数: {feature.coordinates.length}</div>
                          {Object.keys(feature.properties).length > 0 && (
                            <div>属性: {Object.keys(feature.properties).length} 个</div>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </div>
        </div>
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑要素"
        open={isModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingFeature(null);
        }}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入要素名称' }]}
          >
            <Input placeholder="请输入要素名称" />
          </Form.Item>

          <Form.Item
            name="type"
            label="类型"
            rules={[{ required: true, message: '请选择要素类型' }]}
          >
            <Select placeholder="请选择要素类型">
              <Option value="Point">点</Option>
              <Option value="LineString">线</Option>
              <Option value="Polygon">面</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="properties"
            label="属性 (JSON格式)"
          >
            <TextArea 
              rows={4} 
              placeholder='{"key": "value"}'
            />
          </Form.Item>

          <Form.Item label="样式设置">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Form.Item name={['style', 'color']} label="颜色" style={{ marginBottom: 8 }}>
                <Input type="color" style={{ width: 100 }} />
              </Form.Item>
              
              <Form.Item name={['style', 'weight']} label="线宽" style={{ marginBottom: 8 }}>
                <InputNumber min={1} max={10} style={{ width: 100 }} />
              </Form.Item>
              
              <Form.Item name={['style', 'opacity']} label="透明度" style={{ marginBottom: 8 }}>
                <InputNumber min={0} max={1} step={0.1} style={{ width: 100 }} />
              </Form.Item>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
