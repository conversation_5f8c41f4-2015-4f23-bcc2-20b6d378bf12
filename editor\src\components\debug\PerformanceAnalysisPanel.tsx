/**
 * 性能分析面板组件
 * 提供实时性能监控和分析功能
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Alert,
  Switch,
  Button,
  Space,
  Tag,
  Typography,
  Tabs,
  List,
  Badge,
  Modal,
  Descriptions
} from 'antd';
import {
  DashboardOutlined,
  WarningOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ClearOutlined,
  LineChartOutlined,
  DatabaseOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { Line } from '@ant-design/charts';
import { useTranslation } from 'react-i18next';
import PerformanceMonitorService, {
  PerformanceMetrics,
  PerformanceWarning,
  WarningSeverity,
  WarningType
} from '../../services/PerformanceMonitorService';
import './PerformanceAnalysisPanel.less';

const { Text } = Typography;
const { TabPane } = Tabs;

interface PerformanceAnalysisPanelProps {
  visible: boolean;
  onClose: () => void;
}

const PerformanceAnalysisPanel: React.FC<PerformanceAnalysisPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [currentMetrics, setCurrentMetrics] = useState<PerformanceMetrics | null>(null);
  const [metricsHistory, setMetricsHistory] = useState<PerformanceMetrics[]>([]);
  const [warnings, setWarnings] = useState<PerformanceWarning[]>([]);
  const [summary, setSummary] = useState<any>({});
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedWarning, setSelectedWarning] = useState<PerformanceWarning | null>(null);
  const [warningDetailVisible, setWarningDetailVisible] = useState(false);

  const performanceService = PerformanceMonitorService.getInstance();
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, [visible]);

  const setupEventListeners = () => {
    performanceService.on('metricsUpdated', handleMetricsUpdate);
    performanceService.on('warningAdded', handleWarningAdded);
    
    // 定期更新数据
    updateIntervalRef.current = setInterval(() => {
      updateData();
    }, 1000);
  };

  const cleanupEventListeners = () => {
    performanceService.off('metricsUpdated', handleMetricsUpdate);
    performanceService.off('warningAdded', handleWarningAdded);
  };

  const loadData = () => {
    setCurrentMetrics(performanceService.getCurrentMetrics());
    setMetricsHistory(performanceService.getMetricsHistory(60)); // 最近1分钟
    setWarnings(performanceService.getWarnings());
    setSummary(performanceService.getPerformanceSummary());
  };

  const updateData = () => {
    setCurrentMetrics(performanceService.getCurrentMetrics());
    setMetricsHistory(performanceService.getMetricsHistory(60));
    setSummary(performanceService.getPerformanceSummary());
  };

  const handleMetricsUpdate = (metrics: PerformanceMetrics) => {
    setCurrentMetrics(metrics);
  };

  const handleWarningAdded = (warning: PerformanceWarning) => {
    setWarnings(prev => [...prev, warning]);
  };

  const handleToggleMonitoring = () => {
    if (isMonitoring) {
      performanceService.stopMonitoring();
    } else {
      performanceService.startMonitoring();
    }
    setIsMonitoring(!isMonitoring);
  };

  const handleClearWarnings = () => {
    performanceService.clearWarnings();
    setWarnings([]);
  };

  const handleViewWarningDetail = (warning: PerformanceWarning) => {
    setSelectedWarning(warning);
    setWarningDetailVisible(true);
  };

  // 获取警告严重程度颜色
  const getWarningSeverityColor = (severity: WarningSeverity) => {
    switch (severity) {
      case WarningSeverity.INFO:
        return 'blue';
      case WarningSeverity.WARNING:
        return 'orange';
      case WarningSeverity.ERROR:
        return 'red';
      case WarningSeverity.CRITICAL:
        return 'red';
      default:
        return 'default';
    }
  };

  // 获取警告类型图标
  const getWarningTypeIcon = (type: WarningType) => {
    switch (type) {
      case WarningType.LOW_FPS:
        return <LineChartOutlined />;
      case WarningType.HIGH_MEMORY:
      case WarningType.MEMORY_LEAK:
        return <DatabaseOutlined />;
      case WarningType.HIGH_CPU_USAGE:
        return <ThunderboltOutlined />;
      default:
        return <WarningOutlined />;
    }
  };

  // 准备图表数据
  const prepareChartData = (key: keyof PerformanceMetrics) => {
    return metricsHistory.map((metrics, index) => ({
      time: index,
      value: typeof metrics[key] === 'number' ? metrics[key] : 
             key === 'memoryUsage' ? (metrics[key] as any).used / 1024 / 1024 : 0
    }));
  };

  // 渲染概览页面
  const renderOverview = () => (
    <div className="performance-overview">
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card>
            <Statistic
              title="FPS"
              value={currentMetrics?.fps || 0}
              suffix="fps"
              valueStyle={{ 
                color: (currentMetrics?.fps || 0) < 30 ? '#ff4d4f' : '#3f8600' 
              }}
              prefix={<LineChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('debug.performance.frameTime')}
              value={currentMetrics?.frameTime || 0}
              suffix="ms"
              precision={2}
              valueStyle={{ 
                color: (currentMetrics?.frameTime || 0) > 33 ? '#ff4d4f' : '#3f8600' 
              }}
              prefix={<DashboardOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('debug.performance.memoryUsage')}
              value={(currentMetrics?.memoryUsage.used || 0) / 1024 / 1024}
              suffix="MB"
              precision={1}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('debug.performance.cpuUsage')}
              value={currentMetrics?.cpuUsage || 0}
              suffix="%"
              precision={1}
              valueStyle={{ 
                color: (currentMetrics?.cpuUsage || 0) > 80 ? '#ff4d4f' : '#3f8600' 
              }}
              prefix={<ThunderboltOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={12}>
          <Card title={t('debug.performance.renderStats')} size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label={t('debug.performance.drawCalls')}>
                {currentMetrics?.drawCalls || 0}
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.triangles')}>
                {(currentMetrics?.triangles || 0).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.renderTime')}>
                {(currentMetrics?.renderTime || 0).toFixed(2)}ms
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.updateTime')}>
                {(currentMetrics?.updateTime || 0).toFixed(2)}ms
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col span={12}>
          <Card title={t('debug.performance.summary')} size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label={t('debug.performance.avgFps')}>
                {summary.avgFps || 0} fps
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.avgFrameTime')}>
                {summary.avgFrameTime || 0} ms
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.avgMemory')}>
                {((summary.avgMemoryUsage || 0) / 1024 / 1024).toFixed(1)} MB
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.warnings')}>
                <Badge count={summary.warningCount || 0} />
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </div>
  );

  // 渲染图表页面
  const renderCharts = () => (
    <div className="performance-charts">
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="FPS" size="small">
            <Line
              data={prepareChartData('fps')}
              xField="time"
              yField="value"
              height={200}
              smooth
              color="#1890ff"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title={t('debug.performance.memoryUsage')} size="small">
            <Line
              data={prepareChartData('memoryUsage')}
              xField="time"
              yField="value"
              height={200}
              smooth
              color="#52c41a"
            />
          </Card>
        </Col>
      </Row>
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={12}>
          <Card title={t('debug.performance.frameTime')} size="small">
            <Line
              data={prepareChartData('frameTime')}
              xField="time"
              yField="value"
              height={200}
              smooth
              color="#faad14"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title={t('debug.performance.cpuUsage')} size="small">
            <Line
              data={prepareChartData('cpuUsage')}
              xField="time"
              yField="value"
              height={200}
              smooth
              color="#f5222d"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );

  // 渲染警告页面
  const renderWarnings = () => (
    <div className="performance-warnings">
      <div className="warnings-header">
        <Space>
          <Button 
            type="primary" 
            danger 
            icon={<ClearOutlined />}
            onClick={handleClearWarnings}
            disabled={warnings.length === 0}
          >
            {t('debug.performance.clearWarnings')}
          </Button>
          <Text type="secondary">
            {t('debug.performance.totalWarnings', { count: warnings.length })}
          </Text>
        </Space>
      </div>

      <List
        dataSource={warnings.slice().reverse()}
        renderItem={(warning) => (
          <List.Item
            actions={[
              <Button
                type="link"
                onClick={() => handleViewWarningDetail(warning)}
              >
                {t('debug.performance.viewDetails')}
              </Button>
            ]}
          >
            <List.Item.Meta
              avatar={getWarningTypeIcon(warning.type)}
              title={
                <Space>
                  <Text strong>{warning.message}</Text>
                  <Tag color={getWarningSeverityColor(warning.severity)}>
                    {warning.severity}
                  </Tag>
                </Space>
              }
              description={
                <div>
                  <Text type="secondary">{warning.details}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {new Date(warning.timestamp).toLocaleString()}
                  </Text>
                </div>
              }
            />
          </List.Item>
        )}
        locale={{ emptyText: t('debug.performance.noWarnings') }}
      />
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <DashboardOutlined />
          {t('debug.performance.performanceAnalysis')}
          <Switch
            checked={isMonitoring}
            onChange={handleToggleMonitoring}
            checkedChildren={<PauseCircleOutlined />}
            unCheckedChildren={<PlayCircleOutlined />}
          />
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
      className="performance-analysis-panel"
    >
      {summary.criticalWarningCount > 0 && (
        <Alert
          message={t('debug.performance.criticalWarnings')}
          description={t('debug.performance.criticalWarningsDesc', { 
            count: summary.criticalWarningCount 
          })}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <Space>
              <DashboardOutlined />
              {t('debug.performance.overview')}
            </Space>
          }
          key="overview"
        >
          {renderOverview()}
        </TabPane>

        <TabPane
          tab={
            <Space>
              <LineChartOutlined />
              {t('debug.performance.charts')}
            </Space>
          }
          key="charts"
        >
          {renderCharts()}
        </TabPane>

        <TabPane
          tab={
            <Space>
              <Badge count={warnings.length}>
                <WarningOutlined />
              </Badge>
              {t('debug.performance.warnings')}
            </Space>
          }
          key="warnings"
        >
          {renderWarnings()}
        </TabPane>
      </Tabs>

      {/* 警告详情对话框 */}
      <Modal
        title={t('debug.performance.warningDetails')}
        open={warningDetailVisible}
        onCancel={() => setWarningDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setWarningDetailVisible(false)}>
            {t('common.close')}
          </Button>
        ]}
      >
        {selectedWarning && (
          <div className="warning-details">
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label={t('debug.performance.type')}>
                <Space>
                  {getWarningTypeIcon(selectedWarning.type)}
                  {selectedWarning.type}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.severity')}>
                <Tag color={getWarningSeverityColor(selectedWarning.severity)}>
                  {selectedWarning.severity}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.message')}>
                {selectedWarning.message}
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.details')}>
                {selectedWarning.details}
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.value')}>
                {selectedWarning.value.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.threshold')}>
                {selectedWarning.threshold.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.timestamp')}>
                {new Date(selectedWarning.timestamp).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.performance.suggestions')}>
                <List
                  size="small"
                  dataSource={selectedWarning.suggestions}
                  renderItem={(suggestion) => (
                    <List.Item>
                      <Text>• {suggestion}</Text>
                    </List.Item>
                  )}
                />
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </Modal>
  );
};

export default PerformanceAnalysisPanel;
