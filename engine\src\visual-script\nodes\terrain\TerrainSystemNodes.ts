/**
 * 地形系统节点集合
 * 提供地形生成、编辑、纹理混合等功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Vector2, Color } from 'three';

/**
 * 地形类型枚举
 */
export enum TerrainType {
  HEIGHTMAP = 'heightmap',
  VOXEL = 'voxel',
  MESH = 'mesh',
  PROCEDURAL = 'procedural',
  SCULPTED = 'sculpted'
}

/**
 * 地形生成算法枚举
 */
export enum TerrainAlgorithm {
  PERLIN_NOISE = 'perlinNoise',
  SIMPLEX_NOISE = 'simplexNoise',
  RIDGED_NOISE = 'ridgedNoise',
  DIAMOND_SQUARE = 'diamondSquare',
  EROSION = 'erosion',
  THERMAL_EROSION = 'thermalErosion',
  HYDRAULIC_EROSION = 'hydraulicErosion',
  VORONOI = 'voronoi',
  FRACTAL = 'fractal'
}

/**
 * 地形配置接口
 */
export interface TerrainConfig {
  type: TerrainType;
  size: Vector2;
  resolution: Vector2;
  heightScale: number;
  seed: number;
  algorithm: TerrainAlgorithm;
  octaves: number;
  frequency: number;
  amplitude: number;
  persistence: number;
  lacunarity: number;
  offset: Vector2;
}

/**
 * 地形层配置
 */
export interface TerrainLayer {
  id: string;
  name: string;
  texture: string;
  normalMap?: string;
  heightRange: Vector2;
  slopeRange: Vector2;
  blendMode: 'height' | 'slope' | 'noise' | 'manual';
  tiling: Vector2;
  strength: number;
  roughness: number;
  metallic: number;
}

/**
 * 侵蚀配置
 */
export interface ErosionConfig {
  type: 'thermal' | 'hydraulic' | 'wind';
  iterations: number;
  strength: number;
  evaporationRate: number;
  sedimentCapacity: number;
  depositionRate: number;
  erosionRate: number;
  minSlope: number;
  gravity: number;
}

/**
 * 高级地形生成器
 */
class AdvancedTerrainGenerator {
  private heightmaps: Map<string, Float32Array> = new Map();
  private terrainConfigs: Map<string, TerrainConfig> = new Map();
  private terrainLayers: Map<string, TerrainLayer[]> = new Map();
  private noiseGenerators: Map<string, any> = new Map();

  /**
   * 生成地形
   */
  generateTerrain(id: string, config: TerrainConfig): Float32Array {
    this.terrainConfigs.set(id, config);
    
    const heightmap = this.createHeightmap(config);
    this.heightmaps.set(id, heightmap);
    
    Debug.log('AdvancedTerrainGenerator', `地形生成完成: ${id} (${config.resolution.x}x${config.resolution.y})`);
    
    return heightmap;
  }

  /**
   * 创建高度图
   */
  private createHeightmap(config: TerrainConfig): Float32Array {
    const width = config.resolution.x;
    const height = config.resolution.y;
    const heightmap = new Float32Array(width * height);
    
    switch (config.algorithm) {
      case TerrainAlgorithm.PERLIN_NOISE:
        this.generatePerlinNoise(heightmap, config);
        break;
      case TerrainAlgorithm.SIMPLEX_NOISE:
        this.generateSimplexNoise(heightmap, config);
        break;
      case TerrainAlgorithm.DIAMOND_SQUARE:
        this.generateDiamondSquare(heightmap, config);
        break;
      case TerrainAlgorithm.RIDGED_NOISE:
        this.generateRidgedNoise(heightmap, config);
        break;
      case TerrainAlgorithm.VORONOI:
        this.generateVoronoi(heightmap, config);
        break;
      case TerrainAlgorithm.FRACTAL:
        this.generateFractal(heightmap, config);
        break;
      default:
        this.generatePerlinNoise(heightmap, config);
        break;
    }
    
    return heightmap;
  }

  /**
   * 生成柏林噪声
   */
  private generatePerlinNoise(heightmap: Float32Array, config: TerrainConfig): void {
    const width = config.resolution.x;
    const height = config.resolution.y;
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let value = 0;
        let amplitude = config.amplitude;
        let frequency = config.frequency;
        
        for (let octave = 0; octave < config.octaves; octave++) {
          const sampleX = (x + config.offset.x) * frequency / width;
          const sampleY = (y + config.offset.y) * frequency / height;
          
          const noiseValue = this.perlinNoise(sampleX, sampleY, config.seed);
          value += noiseValue * amplitude;
          
          amplitude *= config.persistence;
          frequency *= config.lacunarity;
        }
        
        heightmap[y * width + x] = value * config.heightScale;
      }
    }
  }

  /**
   * 生成单纯形噪声
   */
  private generateSimplexNoise(heightmap: Float32Array, config: TerrainConfig): void {
    const width = config.resolution.x;
    const height = config.resolution.y;
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let value = 0;
        let amplitude = config.amplitude;
        let frequency = config.frequency;
        
        for (let octave = 0; octave < config.octaves; octave++) {
          const sampleX = (x + config.offset.x) * frequency / width;
          const sampleY = (y + config.offset.y) * frequency / height;
          
          const noiseValue = this.simplexNoise(sampleX, sampleY, config.seed);
          value += noiseValue * amplitude;
          
          amplitude *= config.persistence;
          frequency *= config.lacunarity;
        }
        
        heightmap[y * width + x] = value * config.heightScale;
      }
    }
  }

  /**
   * 生成钻石方形算法
   */
  private generateDiamondSquare(heightmap: Float32Array, config: TerrainConfig): void {
    const size = Math.max(config.resolution.x, config.resolution.y);
    const mapSize = this.nextPowerOfTwo(size - 1) + 1;
    const tempMap = new Float32Array(mapSize * mapSize);
    
    // 初始化四个角
    tempMap[0] = Math.random() * config.heightScale;
    tempMap[mapSize - 1] = Math.random() * config.heightScale;
    tempMap[(mapSize - 1) * mapSize] = Math.random() * config.heightScale;
    tempMap[(mapSize - 1) * mapSize + mapSize - 1] = Math.random() * config.heightScale;
    
    let stepSize = mapSize - 1;
    let scale = config.amplitude;
    
    while (stepSize > 1) {
      const halfStep = stepSize / 2;
      
      // Diamond step
      for (let y = halfStep; y < mapSize; y += stepSize) {
        for (let x = halfStep; x < mapSize; x += stepSize) {
          const avg = (
            tempMap[(y - halfStep) * mapSize + (x - halfStep)] +
            tempMap[(y - halfStep) * mapSize + (x + halfStep)] +
            tempMap[(y + halfStep) * mapSize + (x - halfStep)] +
            tempMap[(y + halfStep) * mapSize + (x + halfStep)]
          ) / 4;
          
          tempMap[y * mapSize + x] = avg + (Math.random() - 0.5) * scale;
        }
      }
      
      // Square step
      for (let y = 0; y < mapSize; y += halfStep) {
        for (let x = (y + halfStep) % stepSize; x < mapSize; x += stepSize) {
          const avg = this.getSquareAverage(tempMap, x, y, halfStep, mapSize);
          tempMap[y * mapSize + x] = avg + (Math.random() - 0.5) * scale;
        }
      }
      
      stepSize /= 2;
      scale *= config.persistence;
    }
    
    // 复制到目标高度图
    this.copyHeightmap(tempMap, mapSize, heightmap, config.resolution.x, config.resolution.y);
  }

  /**
   * 生成脊状噪声
   */
  private generateRidgedNoise(heightmap: Float32Array, config: TerrainConfig): void {
    const width = config.resolution.x;
    const height = config.resolution.y;
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let value = 0;
        let amplitude = config.amplitude;
        let frequency = config.frequency;
        
        for (let octave = 0; octave < config.octaves; octave++) {
          const sampleX = (x + config.offset.x) * frequency / width;
          const sampleY = (y + config.offset.y) * frequency / height;
          
          let noiseValue = this.perlinNoise(sampleX, sampleY, config.seed);
          noiseValue = 1.0 - Math.abs(noiseValue); // 创建脊状效果
          noiseValue = noiseValue * noiseValue; // 锐化脊
          
          value += noiseValue * amplitude;
          
          amplitude *= config.persistence;
          frequency *= config.lacunarity;
        }
        
        heightmap[y * width + x] = value * config.heightScale;
      }
    }
  }

  /**
   * 生成Voronoi图
   */
  private generateVoronoi(heightmap: Float32Array, config: TerrainConfig): void {
    const width = config.resolution.x;
    const height = config.resolution.y;
    const numPoints = Math.floor(config.frequency * 10);
    
    // 生成随机点
    const points: Vector2[] = [];
    for (let i = 0; i < numPoints; i++) {
      points.push(new Vector2(
        Math.random() * width,
        Math.random() * height
      ));
    }
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let minDistance = Infinity;
        
        for (const point of points) {
          const distance = Math.sqrt(
            Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2)
          );
          minDistance = Math.min(minDistance, distance);
        }
        
        const normalizedDistance = minDistance / Math.sqrt(width * width + height * height);
        heightmap[y * width + x] = normalizedDistance * config.heightScale;
      }
    }
  }

  /**
   * 生成分形地形
   */
  private generateFractal(heightmap: Float32Array, config: TerrainConfig): void {
    // 组合多种噪声算法
    const tempMap1 = new Float32Array(heightmap.length);
    const tempMap2 = new Float32Array(heightmap.length);
    
    // 基础地形
    const baseConfig = { ...config, algorithm: TerrainAlgorithm.PERLIN_NOISE };
    this.generatePerlinNoise(tempMap1, baseConfig);
    
    // 细节层
    const detailConfig = { 
      ...config, 
      algorithm: TerrainAlgorithm.RIDGED_NOISE,
      frequency: config.frequency * 4,
      amplitude: config.amplitude * 0.3
    };
    this.generateRidgedNoise(tempMap2, detailConfig);
    
    // 混合
    for (let i = 0; i < heightmap.length; i++) {
      heightmap[i] = tempMap1[i] + tempMap2[i];
    }
  }

  /**
   * 应用侵蚀效果
   */
  applyErosion(id: string, erosionConfig: ErosionConfig): void {
    const heightmap = this.heightmaps.get(id);
    const config = this.terrainConfigs.get(id);
    
    if (!heightmap || !config) return;
    
    switch (erosionConfig.type) {
      case 'thermal':
        this.applyThermalErosion(heightmap, config, erosionConfig);
        break;
      case 'hydraulic':
        this.applyHydraulicErosion(heightmap, config, erosionConfig);
        break;
      case 'wind':
        this.applyWindErosion(heightmap, config, erosionConfig);
        break;
    }
    
    Debug.log('AdvancedTerrainGenerator', `侵蚀效果应用: ${id} (${erosionConfig.type})`);
  }

  /**
   * 应用热力侵蚀
   */
  private applyThermalErosion(heightmap: Float32Array, config: TerrainConfig, erosionConfig: ErosionConfig): void {
    const width = config.resolution.x;
    const height = config.resolution.y;
    
    for (let iteration = 0; iteration < erosionConfig.iterations; iteration++) {
      const newHeightmap = new Float32Array(heightmap);
      
      for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
          const currentHeight = heightmap[y * width + x];
          let totalDiff = 0;
          let validNeighbors = 0;
          
          // 检查8个邻居
          for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
              if (dx === 0 && dy === 0) continue;
              
              const neighborHeight = heightmap[(y + dy) * width + (x + dx)];
              const heightDiff = currentHeight - neighborHeight;
              
              if (heightDiff > erosionConfig.minSlope) {
                totalDiff += heightDiff;
                validNeighbors++;
              }
            }
          }
          
          if (validNeighbors > 0) {
            const erosionAmount = (totalDiff / validNeighbors) * erosionConfig.strength;
            newHeightmap[y * width + x] = currentHeight - erosionAmount;
          }
        }
      }
      
      heightmap.set(newHeightmap);
    }
  }

  /**
   * 应用水力侵蚀
   */
  private applyHydraulicErosion(heightmap: Float32Array, config: TerrainConfig, erosionConfig: ErosionConfig): void {
    const width = config.resolution.x;
    const height = config.resolution.y;
    const waterMap = new Float32Array(width * height);
    const sedimentMap = new Float32Array(width * height);
    
    for (let iteration = 0; iteration < erosionConfig.iterations; iteration++) {
      // 添加雨水
      for (let i = 0; i < waterMap.length; i++) {
        waterMap[i] += 0.01;
      }
      
      // 模拟水流
      for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
          const index = y * width + x;
          const currentHeight = heightmap[index] + waterMap[index];
          
          // 找到最低的邻居
          let lowestHeight = currentHeight;
          let lowestIndex = index;
          
          for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
              if (dx === 0 && dy === 0) continue;
              
              const neighborIndex = (y + dy) * width + (x + dx);
              const neighborHeight = heightmap[neighborIndex] + waterMap[neighborIndex];
              
              if (neighborHeight < lowestHeight) {
                lowestHeight = neighborHeight;
                lowestIndex = neighborIndex;
              }
            }
          }
          
          // 如果找到更低的位置，移动水和沉积物
          if (lowestIndex !== index) {
            const heightDiff = currentHeight - lowestHeight;
            const waterFlow = Math.min(waterMap[index], heightDiff * erosionConfig.strength);
            
            waterMap[index] -= waterFlow;
            waterMap[lowestIndex] += waterFlow;
            
            // 侵蚀和沉积
            const erosionCapacity = waterFlow * erosionConfig.sedimentCapacity;
            const currentSediment = sedimentMap[index];
            
            if (currentSediment > erosionCapacity) {
              // 沉积
              const deposition = (currentSediment - erosionCapacity) * erosionConfig.depositionRate;
              heightmap[index] += deposition;
              sedimentMap[index] -= deposition;
            } else {
              // 侵蚀
              const erosion = Math.min(heightDiff, erosionCapacity - currentSediment) * erosionConfig.erosionRate;
              heightmap[index] -= erosion;
              sedimentMap[index] += erosion;
            }
          }
        }
      }
      
      // 蒸发
      for (let i = 0; i < waterMap.length; i++) {
        waterMap[i] *= (1 - erosionConfig.evaporationRate);
      }
    }
  }

  /**
   * 应用风力侵蚀
   */
  private applyWindErosion(heightmap: Float32Array, config: TerrainConfig, erosionConfig: ErosionConfig): void {
    // 简化的风力侵蚀实现
    const width = config.resolution.x;
    const height = config.resolution.y;
    
    for (let iteration = 0; iteration < erosionConfig.iterations; iteration++) {
      const newHeightmap = new Float32Array(heightmap);
      
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const index = y * width + x;
          const currentHeight = heightmap[index];
          
          // 计算坡度
          const slope = this.calculateSlope(heightmap, x, y, width, height);
          
          if (slope > erosionConfig.minSlope) {
            const erosionAmount = slope * erosionConfig.strength;
            newHeightmap[index] = currentHeight - erosionAmount;
          }
        }
      }
      
      heightmap.set(newHeightmap);
    }
  }

  // 辅助方法
  private perlinNoise(x: number, y: number, seed: number): number {
    // 简化的柏林噪声实现
    return (Math.sin(x * 12.9898 + y * 78.233 + seed) * 43758.5453) % 1;
  }

  private simplexNoise(x: number, y: number, seed: number): number {
    // 简化的单纯形噪声实现
    return (Math.sin(x * 15.32 + y * 67.45 + seed) * 23421.631) % 1;
  }

  private nextPowerOfTwo(n: number): number {
    return Math.pow(2, Math.ceil(Math.log2(n)));
  }

  private getSquareAverage(map: Float32Array, x: number, y: number, size: number, mapSize: number): number {
    let sum = 0;
    let count = 0;
    
    const positions = [
      [x - size, y],
      [x + size, y],
      [x, y - size],
      [x, y + size]
    ];
    
    for (const [px, py] of positions) {
      if (px >= 0 && px < mapSize && py >= 0 && py < mapSize) {
        sum += map[py * mapSize + px];
        count++;
      }
    }
    
    return count > 0 ? sum / count : 0;
  }

  private copyHeightmap(source: Float32Array, sourceSize: number, target: Float32Array, targetWidth: number, targetHeight: number): void {
    for (let y = 0; y < targetHeight; y++) {
      for (let x = 0; x < targetWidth; x++) {
        const sourceX = Math.floor((x / targetWidth) * sourceSize);
        const sourceY = Math.floor((y / targetHeight) * sourceSize);
        target[y * targetWidth + x] = source[sourceY * sourceSize + sourceX];
      }
    }
  }

  private calculateSlope(heightmap: Float32Array, x: number, y: number, width: number, height: number): number {
    if (x === 0 || x === width - 1 || y === 0 || y === height - 1) return 0;
    
    const currentHeight = heightmap[y * width + x];
    const leftHeight = heightmap[y * width + (x - 1)];
    const rightHeight = heightmap[y * width + (x + 1)];
    const topHeight = heightmap[(y - 1) * width + x];
    const bottomHeight = heightmap[(y + 1) * width + x];
    
    const dx = (rightHeight - leftHeight) / 2;
    const dy = (bottomHeight - topHeight) / 2;
    
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 获取地形高度图
   */
  getHeightmap(id: string): Float32Array | undefined {
    return this.heightmaps.get(id);
  }

  /**
   * 获取地形配置
   */
  getTerrainConfig(id: string): TerrainConfig | undefined {
    return this.terrainConfigs.get(id);
  }

  /**
   * 清理地形数据
   */
  cleanup(): void {
    this.heightmaps.clear();
    this.terrainConfigs.clear();
    this.terrainLayers.clear();
    this.noiseGenerators.clear();
  }
}

/**
 * 地形生成节点
 */
export class TerrainGenerationNode extends VisualScriptNode {
  public static readonly TYPE = 'TerrainGeneration';
  public static readonly NAME = '地形生成';
  public static readonly DESCRIPTION = '生成程序化地形';

  private static terrainGenerator: AdvancedTerrainGenerator = new AdvancedTerrainGenerator();

  constructor(nodeType: string = TerrainGenerationNode.TYPE, name: string = TerrainGenerationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('generate', 'trigger', '生成');
    this.addInput('terrainId', 'string', '地形ID');
    this.addInput('algorithm', 'string', '生成算法');
    this.addInput('size', 'object', '地形尺寸');
    this.addInput('resolution', 'object', '分辨率');
    this.addInput('heightScale', 'number', '高度缩放');
    this.addInput('seed', 'number', '随机种子');
    this.addInput('octaves', 'number', '倍频数');
    this.addInput('frequency', 'number', '频率');
    this.addInput('amplitude', 'number', '振幅');
    this.addInput('persistence', 'number', '持续性');
    this.addInput('lacunarity', 'number', '间隙性');

    // 输出端口
    this.addOutput('terrainId', 'string', '地形ID');
    this.addOutput('heightmap', 'object', '高度图');
    this.addOutput('terrainSize', 'object', '地形尺寸');
    this.addOutput('vertexCount', 'number', '顶点数量');
    this.addOutput('minHeight', 'number', '最小高度');
    this.addOutput('maxHeight', 'number', '最大高度');
    this.addOutput('onGenerated', 'trigger', '生成完成');
    this.addOutput('onError', 'trigger', '生成失败');
  }

  public execute(inputs?: any): any {
    try {
      const generateTrigger = inputs?.generate;
      if (!generateTrigger) {
        return this.getDefaultOutputs();
      }

      const terrainId = inputs?.terrainId as string || this.generateTerrainId();
      const algorithm = inputs?.algorithm as string || 'perlinNoise';
      const size = inputs?.size as Vector2 || new Vector2(100, 100);
      const resolution = inputs?.resolution as Vector2 || new Vector2(256, 256);
      const heightScale = inputs?.heightScale as number || 10;
      const seed = inputs?.seed as number || Math.floor(Math.random() * 1000000);
      const octaves = inputs?.octaves as number || 4;
      const frequency = inputs?.frequency as number || 0.01;
      const amplitude = inputs?.amplitude as number || 1.0;
      const persistence = inputs?.persistence as number || 0.5;
      const lacunarity = inputs?.lacunarity as number || 2.0;

      // 创建地形配置
      const config: TerrainConfig = {
        type: TerrainType.HEIGHTMAP,
        size,
        resolution,
        heightScale,
        seed,
        algorithm: algorithm as TerrainAlgorithm,
        octaves: Math.max(1, Math.min(8, octaves)),
        frequency: Math.max(0.001, frequency),
        amplitude: Math.max(0.1, amplitude),
        persistence: Math.max(0.1, Math.min(1.0, persistence)),
        lacunarity: Math.max(1.1, lacunarity),
        offset: new Vector2(0, 0)
      };

      // 生成地形
      const heightmap = TerrainGenerationNode.terrainGenerator.generateTerrain(terrainId, config);

      // 计算统计信息
      const stats = this.calculateTerrainStats(heightmap);

      Debug.log('TerrainGenerationNode', `地形生成成功: ${terrainId} (${algorithm})`);

      return {
        terrainId,
        heightmap,
        terrainSize: size,
        vertexCount: resolution.x * resolution.y,
        minHeight: stats.minHeight,
        maxHeight: stats.maxHeight,
        onGenerated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('TerrainGenerationNode', '地形生成失败', error);
      return {
        terrainId: '',
        heightmap: null,
        terrainSize: new Vector2(0, 0),
        vertexCount: 0,
        minHeight: 0,
        maxHeight: 0,
        onGenerated: false,
        onError: true
      };
    }
  }

  private calculateTerrainStats(heightmap: Float32Array): { minHeight: number; maxHeight: number } {
    let minHeight = Infinity;
    let maxHeight = -Infinity;

    for (let i = 0; i < heightmap.length; i++) {
      const height = heightmap[i];
      minHeight = Math.min(minHeight, height);
      maxHeight = Math.max(maxHeight, height);
    }

    return { minHeight, maxHeight };
  }

  private generateTerrainId(): string {
    return 'terrain_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      terrainId: '',
      heightmap: null,
      terrainSize: new Vector2(0, 0),
      vertexCount: 0,
      minHeight: 0,
      maxHeight: 0,
      onGenerated: false,
      onError: false
    };
  }
}

/**
 * 地形侵蚀节点
 */
export class TerrainErosionNode extends VisualScriptNode {
  public static readonly TYPE = 'TerrainErosion';
  public static readonly NAME = '地形侵蚀';
  public static readonly DESCRIPTION = '对地形应用侵蚀效果';

  private static terrainGenerator: AdvancedTerrainGenerator = new AdvancedTerrainGenerator();

  constructor(nodeType: string = TerrainErosionNode.TYPE, name: string = TerrainErosionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('apply', 'trigger', '应用侵蚀');
    this.addInput('terrainId', 'string', '地形ID');
    this.addInput('erosionType', 'string', '侵蚀类型');
    this.addInput('iterations', 'number', '迭代次数');
    this.addInput('strength', 'number', '强度');
    this.addInput('evaporationRate', 'number', '蒸发率');
    this.addInput('sedimentCapacity', 'number', '沉积物容量');
    this.addInput('depositionRate', 'number', '沉积率');
    this.addInput('erosionRate', 'number', '侵蚀率');
    this.addInput('minSlope', 'number', '最小坡度');

    // 输出端口
    this.addOutput('terrainId', 'string', '地形ID');
    this.addOutput('erosionType', 'string', '侵蚀类型');
    this.addOutput('processedHeightmap', 'object', '处理后高度图');
    this.addOutput('erosionAmount', 'number', '侵蚀量');
    this.addOutput('onApplied', 'trigger', '应用完成');
    this.addOutput('onError', 'trigger', '应用失败');
  }

  public execute(inputs?: any): any {
    try {
      const applyTrigger = inputs?.apply;
      if (!applyTrigger) {
        return this.getDefaultOutputs();
      }

      const terrainId = inputs?.terrainId as string;
      if (!terrainId) {
        throw new Error('未提供地形ID');
      }

      const erosionType = inputs?.erosionType as string || 'thermal';
      const iterations = inputs?.iterations as number || 10;
      const strength = inputs?.strength as number || 0.1;
      const evaporationRate = inputs?.evaporationRate as number || 0.01;
      const sedimentCapacity = inputs?.sedimentCapacity as number || 4.0;
      const depositionRate = inputs?.depositionRate as number || 0.3;
      const erosionRate = inputs?.erosionRate as number || 0.3;
      const minSlope = inputs?.minSlope as number || 0.01;

      // 创建侵蚀配置
      const erosionConfig: ErosionConfig = {
        type: erosionType as any,
        iterations: Math.max(1, Math.min(100, iterations)),
        strength: Math.max(0.001, Math.min(1.0, strength)),
        evaporationRate: Math.max(0.001, Math.min(1.0, evaporationRate)),
        sedimentCapacity: Math.max(0.1, sedimentCapacity),
        depositionRate: Math.max(0.01, Math.min(1.0, depositionRate)),
        erosionRate: Math.max(0.01, Math.min(1.0, erosionRate)),
        minSlope: Math.max(0.001, minSlope),
        gravity: 9.81
      };

      // 获取原始高度图
      const originalHeightmap = TerrainErosionNode.terrainGenerator.getHeightmap(terrainId);
      if (!originalHeightmap) {
        throw new Error('找不到指定的地形');
      }

      // 应用侵蚀
      TerrainErosionNode.terrainGenerator.applyErosion(terrainId, erosionConfig);

      // 获取处理后的高度图
      const processedHeightmap = TerrainErosionNode.terrainGenerator.getHeightmap(terrainId);

      // 计算侵蚀量
      const erosionAmount = this.calculateErosionAmount(originalHeightmap, processedHeightmap!);

      Debug.log('TerrainErosionNode', `地形侵蚀应用成功: ${terrainId} (${erosionType})`);

      return {
        terrainId,
        erosionType,
        processedHeightmap,
        erosionAmount,
        onApplied: true,
        onError: false
      };

    } catch (error) {
      Debug.error('TerrainErosionNode', '地形侵蚀应用失败', error);
      return {
        terrainId: '',
        erosionType: '',
        processedHeightmap: null,
        erosionAmount: 0,
        onApplied: false,
        onError: true
      };
    }
  }

  private calculateErosionAmount(original: Float32Array, processed: Float32Array): number {
    let totalDifference = 0;

    for (let i = 0; i < original.length; i++) {
      totalDifference += Math.abs(original[i] - processed[i]);
    }

    return totalDifference / original.length;
  }

  private getDefaultOutputs(): any {
    return {
      terrainId: '',
      erosionType: '',
      processedHeightmap: null,
      erosionAmount: 0,
      onApplied: false,
      onError: false
    };
  }
}
