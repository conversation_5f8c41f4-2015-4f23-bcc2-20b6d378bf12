/**
 * 摄像头输入节点
 * 提供摄像头视频流输入功能，集成真实WebRTC API
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 摄像头输入节点配置
 */
export interface CameraInputNodeConfig {
  /** 摄像头设备ID */
  deviceId?: string;
  /** 分辨率 */
  resolution: { width: number; height: number };
  /** 帧率 */
  frameRate: number;
  /** 是否自动启动 */
  autoStart: boolean;
  /** 是否启用音频 */
  enableAudio?: boolean;
  /** 视频约束 */
  videoConstraints?: MediaTrackConstraints;
  /** 音频约束 */
  audioConstraints?: MediaTrackConstraints;
}

/**
 * 摄像头状态枚举
 */
export enum CameraState {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  REQUESTING_PERMISSION = 'requesting_permission',
  ACTIVE = 'active',
  ERROR = 'error',
  PERMISSION_DENIED = 'permission_denied'
}

/**
 * 摄像头设备信息
 */
export interface CameraDeviceInfo {
  deviceId: string;
  label: string;
  kind: MediaDeviceKind;
  groupId: string;
}

/**
 * 真实的摄像头管理器
 */
class RealCameraManager {
  private mediaStream: MediaStream | null = null;
  private videoElement: HTMLVideoElement | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private context: CanvasRenderingContext2D | null = null;
  private animationFrameId: number | null = null;
  private state: CameraState = CameraState.IDLE;
  private frameCount = 0;
  private lastFrameTime = 0;
  private actualFPS = 0;
  private eventListeners: Map<string, Function[]> = new Map();
  private currentFrame: ImageData | null = null;
  private config: CameraInputNodeConfig;

  constructor(config: CameraInputNodeConfig) {
    this.config = config;
    this.setupCanvas();
  }

  /**
   * 设置画布
   */
  private setupCanvas(): void {
    this.canvas = document.createElement('canvas');
    this.canvas.width = this.config.resolution.width;
    this.canvas.height = this.config.resolution.height;
    this.context = this.canvas.getContext('2d');

    this.videoElement = document.createElement('video');
    this.videoElement.width = this.config.resolution.width;
    this.videoElement.height = this.config.resolution.height;
    this.videoElement.autoplay = true;
    this.videoElement.muted = true;
    this.videoElement.playsInline = true;
  }

  /**
   * 获取可用的摄像头设备
   */
  async getAvailableDevices(): Promise<CameraDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices
        .filter(device => device.kind === 'videoinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `摄像头 ${device.deviceId.slice(0, 8)}`,
          kind: device.kind,
          groupId: device.groupId
        }));
    } catch (error) {
      Debug.error('CameraManager', '获取设备列表失败', error);
      return [];
    }
  }

  /**
   * 初始化摄像头
   */
  async initialize(deviceId?: string): Promise<void> {
    try {
      this.setState(CameraState.INITIALIZING);

      // 检查浏览器支持
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('浏览器不支持摄像头访问');
      }

      this.setState(CameraState.REQUESTING_PERMISSION);

      // 构建媒体约束
      const constraints: MediaStreamConstraints = {
        video: {
          deviceId: deviceId ? { exact: deviceId } : undefined,
          width: { ideal: this.config.resolution.width },
          height: { ideal: this.config.resolution.height },
          frameRate: { ideal: this.config.frameRate },
          ...this.config.videoConstraints
        },
        audio: this.config.enableAudio ? (this.config.audioConstraints || true) : false
      };

      // 获取媒体流
      this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

      if (!this.videoElement) {
        throw new Error('视频元素未初始化');
      }

      // 设置视频源
      this.videoElement.srcObject = this.mediaStream;

      // 等待视频加载
      await new Promise<void>((resolve, reject) => {
        if (!this.videoElement) {
          reject(new Error('视频元素未初始化'));
          return;
        }

        this.videoElement.onloadedmetadata = () => {
          resolve();
        };

        this.videoElement.onerror = () => {
          reject(new Error('视频加载失败'));
        };
      });

      // 开始帧捕获
      this.startFrameCapture();
      this.setState(CameraState.ACTIVE);
      this.emit('started');

    } catch (error) {
      Debug.error('CameraManager', '初始化摄像头失败', error);

      if (error instanceof DOMException) {
        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
          this.setState(CameraState.PERMISSION_DENIED);
        } else {
          this.setState(CameraState.ERROR);
        }
      } else {
        this.setState(CameraState.ERROR);
      }

      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 开始帧捕获
   */
  private startFrameCapture(): void {
    const captureFrame = () => {
      if (this.state !== CameraState.ACTIVE || !this.videoElement || !this.canvas || !this.context) {
        return;
      }

      try {
        // 绘制视频帧到画布
        this.context.drawImage(
          this.videoElement,
          0, 0,
          this.canvas.width,
          this.canvas.height
        );

        // 获取图像数据
        this.currentFrame = this.context.getImageData(
          0, 0,
          this.canvas.width,
          this.canvas.height
        );

        // 更新帧计数和FPS
        this.frameCount++;
        const currentTime = performance.now();
        if (this.lastFrameTime > 0) {
          const deltaTime = currentTime - this.lastFrameTime;
          this.actualFPS = 1000 / deltaTime;
        }
        this.lastFrameTime = currentTime;

        // 发出新帧事件
        this.emit('frame', this.currentFrame);

      } catch (error) {
        Debug.error('CameraManager', '帧捕获失败', error);
      }

      // 继续下一帧
      this.animationFrameId = requestAnimationFrame(captureFrame);
    };

    captureFrame();
  }

  /**
   * 停止摄像头
   */
  async stop(): Promise<void> {
    try {
      // 停止帧捕获
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }

      // 停止媒体流
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach(track => track.stop());
        this.mediaStream = null;
      }

      // 清理视频元素
      if (this.videoElement) {
        this.videoElement.srcObject = null;
      }

      this.setState(CameraState.IDLE);
      this.currentFrame = null;
      this.frameCount = 0;
      this.actualFPS = 0;
      this.emit('stopped');

    } catch (error) {
      Debug.error('CameraManager', '停止摄像头失败', error);
      this.setState(CameraState.ERROR);
      throw error;
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stop().catch(error => {
      Debug.error('CameraManager', '销毁时停止摄像头失败', error);
    });

    // 清理DOM元素
    if (this.canvas) {
      this.canvas.remove();
      this.canvas = null;
    }
    if (this.videoElement) {
      this.videoElement.remove();
      this.videoElement = null;
    }

    this.context = null;
    this.eventListeners.clear();
  }

  /**
   * 获取当前帧
   */
  getCurrentFrame(): ImageData | null {
    return this.currentFrame;
  }

  /**
   * 获取实际FPS
   */
  getActualFPS(): number {
    return this.actualFPS;
  }

  /**
   * 获取状态
   */
  getState(): CameraState {
    return this.state;
  }

  /**
   * 设置状态
   */
  private setState(newState: CameraState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
      this.emit('stateChanged', { oldState, newState });
    }
  }

  /**
   * 事件监听
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * 移除事件监听
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 发出事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('CameraManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 获取帧计数
   */
  getFrameCount(): number {
    return this.frameCount;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<CameraInputNodeConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // 如果摄像头正在运行，需要重新初始化
    if (this.state === CameraState.ACTIVE) {
      this.stop().then(() => {
        return this.initialize(this.config.deviceId);
      }).catch(error => {
        Debug.error('CameraManager', '更新配置时重新初始化失败', error);
      });
    }
  }
}

/**
 * 摄像头输入节点
 */
export class CameraInputNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'CameraInput';

  /** 节点名称 */
  public static readonly NAME = '摄像头输入';

  /** 节点描述 */
  public static readonly DESCRIPTION = '从摄像头获取视频流数据，支持真实WebRTC API';

  private cameraManager: RealCameraManager | null = null;
  private config: CameraInputNodeConfig;
  private isActive = false;
  private lastFrame: ImageData | null = null;
  private frameCount = 0;
  private availableDevices: CameraDeviceInfo[] = [];

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: CameraInputNodeConfig = {
    resolution: { width: 640, height: 480 },
    frameRate: 30,
    autoStart: false,
    enableAudio: false
  };

  constructor(nodeType: string = CameraInputNode.TYPE, name: string = CameraInputNode.NAME, id?: string) {
    super(nodeType, name, id);

    this.config = { ...CameraInputNode.DEFAULT_CONFIG };
    this.setupPorts();
    this.initializeDeviceList();
  }

  /**
   * 初始化设备列表
   */
  private async initializeDeviceList(): Promise<void> {
    try {
      // 请求权限以获取设备标签
      const tempStream = await navigator.mediaDevices.getUserMedia({ video: true });
      tempStream.getTracks().forEach(track => track.stop());

      // 获取设备列表
      const devices = await navigator.mediaDevices.enumerateDevices();
      this.availableDevices = devices
        .filter(device => device.kind === 'videoinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `摄像头 ${device.deviceId.slice(0, 8)}`,
          kind: device.kind,
          groupId: device.groupId
        }));
    } catch (error) {
      Debug.warn('CameraInputNode', '无法获取设备列表', error);
      this.availableDevices = [];
    }
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput('start', 'trigger', '启动');
    this.addInput('stop', 'trigger', '停止');
    this.addInput('deviceId', 'string', '设备ID');
    this.addInput('resolution', 'object', '分辨率');
    this.addInput('frameRate', 'number', '帧率');
    this.addInput('enableAudio', 'boolean', '启用音频');

    // 输出端口
    this.addOutput('frame', 'object', '视频帧');
    this.addOutput('imageData', 'object', '图像数据');
    this.addOutput('isActive', 'boolean', '是否活跃');
    this.addOutput('frameCount', 'number', '帧计数');
    this.addOutput('fps', 'number', 'FPS');
    this.addOutput('state', 'string', '摄像头状态');
    this.addOutput('devices', 'array', '可用设备');
    this.addOutput('onStarted', 'trigger', '启动完成');
    this.addOutput('onStopped', 'trigger', '停止完成');
    this.addOutput('onError', 'trigger', '错误');
    this.addOutput('onFrame', 'trigger', '新帧');
    this.addOutput('onPermissionDenied', 'trigger', '权限被拒绝');
  }

  /**
   * 执行节点
   */
  public async execute(inputs?: any): Promise<any> {
    try {
      // 检查输入
      const startTrigger = inputs?.start;
      const stopTrigger = inputs?.stop;
      const deviceId = inputs?.deviceId as string;
      const resolution = inputs?.resolution as { width: number; height: number };
      const frameRate = inputs?.frameRate as number;
      const enableAudio = inputs?.enableAudio as boolean;

      // 更新配置
      let configChanged = false;
      if (deviceId !== undefined && deviceId !== this.config.deviceId) {
        this.config.deviceId = deviceId;
        configChanged = true;
      }
      if (resolution && (
        resolution.width !== this.config.resolution.width ||
        resolution.height !== this.config.resolution.height
      )) {
        this.config.resolution = resolution;
        configChanged = true;
      }
      if (frameRate !== undefined && frameRate !== this.config.frameRate) {
        this.config.frameRate = frameRate;
        configChanged = true;
      }
      if (enableAudio !== undefined && enableAudio !== this.config.enableAudio) {
        this.config.enableAudio = enableAudio;
        configChanged = true;
      }

      // 如果配置改变且摄像头正在运行，重新启动
      if (configChanged && this.isActive) {
        await this.stopCamera();
        await this.startCamera();
      }

      // 处理启动触发
      if (startTrigger && !this.isActive) {
        await this.startCamera();
      }

      // 处理停止触发
      if (stopTrigger && this.isActive) {
        await this.stopCamera();
      }

      // 返回输出
      return this.getOutputs();

    } catch (error) {
      Debug.error('CameraInputNode', '节点执行失败', String(error));
      return {
        onError: true,
        state: CameraState.ERROR,
        isActive: false
      };
    }
  }

  /**
   * 获取输出值
   */
  public getOutputs(): any {
    return {
      frame: this.lastFrame,
      imageData: this.lastFrame,
      isActive: this.isActive,
      frameCount: this.frameCount,
      fps: this.cameraManager?.getActualFPS() || 0,
      state: this.cameraManager?.getState() || CameraState.IDLE,
      devices: this.availableDevices,
      onStarted: false,
      onStopped: false,
      onError: false,
      onFrame: false,
      onPermissionDenied: false
    };
  }

  /**
   * 启动摄像头
   */
  private async startCamera(): Promise<void> {
    try {
      // 创建摄像头管理器
      if (!this.cameraManager) {
        this.cameraManager = new RealCameraManager(this.config);
        this.setupCameraEvents();
      }

      // 初始化摄像头
      await this.cameraManager.initialize(this.config.deviceId);
      this.isActive = true;
      this.frameCount = 0;

      Debug.log('CameraInputNode', '摄像头启动成功');

    } catch (error) {
      Debug.error('CameraInputNode', '启动摄像头失败', String(error));
      this.isActive = false;

      if (this.cameraManager?.getState() === CameraState.PERMISSION_DENIED) {
        // 权限被拒绝的特殊处理
        Debug.warn('CameraInputNode', '摄像头权限被拒绝');
      }

      throw error;
    }
  }

  /**
   * 停止摄像头
   */
  private async stopCamera(): Promise<void> {
    try {
      if (this.cameraManager) {
        await this.cameraManager.stop();
      }

      this.isActive = false;
      this.lastFrame = null;
      this.frameCount = 0;

      Debug.log('CameraInputNode', '摄像头已停止');

    } catch (error) {
      Debug.error('CameraInputNode', '停止摄像头失败', String(error));
      throw error;
    }
  }

  /**
   * 设置摄像头事件监听
   */
  private setupCameraEvents(): void {
    if (!this.cameraManager) return;

    this.cameraManager.on('frame', (frame: ImageData) => {
      this.lastFrame = frame;
      this.frameCount = this.cameraManager!.getFrameCount();
    });

    this.cameraManager.on('started', () => {
      Debug.log('CameraInputNode', '摄像头启动事件');
    });

    this.cameraManager.on('stopped', () => {
      Debug.log('CameraInputNode', '摄像头停止事件');
    });

    this.cameraManager.on('error', (error: any) => {
      Debug.error('CameraInputNode', '摄像头错误事件', error);
    });

    this.cameraManager.on('stateChanged', ({ oldState, newState }: any) => {
      Debug.log('CameraInputNode', `摄像头状态变化: ${oldState} -> ${newState}`);
    });
  }

  /**
   * 获取节点配置
   */
  public getConfig(): CameraInputNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public updateConfig(newConfig: Partial<CameraInputNodeConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // 如果摄像头管理器存在，更新其配置
    if (this.cameraManager) {
      this.cameraManager.updateConfig(this.config);
    }

    Debug.log('CameraInputNode', '配置已更新', { oldConfig, newConfig: this.config });
  }

  /**
   * 获取当前帧
   */
  public getCurrentFrame(): ImageData | null {
    return this.lastFrame;
  }

  /**
   * 获取摄像头状态
   */
  public getCameraState(): CameraState {
    return this.cameraManager?.getState() || CameraState.IDLE;
  }

  /**
   * 获取帧计数
   */
  public getFrameCount(): number {
    return this.frameCount;
  }

  /**
   * 获取可用设备列表
   */
  public getAvailableDevices(): CameraDeviceInfo[] {
    return [...this.availableDevices];
  }

  /**
   * 刷新设备列表
   */
  public async refreshDevices(): Promise<CameraDeviceInfo[]> {
    await this.initializeDeviceList();
    return this.getAvailableDevices();
  }

  /**
   * 获取实际FPS
   */
  public getActualFPS(): number {
    return this.cameraManager?.getActualFPS() || 0;
  }

  /**
   * 是否正在运行
   */
  public get active(): boolean {
    return this.isActive;
  }

  /**
   * 是否支持摄像头
   */
  public static isSupported(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }

  /**
   * 检查权限状态
   */
  public static async checkPermissions(): Promise<PermissionState> {
    try {
      const result = await navigator.permissions.query({ name: 'camera' as PermissionName });
      return result.state;
    } catch (error) {
      Debug.warn('CameraInputNode', '无法检查摄像头权限', error);
      return 'prompt';
    }
  }

  /**
   * 销毁节点
   */
  public destroy(): void {
    if (this.cameraManager) {
      this.cameraManager.destroy();
      this.cameraManager = null;
    }

    this.isActive = false;
    this.lastFrame = null;
    this.frameCount = 0;
    this.availableDevices = [];

    super.destroy();
  }
}
