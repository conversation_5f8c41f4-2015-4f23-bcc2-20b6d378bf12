/**
 * 内存分析器
 * 用于跟踪和分析内存使用情况
 */
import { EventEmitter } from 'events';
import { Debug } from './Debug';

/**
 * 资源类型枚举
 */
export enum ResourceType {
  TEXTURE = 'texture',
  GEOMETRY = 'geometry',
  MATERIAL = 'material',
  SHADER = 'shader',
  AUDIO = 'audio',
  MODEL = 'model',
  ANIMATION = 'animation',
  OTHER = 'other'
}

/**
 * 资源信息接口
 */
export interface ResourceInfo {
  /** 资源ID */
  id: string;
  /** 资源名称 */
  name: string;
  /** 资源类型 */
  type: ResourceType;
  /** 资源大小（字节） */
  size: number;
  /** 引用计数 */
  refCount: number;
  /** 创建时间 */
  createdAt: number;
  /** 最后访问时间 */
  lastAccessTime: number;
  /** 是否已释放 */
  disposed: boolean;
  /** 所有者/创建者 */
  owner?: string;
  /** 额外元数据 */
  metadata?: Record<string, any>;
}

/**
 * 内存快照接口
 */
export interface MemorySnapshot {
  /** 快照ID */
  id: string;
  /** 快照时间 */
  timestamp: number;
  /** 总内存使用量（字节） */
  totalMemory: number;
  /** JS堆内存使用量（字节） */
  jsHeapMemory: number;
  /** 资源内存使用量（字节） */
  resourceMemory: number;
  /** 按类型分类的内存使用量 */
  memoryByType: Record<ResourceType, number>;
  /** 资源列表 */
  resources: ResourceInfo[];
}

/**
 * 内存分析器配置接口
 */
export interface MemoryAnalyzerConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用自动内存快照 */
  enableAutoSnapshot?: boolean;
  /** 自动快照间隔（毫秒） */
  autoSnapshotInterval?: number;
  /** 是否启用内存泄漏检测 */
  enableLeakDetection?: boolean;
  /** 泄漏检测阈值（毫秒） */
  leakDetectionThreshold?: number;
  /** 是否启用调试输出 */
  debug?: boolean;
  /** 是否收集详细资源信息 */
  collectDetailedInfo?: boolean;
  /** 是否启用警告 */
  enableWarnings?: boolean;
  /** 是否启用内存监控 */
  enableMemoryMonitoring?: boolean;
  /** 内存监控间隔（毫秒） */
  memoryMonitorInterval?: number;
  /** 内存警告阈值（字节） */
  memoryWarningThreshold?: number;
  /** 内存严重警告阈值（字节） */
  memoryCriticalThreshold?: number;
}

/**
 * 内存分析器类
 */
export class MemoryAnalyzer {
  private static instance: MemoryAnalyzer;
  
  /** 配置 */
  private config: MemoryAnalyzerConfig = {
    enabled: false,
    enableAutoSnapshot: false,
    autoSnapshotInterval: 60000, // 1分钟
    enableLeakDetection: true,
    leakDetectionThreshold: 300000, // 5分钟
    debug: false,
    collectDetailedInfo: true,
    enableWarnings: true,
    enableMemoryMonitoring: true,
    memoryMonitorInterval: 10000, // 10秒
    memoryWarningThreshold: 256 * 1024 * 1024, // 256MB
    memoryCriticalThreshold: 512 * 1024 * 1024  // 512MB
  };
  
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  
  /** 资源映射 */
  private resources: Map<string, ResourceInfo> = new Map();
  
  /** 内存快照列表 */
  private snapshots: MemorySnapshot[] = [];
  
  /** 自动快照定时器ID */
  private autoSnapshotTimerId: number | null = null;
  
  /** 内存泄漏检测定时器ID */
  private leakDetectionTimerId: number | null = null;
  
  /** 是否正在运行 */
  private running: boolean = false;

  /** 内存监控定时器ID */
  private memoryMonitorTimerId: number | null = null;

  /** 内存警报阈值 */
  private memoryThresholds = {
    warning: 256 * 1024 * 1024, // 256MB
    critical: 512 * 1024 * 1024  // 512MB
  };
  
  /**
   * 获取单例实例
   */
  public static getInstance(): MemoryAnalyzer {
    if (!MemoryAnalyzer.instance) {
      MemoryAnalyzer.instance = new MemoryAnalyzer();
    }
    return MemoryAnalyzer.instance;
  }
  
  /**
   * 私有构造函数
   */
  private constructor() {
    // 初始化
  }
  
  /**
   * 配置内存分析器
   * @param config 配置
   */
  public configure(config: MemoryAnalyzerConfig): void {
    this.config = {
      ...this.config,
      ...config
    };

    // 更新内存阈值
    if (config.memoryWarningThreshold !== undefined || config.memoryCriticalThreshold !== undefined) {
      this.memoryThresholds = {
        warning: config.memoryWarningThreshold || this.memoryThresholds.warning,
        critical: config.memoryCriticalThreshold || this.memoryThresholds.critical
      };
    }

    if (this.config.debug) {
      Debug.log('内存分析器', '配置已更新', this.config);
    }

    // 如果已启动，则应用新配置
    if (this.running) {
      this.stop();
      this.start();
    }
  }
  
  /**
   * 启动内存分析器
   */
  public start(): void {
    if (!this.config.enabled || this.running) {
      return;
    }
    
    this.running = true;
    
    // 启动自动快照
    if (this.config.enableAutoSnapshot) {
      this.startAutoSnapshot();
    }
    
    // 启动内存泄漏检测
    if (this.config.enableLeakDetection) {
      this.startLeakDetection();
    }

    // 启动内存监控
    if (this.config.enableMemoryMonitoring) {
      this.startMemoryMonitoring();
    }

    if (this.config.debug) {
      Debug.log('内存分析器', '已启动');
    }

    this.eventEmitter.emit('start');
  }
  
  /**
   * 停止内存分析器
   */
  public stop(): void {
    if (!this.running) {
      return;
    }
    
    this.running = false;
    
    // 停止自动快照
    if (this.autoSnapshotTimerId !== null) {
      clearInterval(this.autoSnapshotTimerId);
      this.autoSnapshotTimerId = null;
    }
    
    // 停止内存泄漏检测
    if (this.leakDetectionTimerId !== null) {
      clearInterval(this.leakDetectionTimerId);
      this.leakDetectionTimerId = null;
    }

    // 停止内存监控
    if (this.memoryMonitorTimerId !== null) {
      clearInterval(this.memoryMonitorTimerId);
      this.memoryMonitorTimerId = null;
    }

    if (this.config.debug) {
      Debug.log('内存分析器', '已停止');
    }

    this.eventEmitter.emit('stop');
  }
  
  /**
   * 启动自动快照
   */
  private startAutoSnapshot(): void {
    if (this.autoSnapshotTimerId !== null) {
      clearInterval(this.autoSnapshotTimerId);
    }
    
    this.autoSnapshotTimerId = window.setInterval(() => {
      this.takeSnapshot();
    }, this.config.autoSnapshotInterval);
    
    if (this.config.debug) {
      Debug.log('内存分析器', '自动快照已启动');
    }
  }
  
  /**
   * 启动内存泄漏检测
   */
  private startLeakDetection(): void {
    if (this.leakDetectionTimerId !== null) {
      clearInterval(this.leakDetectionTimerId);
    }

    this.leakDetectionTimerId = window.setInterval(() => {
      this.detectLeaks();
    }, 30000); // 每30秒检测一次

    if (this.config.debug) {
      Debug.log('内存分析器', '内存泄漏检测已启动');
    }
  }

  /**
   * 启动内存监控
   */
  private startMemoryMonitoring(): void {
    if (this.memoryMonitorTimerId !== null) {
      clearInterval(this.memoryMonitorTimerId);
    }

    this.memoryMonitorTimerId = window.setInterval(() => {
      this.monitorMemoryUsage();
    }, this.config.memoryMonitorInterval || 10000);

    if (this.config.debug) {
      Debug.log('内存分析器', '内存监控已启动');
    }
  }

  /**
   * 监控内存使用情况
   */
  private monitorMemoryUsage(): void {
    const memoryUsage = this.getMemoryUsage();
    const totalMemory = memoryUsage.total;

    // 检查内存警告阈值
    if (totalMemory >= this.memoryThresholds.critical) {
      this.eventEmitter.emit('memoryAlert', {
        level: 'critical',
        message: '内存使用量达到严重警告级别',
        currentMemory: totalMemory,
        threshold: this.memoryThresholds.critical,
        usage: memoryUsage
      });

      if (this.config.enableWarnings) {
        Debug.error('内存分析器', `严重警告: 内存使用量过高 (${this.formatBytes(totalMemory)})`);
      }
    } else if (totalMemory >= this.memoryThresholds.warning) {
      this.eventEmitter.emit('memoryAlert', {
        level: 'warning',
        message: '内存使用量达到警告级别',
        currentMemory: totalMemory,
        threshold: this.memoryThresholds.warning,
        usage: memoryUsage
      });

      if (this.config.enableWarnings) {
        Debug.warn('内存分析器', `警告: 内存使用量较高 (${this.formatBytes(totalMemory)})`);
      }
    }

    // 发出内存使用情况更新事件
    this.eventEmitter.emit('memoryUsageUpdate', memoryUsage);
  }
  
  /**
   * 注册资源
   * @param resource 资源信息
   */
  public registerResource(resource: Omit<ResourceInfo, 'createdAt' | 'lastAccessTime' | 'disposed'>): void {
    if (!this.running) {
      return;
    }
    
    const now = Date.now();
    const resourceInfo: ResourceInfo = {
      ...resource,
      createdAt: now,
      lastAccessTime: now,
      disposed: false
    };
    
    this.resources.set(resource.id, resourceInfo);
    
    if (this.config.debug) {
      Debug.log('内存分析器', `资源已注册: ${resource.id} (${resource.type}, ${this.formatBytes(resource.size)})`);
    }
    
    this.eventEmitter.emit('resourceRegistered', resourceInfo);
  }
  
  /**
   * 更新资源
   * @param id 资源ID
   * @param updates 更新内容
   */
  public updateResource(id: string, updates: Partial<ResourceInfo>): void {
    if (!this.running || !this.resources.has(id)) {
      return;
    }
    
    const resource = this.resources.get(id)!;
    const updatedResource = {
      ...resource,
      ...updates,
      lastAccessTime: Date.now()
    };
    
    this.resources.set(id, updatedResource);
    
    if (this.config.debug) {
      Debug.log('内存分析器', `资源已更新: ${id}`);
    }
    
    this.eventEmitter.emit('resourceUpdated', updatedResource);
  }
  
  /**
   * 释放资源
   * @param id 资源ID
   */
  public disposeResource(id: string): void {
    if (!this.running || !this.resources.has(id)) {
      return;
    }
    
    const resource = this.resources.get(id)!;
    resource.disposed = true;
    resource.lastAccessTime = Date.now();
    
    if (this.config.debug) {
      Debug.log('内存分析器', `资源已释放: ${id}`);
    }
    
    this.eventEmitter.emit('resourceDisposed', resource);
  }
  
  /**
   * 获取资源
   * @param id 资源ID
   */
  public getResource(id: string): ResourceInfo | undefined {
    const resource = this.resources.get(id);
    
    if (resource) {
      resource.lastAccessTime = Date.now();
    }
    
    return resource;
  }
  
  /**
   * 获取所有资源
   */
  public getAllResources(): ResourceInfo[] {
    return Array.from(this.resources.values());
  }
  
  /**
   * 获取活跃资源（未释放的）
   */
  public getActiveResources(): ResourceInfo[] {
    return Array.from(this.resources.values()).filter(resource => !resource.disposed);
  }
  
  /**
   * 获取按类型分组的资源
   */
  public getResourcesByType(): Record<ResourceType, ResourceInfo[]> {
    const result: Record<ResourceType, ResourceInfo[]> = {} as any;
    
    // 初始化所有类型的空数组
    Object.values(ResourceType).forEach(type => {
      result[type] = [];
    });
    
    // 按类型分组资源
    this.getActiveResources().forEach(resource => {
      result[resource.type].push(resource);
    });
    
    return result;
  }
  
  /**
   * 获取按类型分组的内存使用量
   */
  public getMemoryByType(): Record<ResourceType, number> {
    const result: Record<ResourceType, number> = {} as any;
    
    // 初始化所有类型的内存使用量为0
    Object.values(ResourceType).forEach(type => {
      result[type] = 0;
    });
    
    // 计算每种类型的内存使用量
    this.getActiveResources().forEach(resource => {
      result[resource.type] += resource.size;
    });
    
    return result;
  }
  
  /**
   * 获取总内存使用量
   */
  public getTotalMemory(): number {
    return this.getActiveResources().reduce((total, resource) => total + resource.size, 0);
  }
  
  /**
   * 获取JS堆内存使用量
   */
  public getJsHeapMemory(): number {
    if (typeof window !== 'undefined' && window.performance && (window.performance as any).memory) {
      return (window.performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }
  
  /**
   * 获取当前内存使用情况
   */
  public getMemoryUsage(): {
    total: number;
    jsHeap: number;
    resources: number;
    byType: Record<ResourceType, number>;
  } {
    const jsHeap = this.getJsHeapMemory();
    const resources = this.getTotalMemory();
    const byType = this.getMemoryByType();
    
    return {
      total: jsHeap,
      jsHeap,
      resources,
      byType
    };
  }
  
  /**
   * 拍摄内存快照
   */
  public takeSnapshot(): MemorySnapshot {
    const now = Date.now();
    const jsHeapMemory = this.getJsHeapMemory();
    const resourceMemory = this.getTotalMemory();
    const memoryByType = this.getMemoryByType();
    const resources = this.config.collectDetailedInfo ? this.getActiveResources() : [];
    
    const snapshot: MemorySnapshot = {
      id: `snapshot-${now}`,
      timestamp: now,
      totalMemory: jsHeapMemory,
      jsHeapMemory,
      resourceMemory,
      memoryByType,
      resources
    };
    
    this.snapshots.push(snapshot);
    
    // 限制快照数量，保留最新的100个
    if (this.snapshots.length > 100) {
      this.snapshots.shift();
    }
    
    if (this.config.debug) {
      Debug.log('内存分析器', `已创建内存快照: ${snapshot.id}`);
    }
    
    this.eventEmitter.emit('snapshotTaken', snapshot);
    
    return snapshot;
  }
  
  /**
   * 获取所有快照
   */
  public getSnapshots(): MemorySnapshot[] {
    return [...this.snapshots];
  }
  
  /**
   * 获取最新快照
   */
  public getLatestSnapshot(): MemorySnapshot | undefined {
    if (this.snapshots.length === 0) {
      return undefined;
    }
    return this.snapshots[this.snapshots.length - 1];
  }
  
  /**
   * 比较两个快照
   * @param snapshotId1 快照1 ID
   * @param snapshotId2 快照2 ID
   */
  public compareSnapshots(snapshotId1: string, snapshotId2: string): {
    totalDiff: number;
    jsHeapDiff: number;
    resourceDiff: number;
    typeDiffs: Record<ResourceType, number>;
    newResources: ResourceInfo[];
    disposedResources: ResourceInfo[];
  } {
    const snapshot1 = this.snapshots.find(s => s.id === snapshotId1);
    const snapshot2 = this.snapshots.find(s => s.id === snapshotId2);
    
    if (!snapshot1 || !snapshot2) {
      throw new Error('快照不存在');
    }
    
    // 计算差异
    const totalDiff = snapshot2.totalMemory - snapshot1.totalMemory;
    const jsHeapDiff = snapshot2.jsHeapMemory - snapshot1.jsHeapMemory;
    const resourceDiff = snapshot2.resourceMemory - snapshot1.resourceMemory;
    
    // 计算各类型内存差异
    const typeDiffs: Record<ResourceType, number> = {} as any;
    Object.values(ResourceType).forEach(type => {
      typeDiffs[type] = snapshot2.memoryByType[type] - snapshot1.memoryByType[type];
    });
    
    // 找出新增和释放的资源
    const resourceIds1 = new Set(snapshot1.resources.map(r => r.id));
    const resourceIds2 = new Set(snapshot2.resources.map(r => r.id));
    
    const newResources = snapshot2.resources.filter(r => !resourceIds1.has(r.id));
    const disposedResources = snapshot1.resources.filter(r => !resourceIds2.has(r.id));
    
    return {
      totalDiff,
      jsHeapDiff,
      resourceDiff,
      typeDiffs,
      newResources,
      disposedResources
    };
  }
  
  /**
   * 检测内存泄漏
   */
  public detectLeaks(): {
    potentialLeaks: ResourceInfo[];
    totalLeakedMemory: number;
  } {
    if (!this.running) {
      return { potentialLeaks: [], totalLeakedMemory: 0 };
    }
    
    const now = Date.now();
    const threshold = this.config.leakDetectionThreshold || 300000; // 默认5分钟
    
    // 查找长时间未访问但未释放的资源
    const potentialLeaks = this.getActiveResources().filter(resource => {
      return now - resource.lastAccessTime > threshold;
    });
    
    // 计算泄漏的总内存
    const totalLeakedMemory = potentialLeaks.reduce((total, resource) => total + resource.size, 0);
    
    if (potentialLeaks.length > 0 && this.config.enableWarnings) {
      Debug.warn('内存分析器', `检测到潜在内存泄漏: ${potentialLeaks.length}个资源, ${this.formatBytes(totalLeakedMemory)}`);
      
      if (this.config.debug) {
        potentialLeaks.forEach(leak => {
          Debug.log('内存分析器', `  - ${leak.id} (${leak.type}, ${this.formatBytes(leak.size)}, 最后访问: ${new Date(leak.lastAccessTime).toLocaleString()})`);
        });
      }
    }
    
    this.eventEmitter.emit('leaksDetected', { potentialLeaks, totalLeakedMemory });
    
    return { potentialLeaks, totalLeakedMemory };
  }
  
  /**
   * 清理资源
   */
  public clearResources(): void {
    this.resources.clear();
    
    if (this.config.debug) {
      Debug.log('内存分析器', '所有资源记录已清除');
    }
    
    this.eventEmitter.emit('resourcesCleared');
  }
  
  /**
   * 清理快照
   */
  public clearSnapshots(): void {
    this.snapshots = [];
    
    if (this.config.debug) {
      Debug.log('内存分析器', '所有快照已清除');
    }
    
    this.eventEmitter.emit('snapshotsCleared');
  }
  
  /**
   * 重置内存分析器
   */
  public reset(): void {
    this.clearResources();
    this.clearSnapshots();

    if (this.config.debug) {
      Debug.log('内存分析器', '已重置');
    }

    this.eventEmitter.emit('reset');
  }

  /**
   * 获取内存优化建议
   */
  public getOptimizationSuggestions(): Array<{
    type: 'warning' | 'suggestion' | 'critical';
    category: string;
    message: string;
    details?: string;
    action?: string;
  }> {
    const suggestions: Array<{
      type: 'warning' | 'suggestion' | 'critical';
      category: string;
      message: string;
      details?: string;
      action?: string;
    }> = [];

    const activeResources = this.getActiveResources();
    const totalMemory = this.getTotalMemory();
    const leakDetection = this.detectLeaks();

    // 检查内存泄漏
    if (leakDetection.potentialLeaks.length > 0) {
      suggestions.push({
        type: 'critical',
        category: '内存泄漏',
        message: `检测到 ${leakDetection.potentialLeaks.length} 个潜在内存泄漏`,
        details: `泄漏内存: ${this.formatBytes(leakDetection.totalLeakedMemory)}`,
        action: '检查长时间未访问的资源，确保正确释放'
      });
    }

    // 检查大型纹理
    const largeTextures = activeResources.filter(r =>
      r.type === ResourceType.TEXTURE && r.size > 4 * 1024 * 1024 // 4MB
    );
    if (largeTextures.length > 0) {
      suggestions.push({
        type: 'warning',
        category: '纹理优化',
        message: `发现 ${largeTextures.length} 个大型纹理`,
        details: `总大小: ${this.formatBytes(largeTextures.reduce((sum, t) => sum + t.size, 0))}`,
        action: '考虑压缩纹理或使用更小的分辨率'
      });
    }

    // 检查重复资源
    const resourcesByName = new Map<string, ResourceInfo[]>();
    activeResources.forEach(resource => {
      if (!resourcesByName.has(resource.name)) {
        resourcesByName.set(resource.name, []);
      }
      resourcesByName.get(resource.name)!.push(resource);
    });

    const duplicates = Array.from(resourcesByName.entries()).filter(([_, resources]) => resources.length > 1);
    if (duplicates.length > 0) {
      suggestions.push({
        type: 'suggestion',
        category: '资源重用',
        message: `发现 ${duplicates.length} 组重复资源`,
        details: `可节省内存: ${this.formatBytes(duplicates.reduce((sum, [_, resources]) =>
          sum + resources.slice(1).reduce((s, r) => s + r.size, 0), 0))}`,
        action: '考虑重用相同的资源实例'
      });
    }

    // 检查几何体复杂度
    const complexGeometries = activeResources.filter(r =>
      r.type === ResourceType.GEOMETRY && r.size > 1024 * 1024 // 1MB
    );
    if (complexGeometries.length > 0) {
      suggestions.push({
        type: 'suggestion',
        category: '几何体优化',
        message: `发现 ${complexGeometries.length} 个复杂几何体`,
        details: `总大小: ${this.formatBytes(complexGeometries.reduce((sum, g) => sum + g.size, 0))}`,
        action: '考虑使用LOD或简化几何体'
      });
    }

    // 检查总内存使用量
    if (totalMemory > 512 * 1024 * 1024) { // 512MB
      suggestions.push({
        type: 'critical',
        category: '内存使用',
        message: '总内存使用量过高',
        details: `当前使用: ${this.formatBytes(totalMemory)}`,
        action: '考虑释放不必要的资源或实施资源管理策略'
      });
    } else if (totalMemory > 256 * 1024 * 1024) { // 256MB
      suggestions.push({
        type: 'warning',
        category: '内存使用',
        message: '内存使用量较高',
        details: `当前使用: ${this.formatBytes(totalMemory)}`,
        action: '监控内存使用情况，考虑优化'
      });
    }

    // 检查未使用的资源
    const unusedResources = activeResources.filter(r =>
      r.refCount === 0 && Date.now() - r.lastAccessTime > 60000 // 1分钟未访问
    );
    if (unusedResources.length > 0) {
      suggestions.push({
        type: 'suggestion',
        category: '资源清理',
        message: `发现 ${unusedResources.length} 个未使用的资源`,
        details: `可释放内存: ${this.formatBytes(unusedResources.reduce((sum, r) => sum + r.size, 0))}`,
        action: '考虑释放长时间未使用的资源'
      });
    }

    return suggestions;
  }

  /**
   * 自动清理未使用的资源
   */
  public autoCleanup(): {
    cleanedResources: number;
    freedMemory: number;
    details: Array<{ id: string; name: string; type: string; size: number }>;
  } {
    const activeResources = this.getActiveResources();
    const now = Date.now();
    const cleanupThreshold = 5 * 60 * 1000; // 5分钟

    // 找出需要清理的资源
    const resourcesToClean = activeResources.filter(resource => {
      // 引用计数为0且长时间未访问
      return resource.refCount === 0 &&
             (now - resource.lastAccessTime) > cleanupThreshold;
    });

    const details = resourcesToClean.map(resource => ({
      id: resource.id,
      name: resource.name,
      type: resource.type,
      size: resource.size
    }));

    const freedMemory = resourcesToClean.reduce((sum, resource) => sum + resource.size, 0);

    // 标记资源为已释放
    resourcesToClean.forEach(resource => {
      this.disposeResource(resource.id);
    });

    if (this.config.debug && resourcesToClean.length > 0) {
      Debug.log('内存分析器', `自动清理完成: 清理了 ${resourcesToClean.length} 个资源, 释放了 ${this.formatBytes(freedMemory)} 内存`);
    }

    this.eventEmitter.emit('autoCleanupCompleted', {
      cleanedResources: resourcesToClean.length,
      freedMemory,
      details
    });

    return {
      cleanedResources: resourcesToClean.length,
      freedMemory,
      details
    };
  }

  /**
   * 强制垃圾回收（如果可用）
   */
  public forceGarbageCollection(): boolean {
    if (typeof window !== 'undefined' && window.gc) {
      try {
        window.gc();
        if (this.config.debug) {
          Debug.log('内存分析器', '已执行强制垃圾回收');
        }
        this.eventEmitter.emit('garbageCollectionForced');
        return true;
      } catch (error) {
        if (this.config.debug) {
          Debug.warn('内存分析器', '强制垃圾回收失败:', error);
        }
        return false;
      }
    }
    return false;
  }

  /**
   * 导出内存分析数据
   */
  public exportData(): {
    config: MemoryAnalyzerConfig;
    resources: ResourceInfo[];
    snapshots: MemorySnapshot[];
    currentUsage: {
      total: number;
      jsHeap: number;
      resources: number;
      byType: Record<ResourceType, number>;
    };
    report: {
      summary: {
        totalResources: number;
        activeResources: number;
        disposedResources: number;
        totalMemory: string;
        jsHeapMemory: string;
        resourceMemory: string;
        timestamp: string;
      };
      byType: Record<string, {
        count: number;
        memory: string;
        percentage: number;
      }>;
      topConsumers: Array<{
        id: string;
        name: string;
        type: string;
        size: string;
        refCount: number;
        age: string;
      }>;
      potentialLeaks: Array<{
        id: string;
        name: string;
        type: string;
        size: string;
        lastAccess: string;
        age: string;
      }>;
      trends: {
        memoryGrowth: number;
        resourceGrowth: number;
        averageResourceSize: string;
      };
    };
    suggestions: Array<{
      type: 'warning' | 'suggestion' | 'critical';
      category: string;
      message: string;
      details?: string;
      action?: string;
    }>;
    exportTime: string;
  } {
    return {
      config: { ...this.config },
      resources: this.getAllResources(),
      snapshots: this.getSnapshots(),
      currentUsage: this.getMemoryUsage(),
      report: this.generateReport(),
      suggestions: this.getOptimizationSuggestions(),
      exportTime: new Date().toISOString()
    };
  }

  /**
   * 导入内存分析数据
   */
  public importData(data: {
    resources?: ResourceInfo[];
    snapshots?: MemorySnapshot[];
  }): void {
    if (data.resources) {
      this.resources.clear();
      data.resources.forEach(resource => {
        this.resources.set(resource.id, resource);
      });
    }

    if (data.snapshots) {
      this.snapshots = [...data.snapshots];
    }

    if (this.config.debug) {
      Debug.log('内存分析器', '数据导入完成');
    }

    this.eventEmitter.emit('dataImported', data);
  }

  /**
   * 获取内存分析器状态
   */
  public getStatus(): {
    running: boolean;
    config: MemoryAnalyzerConfig;
    resourceCount: number;
    snapshotCount: number;
    totalMemory: string;
    lastSnapshot?: string;
  } {
    const latestSnapshot = this.getLatestSnapshot();

    return {
      running: this.running,
      config: { ...this.config },
      resourceCount: this.resources.size,
      snapshotCount: this.snapshots.length,
      totalMemory: this.formatBytes(this.getTotalMemory()),
      lastSnapshot: latestSnapshot ? new Date(latestSnapshot.timestamp).toLocaleString() : undefined
    };
  }
  
  /**
   * 监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }
  
  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
  
  /**
   * 生成内存分析报告
   */
  public generateReport(): {
    summary: {
      totalResources: number;
      activeResources: number;
      disposedResources: number;
      totalMemory: string;
      jsHeapMemory: string;
      resourceMemory: string;
      timestamp: string;
    };
    byType: Record<string, {
      count: number;
      memory: string;
      percentage: number;
    }>;
    topConsumers: Array<{
      id: string;
      name: string;
      type: string;
      size: string;
      refCount: number;
      age: string;
    }>;
    potentialLeaks: Array<{
      id: string;
      name: string;
      type: string;
      size: string;
      lastAccess: string;
      age: string;
    }>;
    trends: {
      memoryGrowth: number;
      resourceGrowth: number;
      averageResourceSize: string;
    };
  } {
    const allResources = this.getAllResources();
    const activeResources = this.getActiveResources();
    const disposedResources = allResources.filter(r => r.disposed);
    const memoryByType = this.getMemoryByType();
    const totalResourceMemory = this.getTotalMemory();
    const jsHeapMemory = this.getJsHeapMemory();
    const leakDetection = this.detectLeaks();

    // 生成摘要
    const summary = {
      totalResources: allResources.length,
      activeResources: activeResources.length,
      disposedResources: disposedResources.length,
      totalMemory: this.formatBytes(jsHeapMemory),
      jsHeapMemory: this.formatBytes(jsHeapMemory),
      resourceMemory: this.formatBytes(totalResourceMemory),
      timestamp: new Date().toISOString()
    };

    // 按类型统计
    const byType: Record<string, { count: number; memory: string; percentage: number }> = {};
    Object.entries(memoryByType).forEach(([type, memory]) => {
      const resourcesOfType = activeResources.filter(r => r.type === type);
      byType[type] = {
        count: resourcesOfType.length,
        memory: this.formatBytes(memory),
        percentage: totalResourceMemory > 0 ? (memory / totalResourceMemory) * 100 : 0
      };
    });

    // 内存消耗最大的资源（前10个）
    const topConsumers = activeResources
      .sort((a, b) => b.size - a.size)
      .slice(0, 10)
      .map(resource => ({
        id: resource.id,
        name: resource.name,
        type: resource.type,
        size: this.formatBytes(resource.size),
        refCount: resource.refCount,
        age: this.formatDuration(Date.now() - resource.createdAt)
      }));

    // 潜在内存泄漏
    const potentialLeaks = leakDetection.potentialLeaks.map(leak => ({
      id: leak.id,
      name: leak.name,
      type: leak.type,
      size: this.formatBytes(leak.size),
      lastAccess: new Date(leak.lastAccessTime).toLocaleString(),
      age: this.formatDuration(Date.now() - leak.createdAt)
    }));

    // 趋势分析
    const trends = this.analyzeTrends();

    return {
      summary,
      byType,
      topConsumers,
      potentialLeaks,
      trends
    };
  }

  /**
   * 分析内存趋势
   */
  private analyzeTrends(): {
    memoryGrowth: number;
    resourceGrowth: number;
    averageResourceSize: string;
  } {
    const snapshots = this.getSnapshots();
    let memoryGrowth = 0;
    let resourceGrowth = 0;

    if (snapshots.length >= 2) {
      const firstSnapshot = snapshots[0];
      const lastSnapshot = snapshots[snapshots.length - 1];

      memoryGrowth = lastSnapshot.totalMemory - firstSnapshot.totalMemory;
      resourceGrowth = lastSnapshot.resources.length - firstSnapshot.resources.length;
    }

    const activeResources = this.getActiveResources();
    const averageSize = activeResources.length > 0
      ? activeResources.reduce((sum, r) => sum + r.size, 0) / activeResources.length
      : 0;

    return {
      memoryGrowth,
      resourceGrowth,
      averageResourceSize: this.formatBytes(averageSize)
    };
  }

  /**
   * 打印内存报告到控制台
   */
  public printReport(): void {
    const report = this.generateReport();

    console.group('📊 内存分析报告');

    // 摘要信息
    console.group('📈 摘要');
    console.log(`总资源数: ${report.summary.totalResources}`);
    console.log(`活跃资源: ${report.summary.activeResources}`);
    console.log(`已释放资源: ${report.summary.disposedResources}`);
    console.log(`总内存: ${report.summary.totalMemory}`);
    console.log(`JS堆内存: ${report.summary.jsHeapMemory}`);
    console.log(`资源内存: ${report.summary.resourceMemory}`);
    console.log(`生成时间: ${report.summary.timestamp}`);
    console.groupEnd();

    // 按类型统计
    console.group('📋 按类型统计');
    Object.entries(report.byType).forEach(([type, stats]) => {
      if (stats.count > 0) {
        console.log(`${type}: ${stats.count}个 (${stats.memory}, ${stats.percentage.toFixed(1)}%)`);
      }
    });
    console.groupEnd();

    // 内存消耗最大的资源
    if (report.topConsumers.length > 0) {
      console.group('🔥 内存消耗最大的资源');
      console.table(report.topConsumers);
      console.groupEnd();
    }

    // 潜在内存泄漏
    if (report.potentialLeaks.length > 0) {
      console.group('⚠️ 潜在内存泄漏');
      console.table(report.potentialLeaks);
      console.groupEnd();
    }

    // 趋势分析
    console.group('📊 趋势分析');
    console.log(`内存增长: ${this.formatBytes(report.trends.memoryGrowth)}`);
    console.log(`资源增长: ${report.trends.resourceGrowth}`);
    console.log(`平均资源大小: ${report.trends.averageResourceSize}`);
    console.groupEnd();

    console.groupEnd();
  }

  /**
   * 格式化持续时间
   * @param ms 毫秒数
   * @returns 格式化后的字符串
   */
  private formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天 ${hours % 24}小时`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟 ${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 格式化字节数
   * @param bytes 字节数
   * @returns 格式化后的字符串
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
