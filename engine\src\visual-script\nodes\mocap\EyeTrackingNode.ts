/**
 * 眼部追踪节点
 * 基于面部关键点进行眼部追踪和视线方向检测
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Vector2 } from 'three';

/**
 * 眼部关键点数据接口
 */
export interface EyeLandmarkData {
  x: number;
  y: number;
  z?: number;
}

/**
 * 眼部状态枚举
 */
export enum EyeState {
  OPEN = 'open',
  CLOSED = 'closed',
  BLINKING = 'blinking',
  SQUINTING = 'squinting',
  WIDE_OPEN = 'wide_open'
}

/**
 * 视线方向数据
 */
export interface GazeDirection {
  direction: Vector3;
  pitch: number;  // 俯仰角
  yaw: number;    // 偏航角
  confidence: number;
}

/**
 * 眼部追踪结果
 */
export interface EyeTrackingResults {
  leftEye: {
    landmarks: EyeLandmarkData[];
    state: EyeState;
    openness: number;
    gazeDirection: GazeDirection;
    pupilPosition: Vector2;
    blinkDetected: boolean;
  };
  rightEye: {
    landmarks: EyeLandmarkData[];
    state: EyeState;
    openness: number;
    gazeDirection: GazeDirection;
    pupilPosition: Vector2;
    blinkDetected: boolean;
  };
  combinedGaze: GazeDirection;
  blinkRate: number;
  attentionLevel: number;
  confidence: number;
  processingTime: number;
  timestamp: number;
}

/**
 * 高级眼部分析器
 */
class AdvancedEyeAnalyzer {
  private blinkHistory: boolean[] = [];
  private gazeHistory: GazeDirection[] = [];
  private maxHistorySize = 30; // 1秒历史（假设30fps）
  private blinkThreshold = 0.3;
  private gazeStabilityFactor = 0.7;

  /**
   * 眼部关键点索引（基于MediaPipe Face Mesh）
   */
  private static readonly EYE_LANDMARKS = {
    LEFT_EYE: {
      OUTER_CORNER: 33,
      INNER_CORNER: 133,
      TOP_LID: 159,
      BOTTOM_LID: 145,
      UPPER_LID: [157, 158, 159, 160, 161],
      LOWER_LID: [144, 145, 153, 154, 155],
      IRIS: [468, 469, 470, 471, 472]
    },
    RIGHT_EYE: {
      OUTER_CORNER: 362,
      INNER_CORNER: 263,
      TOP_LID: 386,
      BOTTOM_LID: 374,
      UPPER_LID: [384, 385, 386, 387, 388],
      LOWER_LID: [373, 374, 380, 381, 382],
      IRIS: [473, 474, 475, 476, 477]
    }
  };

  /**
   * 分析眼部状态
   */
  analyzeEyes(faceLandmarks: any[]): EyeTrackingResults {
    const leftEyeData = this.analyzeEye(faceLandmarks, 'left');
    const rightEyeData = this.analyzeEye(faceLandmarks, 'right');
    
    // 计算组合视线方向
    const combinedGaze = this.calculateCombinedGaze(leftEyeData.gazeDirection, rightEyeData.gazeDirection);
    
    // 更新历史记录
    this.updateBlinkHistory(leftEyeData.blinkDetected || rightEyeData.blinkDetected);
    this.updateGazeHistory(combinedGaze);
    
    // 计算眨眼率
    const blinkRate = this.calculateBlinkRate();
    
    // 计算注意力水平
    const attentionLevel = this.calculateAttentionLevel(leftEyeData, rightEyeData, combinedGaze);
    
    return {
      leftEye: leftEyeData,
      rightEye: rightEyeData,
      combinedGaze,
      blinkRate,
      attentionLevel,
      confidence: Math.min(leftEyeData.gazeDirection.confidence, rightEyeData.gazeDirection.confidence),
      processingTime: 0,
      timestamp: Date.now()
    };
  }

  /**
   * 分析单个眼部
   */
  private analyzeEye(faceLandmarks: any[], eye: 'left' | 'right'): any {
    const landmarks = eye === 'left' ? 
      AdvancedEyeAnalyzer.EYE_LANDMARKS.LEFT_EYE : 
      AdvancedEyeAnalyzer.EYE_LANDMARKS.RIGHT_EYE;

    // 提取眼部关键点
    const eyeLandmarks = this.extractEyeLandmarks(faceLandmarks, landmarks);
    
    // 计算眼部开合度
    const openness = this.calculateEyeOpenness(eyeLandmarks, landmarks);
    
    // 确定眼部状态
    const state = this.determineEyeState(openness);
    
    // 检测眨眼
    const blinkDetected = this.detectBlink(openness);
    
    // 计算瞳孔位置
    const pupilPosition = this.calculatePupilPosition(eyeLandmarks, landmarks);
    
    // 计算视线方向
    const gazeDirection = this.calculateGazeDirection(eyeLandmarks, landmarks, pupilPosition);

    return {
      landmarks: eyeLandmarks,
      state,
      openness,
      gazeDirection,
      pupilPosition,
      blinkDetected
    };
  }

  /**
   * 提取眼部关键点
   */
  private extractEyeLandmarks(faceLandmarks: any[], landmarks: any): EyeLandmarkData[] {
    const eyePoints: EyeLandmarkData[] = [];
    
    // 提取眼部轮廓点
    const allIndices = [
      landmarks.OUTER_CORNER,
      landmarks.INNER_CORNER,
      landmarks.TOP_LID,
      landmarks.BOTTOM_LID,
      ...landmarks.UPPER_LID,
      ...landmarks.LOWER_LID
    ];

    for (const index of allIndices) {
      if (faceLandmarks[index]) {
        eyePoints.push({
          x: faceLandmarks[index].x,
          y: faceLandmarks[index].y,
          z: faceLandmarks[index].z || 0
        });
      }
    }

    return eyePoints;
  }

  /**
   * 计算眼部开合度
   */
  private calculateEyeOpenness(eyeLandmarks: EyeLandmarkData[], landmarks: any): number {
    if (eyeLandmarks.length < 4) return 0;

    // 使用眼部上下眼睑的距离来计算开合度
    const topLid = eyeLandmarks[2]; // TOP_LID
    const bottomLid = eyeLandmarks[3]; // BOTTOM_LID
    const outerCorner = eyeLandmarks[0]; // OUTER_CORNER
    const innerCorner = eyeLandmarks[1]; // INNER_CORNER

    // 计算眼部高度和宽度
    const eyeHeight = Math.abs(topLid.y - bottomLid.y);
    const eyeWidth = Math.abs(outerCorner.x - innerCorner.x);

    // 眼部开合度 = 高度/宽度比例
    const openness = eyeHeight / eyeWidth;
    
    // 归一化到0-1范围
    return Math.max(0, Math.min(1, openness * 5)); // 调整系数
  }

  /**
   * 确定眼部状态
   */
  private determineEyeState(openness: number): EyeState {
    if (openness < 0.1) {
      return EyeState.CLOSED;
    } else if (openness < 0.3) {
      return EyeState.SQUINTING;
    } else if (openness > 0.8) {
      return EyeState.WIDE_OPEN;
    } else {
      return EyeState.OPEN;
    }
  }

  /**
   * 检测眨眼
   */
  private detectBlink(openness: number): boolean {
    return openness < this.blinkThreshold;
  }

  /**
   * 计算瞳孔位置
   */
  private calculatePupilPosition(eyeLandmarks: EyeLandmarkData[], landmarks: any): Vector2 {
    if (eyeLandmarks.length < 4) return new Vector2(0.5, 0.5);

    // 简化的瞳孔位置计算（眼部中心）
    const outerCorner = eyeLandmarks[0];
    const innerCorner = eyeLandmarks[1];
    const topLid = eyeLandmarks[2];
    const bottomLid = eyeLandmarks[3];

    const centerX = (outerCorner.x + innerCorner.x) / 2;
    const centerY = (topLid.y + bottomLid.y) / 2;

    // 归一化到眼部坐标系
    const eyeWidth = Math.abs(outerCorner.x - innerCorner.x);
    const eyeHeight = Math.abs(topLid.y - bottomLid.y);

    const normalizedX = (centerX - innerCorner.x) / eyeWidth;
    const normalizedY = (centerY - bottomLid.y) / eyeHeight;

    return new Vector2(normalizedX, normalizedY);
  }

  /**
   * 计算视线方向
   */
  private calculateGazeDirection(eyeLandmarks: EyeLandmarkData[], landmarks: any, pupilPosition: Vector2): GazeDirection {
    // 简化的视线方向计算
    const centerX = pupilPosition.x - 0.5; // 相对于眼部中心的偏移
    const centerY = pupilPosition.y - 0.5;

    // 转换为角度
    const yaw = centerX * 30; // 最大30度偏转
    const pitch = -centerY * 20; // 最大20度偏转

    // 计算3D方向向量
    const direction = new Vector3(
      Math.sin(yaw * Math.PI / 180),
      Math.sin(pitch * Math.PI / 180),
      Math.cos(yaw * Math.PI / 180) * Math.cos(pitch * Math.PI / 180)
    ).normalize();

    // 计算置信度（基于眼部开合度和关键点质量）
    const confidence = Math.min(1, eyeLandmarks.length / 10);

    return {
      direction,
      pitch,
      yaw,
      confidence
    };
  }

  /**
   * 计算组合视线方向
   */
  private calculateCombinedGaze(leftGaze: GazeDirection, rightGaze: GazeDirection): GazeDirection {
    // 加权平均两眼的视线方向
    const leftWeight = leftGaze.confidence;
    const rightWeight = rightGaze.confidence;
    const totalWeight = leftWeight + rightWeight;

    if (totalWeight === 0) {
      return {
        direction: new Vector3(0, 0, 1),
        pitch: 0,
        yaw: 0,
        confidence: 0
      };
    }

    const combinedDirection = new Vector3()
      .addScaledVector(leftGaze.direction, leftWeight / totalWeight)
      .addScaledVector(rightGaze.direction, rightWeight / totalWeight)
      .normalize();

    const combinedPitch = (leftGaze.pitch * leftWeight + rightGaze.pitch * rightWeight) / totalWeight;
    const combinedYaw = (leftGaze.yaw * leftWeight + rightGaze.yaw * rightWeight) / totalWeight;

    // 应用稳定性滤波
    const stabilizedGaze = this.applyGazeStabilization({
      direction: combinedDirection,
      pitch: combinedPitch,
      yaw: combinedYaw,
      confidence: Math.min(leftGaze.confidence, rightGaze.confidence)
    });

    return stabilizedGaze;
  }

  /**
   * 应用视线稳定化
   */
  private applyGazeStabilization(currentGaze: GazeDirection): GazeDirection {
    if (this.gazeHistory.length === 0) {
      return currentGaze;
    }

    const lastGaze = this.gazeHistory[this.gazeHistory.length - 1];
    
    // 平滑滤波
    const stabilizedPitch = lastGaze.pitch * this.gazeStabilityFactor + 
                           currentGaze.pitch * (1 - this.gazeStabilityFactor);
    const stabilizedYaw = lastGaze.yaw * this.gazeStabilityFactor + 
                         currentGaze.yaw * (1 - this.gazeStabilityFactor);

    const stabilizedDirection = new Vector3(
      Math.sin(stabilizedYaw * Math.PI / 180),
      Math.sin(stabilizedPitch * Math.PI / 180),
      Math.cos(stabilizedYaw * Math.PI / 180) * Math.cos(stabilizedPitch * Math.PI / 180)
    ).normalize();

    return {
      direction: stabilizedDirection,
      pitch: stabilizedPitch,
      yaw: stabilizedYaw,
      confidence: currentGaze.confidence
    };
  }

  /**
   * 更新眨眼历史
   */
  private updateBlinkHistory(blinkDetected: boolean): void {
    this.blinkHistory.push(blinkDetected);
    if (this.blinkHistory.length > this.maxHistorySize) {
      this.blinkHistory.shift();
    }
  }

  /**
   * 更新视线历史
   */
  private updateGazeHistory(gaze: GazeDirection): void {
    this.gazeHistory.push(gaze);
    if (this.gazeHistory.length > this.maxHistorySize) {
      this.gazeHistory.shift();
    }
  }

  /**
   * 计算眨眼率
   */
  private calculateBlinkRate(): number {
    if (this.blinkHistory.length < 2) return 0;

    // 计算眨眼次数（检测从false到true的转换）
    let blinkCount = 0;
    for (let i = 1; i < this.blinkHistory.length; i++) {
      if (this.blinkHistory[i] && !this.blinkHistory[i - 1]) {
        blinkCount++;
      }
    }

    // 转换为每分钟眨眼次数
    const timeSpan = this.blinkHistory.length / 30; // 假设30fps
    return (blinkCount / timeSpan) * 60;
  }

  /**
   * 计算注意力水平
   */
  private calculateAttentionLevel(leftEye: any, rightEye: any, combinedGaze: GazeDirection): number {
    // 基于多个因素计算注意力水平
    const eyeOpennessScore = (leftEye.openness + rightEye.openness) / 2;
    const gazeStabilityScore = this.calculateGazeStability();
    const blinkRateScore = this.calculateBlinkRateScore();

    // 加权平均
    const attentionLevel = (
      eyeOpennessScore * 0.4 +
      gazeStabilityScore * 0.4 +
      blinkRateScore * 0.2
    );

    return Math.max(0, Math.min(1, attentionLevel));
  }

  /**
   * 计算视线稳定性
   */
  private calculateGazeStability(): number {
    if (this.gazeHistory.length < 5) return 0.5;

    // 计算最近几帧的视线变化
    let totalVariation = 0;
    for (let i = 1; i < Math.min(5, this.gazeHistory.length); i++) {
      const current = this.gazeHistory[this.gazeHistory.length - i];
      const previous = this.gazeHistory[this.gazeHistory.length - i - 1];
      
      const pitchDiff = Math.abs(current.pitch - previous.pitch);
      const yawDiff = Math.abs(current.yaw - previous.yaw);
      totalVariation += pitchDiff + yawDiff;
    }

    // 稳定性 = 1 - 变化程度
    const stability = Math.max(0, 1 - totalVariation / 50); // 调整系数
    return stability;
  }

  /**
   * 计算眨眼率评分
   */
  private calculateBlinkRateScore(): number {
    const blinkRate = this.calculateBlinkRate();
    
    // 正常眨眼率约为15-20次/分钟
    const normalBlinkRate = 17;
    const deviation = Math.abs(blinkRate - normalBlinkRate);
    
    // 偏差越小，评分越高
    return Math.max(0, 1 - deviation / normalBlinkRate);
  }

  /**
   * 重置分析器
   */
  reset(): void {
    this.blinkHistory = [];
    this.gazeHistory = [];
  }

  /**
   * 设置参数
   */
  setParameters(params: {
    blinkThreshold?: number;
    gazeStabilityFactor?: number;
    maxHistorySize?: number;
  }): void {
    if (params.blinkThreshold !== undefined) {
      this.blinkThreshold = params.blinkThreshold;
    }
    if (params.gazeStabilityFactor !== undefined) {
      this.gazeStabilityFactor = params.gazeStabilityFactor;
    }
    if (params.maxHistorySize !== undefined) {
      this.maxHistorySize = params.maxHistorySize;
    }
  }
}

/**
 * 眼部追踪节点配置
 */
export interface EyeTrackingNodeConfig {
  /** 眨眼检测阈值 */
  blinkThreshold: number;
  /** 视线稳定性因子 */
  gazeStabilityFactor: number;
  /** 历史记录最大长度 */
  maxHistorySize: number;
  /** 是否启用眨眼检测 */
  enableBlinkDetection: boolean;
  /** 是否启用视线追踪 */
  enableGazeTracking: boolean;
  /** 是否启用注意力分析 */
  enableAttentionAnalysis: boolean;
  /** 置信度阈值 */
  confidenceThreshold: number;
  /** 是否启用调试模式 */
  debug: boolean;
}

/**
 * 眼部追踪节点
 */
export class EyeTrackingNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'EyeTracking';

  /** 节点名称 */
  public static readonly NAME = '眼部追踪';

  /** 节点描述 */
  public static readonly DESCRIPTION = '基于面部关键点进行眼部追踪和视线方向检测';

  private eyeAnalyzer: AdvancedEyeAnalyzer;
  private config: EyeTrackingNodeConfig;
  private lastResults: EyeTrackingResults | null = null;
  private processingCount = 0;
  private successCount = 0;
  private averageProcessingTime = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: EyeTrackingNodeConfig = {
    blinkThreshold: 0.3,
    gazeStabilityFactor: 0.7,
    maxHistorySize: 30,
    enableBlinkDetection: true,
    enableGazeTracking: true,
    enableAttentionAnalysis: true,
    confidenceThreshold: 0.5,
    debug: false
  };

  constructor(nodeType: string = EyeTrackingNode.TYPE, name: string = EyeTrackingNode.NAME, id?: string) {
    super(nodeType, name, id);

    this.config = { ...EyeTrackingNode.DEFAULT_CONFIG };
    this.eyeAnalyzer = new AdvancedEyeAnalyzer();
    this.setupPorts();
    this.updateAnalyzerParameters();
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput('faceLandmarks', 'array', '面部关键点');
    this.addInput('analyze', 'trigger', '分析');
    this.addInput('reset', 'trigger', '重置');
    this.addInput('blinkThreshold', 'number', '眨眼阈值');
    this.addInput('gazeStabilityFactor', 'number', '视线稳定性因子');
    this.addInput('enableBlinkDetection', 'boolean', '启用眨眼检测');
    this.addInput('enableGazeTracking', 'boolean', '启用视线追踪');
    this.addInput('enableAttentionAnalysis', 'boolean', '启用注意力分析');

    // 眼部状态输出端口
    this.addOutput('leftEyeState', 'string', '左眼状态');
    this.addOutput('rightEyeState', 'string', '右眼状态');
    this.addOutput('leftEyeOpenness', 'number', '左眼开合度');
    this.addOutput('rightEyeOpenness', 'number', '右眼开合度');
    this.addOutput('leftBlinkDetected', 'boolean', '左眼眨眼检测');
    this.addOutput('rightBlinkDetected', 'boolean', '右眼眨眼检测');

    // 视线追踪输出端口
    this.addOutput('leftGazeDirection', 'object', '左眼视线方向');
    this.addOutput('rightGazeDirection', 'object', '右眼视线方向');
    this.addOutput('combinedGazeDirection', 'object', '组合视线方向');
    this.addOutput('gazePitch', 'number', '视线俯仰角');
    this.addOutput('gazeYaw', 'number', '视线偏航角');
    this.addOutput('leftPupilPosition', 'object', '左眼瞳孔位置');
    this.addOutput('rightPupilPosition', 'object', '右眼瞳孔位置');

    // 分析结果输出端口
    this.addOutput('blinkRate', 'number', '眨眼率');
    this.addOutput('attentionLevel', 'number', '注意力水平');
    this.addOutput('gazeStability', 'number', '视线稳定性');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('processingTime', 'number', '处理时间');
    this.addOutput('averageProcessingTime', 'number', '平均处理时间');
    this.addOutput('successRate', 'number', '成功率');

    // 眼部区域输出端口
    this.addOutput('leftEyeLandmarks', 'array', '左眼关键点');
    this.addOutput('rightEyeLandmarks', 'array', '右眼关键点');
    this.addOutput('eyeRegionBox', 'object', '眼部区域边界框');

    // 事件输出端口
    this.addOutput('onBlinkDetected', 'trigger', '眨眼检测');
    this.addOutput('onGazeChanged', 'trigger', '视线变化');
    this.addOutput('onAttentionChanged', 'trigger', '注意力变化');
    this.addOutput('onAnalyzed', 'trigger', '分析完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  /**
   * 执行节点
   */
  public async execute(inputs?: any): Promise<any> {
    try {
      const startTime = performance.now();

      // 获取输入
      const faceLandmarks = inputs?.faceLandmarks as any[];
      const analyzeTrigger = inputs?.analyze;
      const resetTrigger = inputs?.reset;
      const blinkThreshold = inputs?.blinkThreshold as number;
      const gazeStabilityFactor = inputs?.gazeStabilityFactor as number;
      const enableBlinkDetection = inputs?.enableBlinkDetection as boolean;
      const enableGazeTracking = inputs?.enableGazeTracking as boolean;
      const enableAttentionAnalysis = inputs?.enableAttentionAnalysis as boolean;

      // 更新配置
      let configChanged = false;
      if (blinkThreshold !== undefined && blinkThreshold !== this.config.blinkThreshold) {
        this.config.blinkThreshold = Math.max(0, Math.min(1, blinkThreshold));
        configChanged = true;
      }
      if (gazeStabilityFactor !== undefined && gazeStabilityFactor !== this.config.gazeStabilityFactor) {
        this.config.gazeStabilityFactor = Math.max(0, Math.min(1, gazeStabilityFactor));
        configChanged = true;
      }
      if (enableBlinkDetection !== undefined && enableBlinkDetection !== this.config.enableBlinkDetection) {
        this.config.enableBlinkDetection = enableBlinkDetection;
        configChanged = true;
      }
      if (enableGazeTracking !== undefined && enableGazeTracking !== this.config.enableGazeTracking) {
        this.config.enableGazeTracking = enableGazeTracking;
        configChanged = true;
      }
      if (enableAttentionAnalysis !== undefined && enableAttentionAnalysis !== this.config.enableAttentionAnalysis) {
        this.config.enableAttentionAnalysis = enableAttentionAnalysis;
        configChanged = true;
      }

      // 如果配置改变，更新分析器参数
      if (configChanged) {
        this.updateAnalyzerParameters();
      }

      // 处理重置触发
      if (resetTrigger) {
        this.resetAnalyzer();
      }

      // 处理分析触发
      if (analyzeTrigger && faceLandmarks && faceLandmarks.length > 0) {
        await this.analyzeEyes(faceLandmarks);
        this.processingCount++;
        this.successCount++;
      }

      // 更新平均处理时间
      const processingTime = performance.now() - startTime;
      this.averageProcessingTime = (this.averageProcessingTime * (this.processingCount - 1) + processingTime) / this.processingCount;

      // 返回输出
      return this.getNodeOutputs();

    } catch (error) {
      Debug.error('EyeTrackingNode', '节点执行失败', String(error));
      return {
        onError: true,
        confidence: 0,
        processingTime: 0
      };
    }
  }

  /**
   * 更新分析器参数
   */
  private updateAnalyzerParameters(): void {
    this.eyeAnalyzer.setParameters({
      blinkThreshold: this.config.blinkThreshold,
      gazeStabilityFactor: this.config.gazeStabilityFactor,
      maxHistorySize: this.config.maxHistorySize
    });
  }

  /**
   * 重置分析器
   */
  private resetAnalyzer(): void {
    this.eyeAnalyzer.reset();
    this.lastResults = null;
    this.processingCount = 0;
    this.successCount = 0;
    this.averageProcessingTime = 0;

    Debug.log('EyeTrackingNode', '分析器已重置');
  }

  /**
   * 分析眼部
   */
  private async analyzeEyes(faceLandmarks: any[]): Promise<void> {
    try {
      if (!faceLandmarks || faceLandmarks.length < 468) {
        throw new Error('面部关键点数据不足，需要至少468个关键点');
      }

      // 执行眼部分析
      const results = this.eyeAnalyzer.analyzeEyes(faceLandmarks);

      // 更新处理时间
      results.processingTime = performance.now() - Date.now();

      this.lastResults = results;

      if (this.config.debug) {
        Debug.log('EyeTrackingNode', '眼部分析完成', {
          leftEyeState: results.leftEye.state,
          rightEyeState: results.rightEye.state,
          blinkRate: results.blinkRate,
          attentionLevel: results.attentionLevel
        });
      }

    } catch (error) {
      Debug.error('EyeTrackingNode', '眼部分析失败', String(error));
      throw error;
    }
  }

  /**
   * 获取节点输出值
   */
  private getNodeOutputs(): any {
    const baseOutputs = {
      leftEyeState: EyeState.OPEN,
      rightEyeState: EyeState.OPEN,
      leftEyeOpenness: 0,
      rightEyeOpenness: 0,
      leftBlinkDetected: false,
      rightBlinkDetected: false,
      leftGazeDirection: new Vector3(0, 0, 1),
      rightGazeDirection: new Vector3(0, 0, 1),
      combinedGazeDirection: new Vector3(0, 0, 1),
      gazePitch: 0,
      gazeYaw: 0,
      leftPupilPosition: new Vector2(0.5, 0.5),
      rightPupilPosition: new Vector2(0.5, 0.5),
      blinkRate: 0,
      attentionLevel: 0,
      gazeStability: 0,
      confidence: 0,
      processingTime: 0,
      averageProcessingTime: this.averageProcessingTime,
      successRate: this.processingCount > 0 ? this.successCount / this.processingCount : 0,
      leftEyeLandmarks: [],
      rightEyeLandmarks: [],
      eyeRegionBox: null,
      onBlinkDetected: false,
      onGazeChanged: false,
      onAttentionChanged: false,
      onAnalyzed: false,
      onError: false
    };

    if (!this.lastResults) {
      return baseOutputs;
    }

    const results = this.lastResults;

    // 计算眼部区域边界框
    const eyeRegionBox = this.calculateEyeRegionBox(results);

    // 计算视线稳定性
    const gazeStability = this.calculateGazeStability();

    return {
      leftEyeState: results.leftEye.state,
      rightEyeState: results.rightEye.state,
      leftEyeOpenness: results.leftEye.openness,
      rightEyeOpenness: results.rightEye.openness,
      leftBlinkDetected: this.config.enableBlinkDetection ? results.leftEye.blinkDetected : false,
      rightBlinkDetected: this.config.enableBlinkDetection ? results.rightEye.blinkDetected : false,

      leftGazeDirection: this.config.enableGazeTracking ? results.leftEye.gazeDirection.direction : new Vector3(0, 0, 1),
      rightGazeDirection: this.config.enableGazeTracking ? results.rightEye.gazeDirection.direction : new Vector3(0, 0, 1),
      combinedGazeDirection: this.config.enableGazeTracking ? results.combinedGaze.direction : new Vector3(0, 0, 1),
      gazePitch: this.config.enableGazeTracking ? results.combinedGaze.pitch : 0,
      gazeYaw: this.config.enableGazeTracking ? results.combinedGaze.yaw : 0,
      leftPupilPosition: results.leftEye.pupilPosition,
      rightPupilPosition: results.rightEye.pupilPosition,

      blinkRate: this.config.enableBlinkDetection ? results.blinkRate : 0,
      attentionLevel: this.config.enableAttentionAnalysis ? results.attentionLevel : 0,
      gazeStability,
      confidence: results.confidence,
      processingTime: results.processingTime,
      averageProcessingTime: this.averageProcessingTime,
      successRate: this.processingCount > 0 ? this.successCount / this.processingCount : 0,

      leftEyeLandmarks: results.leftEye.landmarks,
      rightEyeLandmarks: results.rightEye.landmarks,
      eyeRegionBox,

      onBlinkDetected: false,
      onGazeChanged: false,
      onAttentionChanged: false,
      onAnalyzed: false,
      onError: false
    };
  }

  /**
   * 计算眼部区域边界框
   */
  private calculateEyeRegionBox(results: EyeTrackingResults): any {
    const allLandmarks = [...results.leftEye.landmarks, ...results.rightEye.landmarks];

    if (allLandmarks.length === 0) {
      return null;
    }

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    for (const landmark of allLandmarks) {
      minX = Math.min(minX, landmark.x);
      minY = Math.min(minY, landmark.y);
      maxX = Math.max(maxX, landmark.x);
      maxY = Math.max(maxY, landmark.y);
    }

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  /**
   * 计算视线稳定性
   */
  private calculateGazeStability(): number {
    // 这里可以从分析器获取稳定性数据
    // 简化实现，返回基于置信度的稳定性
    if (!this.lastResults) return 0;
    return this.lastResults.confidence;
  }

  /**
   * 获取节点配置
   */
  public getConfig(): EyeTrackingNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public updateConfig(newConfig: Partial<EyeTrackingNodeConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // 更新分析器参数
    this.updateAnalyzerParameters();

    Debug.log('EyeTrackingNode', '配置已更新', { oldConfig, newConfig: this.config });
  }

  /**
   * 获取最后分析结果
   */
  public getLastResults(): EyeTrackingResults | null {
    return this.lastResults ? { ...this.lastResults } : null;
  }

  /**
   * 获取处理统计
   */
  public getProcessingStats(): {
    totalProcessed: number;
    successCount: number;
    successRate: number;
    averageProcessingTime: number;
  } {
    return {
      totalProcessed: this.processingCount,
      successCount: this.successCount,
      successRate: this.processingCount > 0 ? this.successCount / this.processingCount : 0,
      averageProcessingTime: this.averageProcessingTime
    };
  }

  /**
   * 获取支持的眼部状态
   */
  public static getSupportedEyeStates(): EyeState[] {
    return Object.values(EyeState);
  }

  /**
   * 检查是否有有效的眼部数据
   */
  public hasValidEyeData(): boolean {
    return this.lastResults !== null && this.lastResults.confidence > this.config.confidenceThreshold;
  }

  /**
   * 获取当前眨眼率
   */
  public getCurrentBlinkRate(): number {
    return this.lastResults ? this.lastResults.blinkRate : 0;
  }

  /**
   * 获取当前注意力水平
   */
  public getCurrentAttentionLevel(): number {
    return this.lastResults ? this.lastResults.attentionLevel : 0;
  }

  /**
   * 获取当前视线方向
   */
  public getCurrentGazeDirection(): GazeDirection | null {
    return this.lastResults ? this.lastResults.combinedGaze : null;
  }

  /**
   * 销毁节点
   */
  public destroy(): void {
    this.eyeAnalyzer.reset();
    this.lastResults = null;
    this.processingCount = 0;
    this.successCount = 0;
    this.averageProcessingTime = 0;

    super.destroy();
  }
}
