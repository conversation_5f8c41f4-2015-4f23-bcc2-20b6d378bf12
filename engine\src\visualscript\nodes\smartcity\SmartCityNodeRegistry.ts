/**
 * 智慧城市节点注册表
 * 注册所有智慧城市相关的视觉脚本节点
 */

import {
  IoTDeviceConnectNode,
  IoTDataProcessingNode,
  TrafficFlowMonitorNode,
  SmartTrafficLightNode,
  AirQualityMonitorNode,
  SmartStreetLightNode,
  EmergencyResponseNode
} from './SmartCityNodes';

/**
 * 智慧城市节点分类
 */
export enum SmartCityNodeCategory {
  IOT_DEVICES = 'iot_devices',
  TRAFFIC_MANAGEMENT = 'traffic_management',
  ENVIRONMENT_MONITORING = 'environment_monitoring',
  CITY_MANAGEMENT = 'city_management',
  EMERGENCY_RESPONSE = 'emergency_response',
  ENERGY_MANAGEMENT = 'energy_management',
  PUBLIC_SAFETY = 'public_safety',
  URBAN_PLANNING = 'urban_planning'
}

/**
 * 注册智慧城市节点到简化注册表
 */
export function registerSmartCityNodes(): Map<string, any> {
  const nodeRegistry = new Map<string, any>();

  // IoT设备管理节点
  nodeRegistry.set(IoTDeviceConnectNode.TYPE, {
    type: IoTDeviceConnectNode.TYPE,
    name: IoTDeviceConnectNode.NAME,
    description: IoTDeviceConnectNode.DESCRIPTION,
    category: SmartCityNodeCategory.IOT_DEVICES,
    nodeClass: IoTDeviceConnectNode,
    icon: 'device_hub',
    color: '#2196F3',
    tags: ['iot', 'device', 'connection', 'mqtt', 'sensor']
  });

  nodeRegistry.set(IoTDataProcessingNode.TYPE, {
    type: IoTDataProcessingNode.TYPE,
    name: IoTDataProcessingNode.NAME,
    description: IoTDataProcessingNode.DESCRIPTION,
    category: SmartCityNodeCategory.IOT_DEVICES,
    nodeClass: IoTDataProcessingNode,
    icon: 'analytics',
    color: '#2196F3',
    tags: ['iot', 'data', 'processing', 'analytics', 'anomaly']
  });

  return nodeRegistry;
}

/**
 * 获取智慧城市节点统计信息
 */
export function getSmartCityNodeStats(): any {
  return {
    totalNodes: 7,
    categories: {
      [SmartCityNodeCategory.IOT_DEVICES]: 2,
      [SmartCityNodeCategory.TRAFFIC_MANAGEMENT]: 2,
      [SmartCityNodeCategory.ENVIRONMENT_MONITORING]: 1,
      [SmartCityNodeCategory.CITY_MANAGEMENT]: 1,
      [SmartCityNodeCategory.EMERGENCY_RESPONSE]: 1,
      [SmartCityNodeCategory.ENERGY_MANAGEMENT]: 0,
      [SmartCityNodeCategory.PUBLIC_SAFETY]: 0,
      [SmartCityNodeCategory.URBAN_PLANNING]: 0
    },
    implementationStatus: {
      implemented: 7,
      planned: 15,
      total: 22
    }
  };
}

/**
 * 智慧城市节点使用示例
 */
export const SMART_CITY_NODE_EXAMPLES = {
  iot_monitoring: {
    title: 'IoT设备监控系统',
    description: '连接和监控城市IoT设备，实时处理传感器数据',
    nodes: [
      { type: 'iot_device_connect', config: { protocol: 'MQTT', deviceType: 'temperature_sensor' } },
      { type: 'iot_data_processing', config: { processingType: 'anomaly_detect' } }
    ]
  },
  
  traffic_optimization: {
    title: '智能交通优化',
    description: '监控交通流量并智能控制信号灯',
    nodes: [
      { type: 'traffic_flow_monitor', config: { analysisInterval: 60 } },
      { type: 'smart_traffic_light', config: { optimizationMode: 'flow' } }
    ]
  },
  
  environmental_monitoring: {
    title: '环境质量监测',
    description: '监测空气质量并提供健康建议',
    nodes: [
      { type: 'air_quality_monitor', config: { alertThreshold: 150 } }
    ]
  },
  
  smart_lighting: {
    title: '智慧路灯管理',
    description: '根据环境和运动检测智能控制路灯',
    nodes: [
      { type: 'smart_street_light', config: { energyMode: 'eco' } }
    ]
  },
  
  emergency_management: {
    title: '应急响应管理',
    description: '处理应急事件并协调响应资源',
    nodes: [
      { type: 'emergency_response', config: { autoDispatch: true } }
    ]
  }
};

/**
 * 智慧城市应用场景模板
 */
export const SMART_CITY_SCENARIOS = [
  {
    id: 'digital_twin_city',
    name: '数字孪生城市',
    description: '构建城市的完整数字孪生模型',
    requiredNodes: ['iot_device_connect', 'spatial_query', 'real_time_sync'],
    complexity: 'high'
  },
  {
    id: 'intelligent_transportation',
    name: '智能交通系统',
    description: '优化城市交通流量和信号控制',
    requiredNodes: ['traffic_flow_monitor', 'smart_traffic_light', 'route_optimization'],
    complexity: 'medium'
  },
  {
    id: 'environmental_protection',
    name: '环境保护监测',
    description: '全面监测城市环境质量',
    requiredNodes: ['air_quality_monitor', 'noise_monitor', 'water_quality_monitor'],
    complexity: 'medium'
  },
  {
    id: 'smart_energy_grid',
    name: '智慧能源网格',
    description: '优化城市能源分配和使用',
    requiredNodes: ['energy_monitor', 'smart_street_light', 'building_energy_management'],
    complexity: 'high'
  },
  {
    id: 'public_safety_system',
    name: '公共安全系统',
    description: '提升城市公共安全水平',
    requiredNodes: ['emergency_response', 'surveillance_monitor', 'crowd_analysis'],
    complexity: 'high'
  }
];

/**
 * 导出所有智慧城市节点类
 */
export {
  IoTDeviceConnectNode,
  IoTDataProcessingNode,
  TrafficFlowMonitorNode,
  SmartTrafficLightNode,
  AirQualityMonitorNode,
  SmartStreetLightNode,
  EmergencyResponseNode
};
