/**
 * AI内容推荐服务
 * 提供智能内容推荐功能，包括组件、资源、模板等推荐
 */
import { EventEmitter } from '../utils/EventEmitter';

// 推荐类型枚举
export enum RecommendationType {
  COMPONENT = 'component',
  TEMPLATE = 'template',
  ASSET = 'asset',
  COLOR_PALETTE = 'color_palette',
  FONT = 'font',
  ICON = 'icon',
  IMAGE = 'image',
  LAYOUT = 'layout'
}

// 推荐项目接口
export interface RecommendationItem {
  id: string;
  type: RecommendationType;
  title: string;
  description: string;
  category: string;
  tags: string[];
  thumbnail?: string;
  preview?: string;
  confidence: number; // 0-100
  relevanceScore: number; // 0-100
  popularity: number; // 0-100
  metadata: RecommendationMetadata;
  source: RecommendationSource;
  timestamp: number;
}

// 推荐元数据接口
export interface RecommendationMetadata {
  size?: string;
  format?: string;
  dimensions?: { width: number; height: number };
  fileSize?: number;
  license?: string;
  author?: string;
  downloadCount?: number;
  rating?: number;
  compatibility?: string[];
  properties?: Record<string, any>;
}

// 推荐来源接口
export interface RecommendationSource {
  name: string;
  url?: string;
  type: 'internal' | 'external' | 'ai_generated';
  apiKey?: string;
}

// 推荐上下文接口
export interface RecommendationContext {
  currentProject?: ProjectContext;
  userPreferences?: UserPreferences;
  designStyle?: string;
  colorScheme?: string;
  targetAudience?: string;
  industry?: string;
  keywords?: string[];
  excludeTypes?: RecommendationType[];
  maxResults?: number;
}

// 项目上下文接口
export interface ProjectContext {
  type: string;
  theme: string;
  colors: string[];
  fonts: string[];
  components: string[];
  layout: string;
  content: string[];
}

// 用户偏好接口
export interface UserPreferences {
  favoriteCategories: string[];
  preferredStyles: string[];
  recentlyUsed: string[];
  bookmarks: string[];
  customTags: string[];
  qualityThreshold: number;
}

// 推荐结果接口
export interface RecommendationResult {
  id: string;
  query: string;
  context: RecommendationContext;
  items: RecommendationItem[];
  totalCount: number;
  categories: CategorySummary[];
  suggestions: string[];
  timestamp: number;
}

// 分类摘要接口
export interface CategorySummary {
  category: string;
  count: number;
  avgConfidence: number;
  topItems: RecommendationItem[];
}

// 推荐配置接口
export interface RecommendationConfig {
  enableAIRecommendations: boolean;
  enablePopularityBoost: boolean;
  enablePersonalization: boolean;
  confidenceThreshold: number;
  maxResultsPerType: number;
  cacheExpiration: number; // minutes
  dataSources: RecommendationSource[];
}

/**
 * AI内容推荐服务类
 */
export class AIContentRecommendationService extends EventEmitter {
  private static instance: AIContentRecommendationService;
  private recommendations: RecommendationResult[] = [];
  private userPreferences: UserPreferences;
  private config: RecommendationConfig;
  private cache: Map<string, RecommendationResult> = new Map();
  private isRecommending: boolean = false;

  private constructor() {
    super();
    this.config = this.getDefaultConfig();
    this.userPreferences = this.getDefaultUserPreferences();
  }

  public static getInstance(): AIContentRecommendationService {
    if (!AIContentRecommendationService.instance) {
      AIContentRecommendationService.instance = new AIContentRecommendationService();
    }
    return AIContentRecommendationService.instance;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): RecommendationConfig {
    return {
      enableAIRecommendations: true,
      enablePopularityBoost: true,
      enablePersonalization: true,
      confidenceThreshold: 70,
      maxResultsPerType: 20,
      cacheExpiration: 30,
      dataSources: [
        {
          name: 'Internal Library',
          type: 'internal'
        },
        {
          name: 'AI Generator',
          type: 'ai_generated'
        }
      ]
    };
  }

  /**
   * 获取默认用户偏好
   */
  private getDefaultUserPreferences(): UserPreferences {
    return {
      favoriteCategories: ['ui-components', 'icons', 'templates'],
      preferredStyles: ['modern', 'minimalist'],
      recentlyUsed: [],
      bookmarks: [],
      customTags: [],
      qualityThreshold: 80
    };
  }

  /**
   * 获取推荐内容
   */
  public async getRecommendations(
    query: string,
    context?: RecommendationContext
  ): Promise<RecommendationResult> {
    if (this.isRecommending) {
      throw new Error('Recommendation already in progress');
    }

    this.isRecommending = true;
    this.emit('recommendationStarted');

    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(query, context);
      const cached = this.cache.get(cacheKey);
      
      if (cached && this.isCacheValid(cached)) {
        this.emit('recommendationCompleted', cached);
        return cached;
      }

      // 生成推荐
      const result = await this.generateRecommendations(query, context);
      
      // 缓存结果
      this.cache.set(cacheKey, result);
      this.recommendations.push(result);
      
      // 保持历史记录在合理范围内
      if (this.recommendations.length > 50) {
        this.recommendations.shift();
      }

      this.emit('recommendationCompleted', result);
      return result;
    } catch (error) {
      this.emit('recommendationError', error);
      throw error;
    } finally {
      this.isRecommending = false;
    }
  }

  /**
   * 生成推荐内容
   */
  private async generateRecommendations(
    query: string,
    context?: RecommendationContext
  ): Promise<RecommendationResult> {
    // 模拟AI推荐延迟
    await new Promise(resolve => setTimeout(resolve, 1500));

    const resultId = `recommendation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const mergedContext = { ...context, userPreferences: this.userPreferences };
    
    // 生成推荐项目
    const items = await this.generateRecommendationItems(query, mergedContext);
    
    // 按类型分组
    const categories = this.groupByCategory(items);
    
    // 生成建议
    const suggestions = this.generateSuggestions(query, items);

    const result: RecommendationResult = {
      id: resultId,
      query,
      context: mergedContext,
      items,
      totalCount: items.length,
      categories,
      suggestions,
      timestamp: Date.now()
    };

    return result;
  }

  /**
   * 生成推荐项目
   */
  private async generateRecommendationItems(
    query: string,
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    const items: RecommendationItem[] = [];

    // 基于查询关键词生成不同类型的推荐
    if (query.toLowerCase().includes('button') || query.toLowerCase().includes('component')) {
      items.push(...this.generateComponentRecommendations(query, context));
    }

    if (query.toLowerCase().includes('icon') || query.toLowerCase().includes('图标')) {
      items.push(...this.generateIconRecommendations(query, context));
    }

    if (query.toLowerCase().includes('color') || query.toLowerCase().includes('颜色')) {
      items.push(...this.generateColorRecommendations(query, context));
    }

    if (query.toLowerCase().includes('template') || query.toLowerCase().includes('模板')) {
      items.push(...this.generateTemplateRecommendations(query, context));
    }

    if (query.toLowerCase().includes('image') || query.toLowerCase().includes('图片')) {
      items.push(...this.generateImageRecommendations(query, context));
    }

    // 如果没有特定类型，生成通用推荐
    if (items.length === 0) {
      items.push(...this.generateGeneralRecommendations(query, context));
    }

    // 应用个性化和排序
    return this.personalizeAndSort(items, context);
  }

  /**
   * 生成组件推荐
   */
  private generateComponentRecommendations(
    _query: string,
    _context: RecommendationContext
  ): RecommendationItem[] {
    return [
      {
        id: 'comp_button_primary',
        type: RecommendationType.COMPONENT,
        title: 'Primary Button',
        description: 'Modern primary button with hover effects',
        category: 'buttons',
        tags: ['button', 'primary', 'interactive'],
        thumbnail: '/thumbnails/button_primary.png',
        confidence: 95,
        relevanceScore: 90,
        popularity: 85,
        metadata: {
          size: 'medium',
          compatibility: ['react', 'vue', 'angular'],
          properties: { variant: 'primary', size: 'medium' }
        },
        source: { name: 'Internal Library', type: 'internal' },
        timestamp: Date.now()
      },
      {
        id: 'comp_button_secondary',
        type: RecommendationType.COMPONENT,
        title: 'Secondary Button',
        description: 'Subtle secondary button for less prominent actions',
        category: 'buttons',
        tags: ['button', 'secondary', 'subtle'],
        thumbnail: '/thumbnails/button_secondary.png',
        confidence: 88,
        relevanceScore: 85,
        popularity: 78,
        metadata: {
          size: 'medium',
          compatibility: ['react', 'vue', 'angular'],
          properties: { variant: 'secondary', size: 'medium' }
        },
        source: { name: 'Internal Library', type: 'internal' },
        timestamp: Date.now()
      }
    ];
  }

  /**
   * 生成图标推荐
   */
  private generateIconRecommendations(
    _query: string,
    _context: RecommendationContext
  ): RecommendationItem[] {
    return [
      {
        id: 'icon_home',
        type: RecommendationType.ICON,
        title: 'Home Icon',
        description: 'Clean home icon for navigation',
        category: 'navigation',
        tags: ['home', 'navigation', 'house'],
        thumbnail: '/thumbnails/icon_home.svg',
        confidence: 92,
        relevanceScore: 88,
        popularity: 95,
        metadata: {
          format: 'svg',
          dimensions: { width: 24, height: 24 },
          license: 'MIT'
        },
        source: { name: 'Icon Library', type: 'external' },
        timestamp: Date.now()
      },
      {
        id: 'icon_user',
        type: RecommendationType.ICON,
        title: 'User Icon',
        description: 'User profile icon',
        category: 'user',
        tags: ['user', 'profile', 'person'],
        thumbnail: '/thumbnails/icon_user.svg',
        confidence: 89,
        relevanceScore: 82,
        popularity: 87,
        metadata: {
          format: 'svg',
          dimensions: { width: 24, height: 24 },
          license: 'MIT'
        },
        source: { name: 'Icon Library', type: 'external' },
        timestamp: Date.now()
      }
    ];
  }

  /**
   * 生成颜色推荐
   */
  private generateColorRecommendations(
    _query: string,
    _context: RecommendationContext
  ): RecommendationItem[] {
    return [
      {
        id: 'palette_modern_blue',
        type: RecommendationType.COLOR_PALETTE,
        title: 'Modern Blue Palette',
        description: 'Professional blue color palette',
        category: 'color-palettes',
        tags: ['blue', 'professional', 'modern'],
        thumbnail: '/thumbnails/palette_blue.png',
        confidence: 91,
        relevanceScore: 87,
        popularity: 83,
        metadata: {
          properties: {
            colors: ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff', '#bae7ff'],
            harmony: 'monochromatic'
          }
        },
        source: { name: 'AI Generator', type: 'ai_generated' },
        timestamp: Date.now()
      }
    ];
  }

  /**
   * 生成模板推荐
   */
  private generateTemplateRecommendations(
    _query: string,
    _context: RecommendationContext
  ): RecommendationItem[] {
    return [
      {
        id: 'template_dashboard',
        type: RecommendationType.TEMPLATE,
        title: 'Admin Dashboard Template',
        description: 'Complete admin dashboard with sidebar navigation',
        category: 'dashboards',
        tags: ['dashboard', 'admin', 'sidebar'],
        thumbnail: '/thumbnails/template_dashboard.png',
        confidence: 94,
        relevanceScore: 92,
        popularity: 89,
        metadata: {
          compatibility: ['react', 'vue'],
          rating: 4.8,
          downloadCount: 1250
        },
        source: { name: 'Template Library', type: 'external' },
        timestamp: Date.now()
      }
    ];
  }

  /**
   * 生成图片推荐
   */
  private generateImageRecommendations(
    _query: string,
    _context: RecommendationContext
  ): RecommendationItem[] {
    return [
      {
        id: 'image_hero_tech',
        type: RecommendationType.IMAGE,
        title: 'Technology Hero Image',
        description: 'Modern technology background for hero sections',
        category: 'backgrounds',
        tags: ['technology', 'hero', 'background'],
        thumbnail: '/thumbnails/image_hero_tech.jpg',
        confidence: 86,
        relevanceScore: 84,
        popularity: 76,
        metadata: {
          dimensions: { width: 1920, height: 1080 },
          format: 'jpg',
          fileSize: 245760,
          license: 'Royalty Free'
        },
        source: { name: 'Stock Photos', type: 'external' },
        timestamp: Date.now()
      }
    ];
  }

  /**
   * 生成通用推荐
   */
  private generateGeneralRecommendations(
    _query: string,
    context: RecommendationContext
  ): RecommendationItem[] {
    const items: RecommendationItem[] = [];
    
    // 基于用户偏好生成推荐
    if (context.userPreferences?.favoriteCategories.includes('ui-components')) {
      items.push(...this.generateComponentRecommendations('component', context));
    }
    
    if (context.userPreferences?.favoriteCategories.includes('icons')) {
      items.push(...this.generateIconRecommendations('icon', context));
    }
    
    return items;
  }

  /**
   * 个性化和排序
   */
  private personalizeAndSort(
    items: RecommendationItem[],
    context: RecommendationContext
  ): RecommendationItem[] {
    if (!this.config.enablePersonalization) {
      return items.sort((a, b) => b.confidence - a.confidence);
    }

    // 应用个性化评分
    const personalizedItems = items.map(item => {
      let personalizedScore = item.confidence;
      
      // 基于用户偏好调整分数
      if (context.userPreferences?.favoriteCategories.includes(item.category)) {
        personalizedScore += 10;
      }
      
      if (context.userPreferences?.recentlyUsed.includes(item.id)) {
        personalizedScore += 5;
      }
      
      if (context.userPreferences?.bookmarks.includes(item.id)) {
        personalizedScore += 15;
      }
      
      // 应用流行度提升
      if (this.config.enablePopularityBoost) {
        personalizedScore += item.popularity * 0.1;
      }
      
      return {
        ...item,
        confidence: Math.min(100, personalizedScore)
      };
    });

    // 按个性化分数排序
    return personalizedItems.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * 按类别分组
   */
  private groupByCategory(items: RecommendationItem[]): CategorySummary[] {
    const groups = new Map<string, RecommendationItem[]>();
    
    items.forEach(item => {
      if (!groups.has(item.category)) {
        groups.set(item.category, []);
      }
      groups.get(item.category)!.push(item);
    });

    return Array.from(groups.entries()).map(([category, categoryItems]) => ({
      category,
      count: categoryItems.length,
      avgConfidence: categoryItems.reduce((sum, item) => sum + item.confidence, 0) / categoryItems.length,
      topItems: categoryItems.slice(0, 3)
    }));
  }

  /**
   * 生成建议
   */
  private generateSuggestions(_query: string, items: RecommendationItem[]): string[] {
    const suggestions: string[] = [];
    
    // 基于推荐项目生成相关建议
    const categories = [...new Set(items.map(item => item.category))];
    categories.forEach(category => {
      suggestions.push(`Try searching for more ${category}`);
    });
    
    // 基于标签生成建议
    const allTags = items.flatMap(item => item.tags);
    const popularTags = [...new Set(allTags)].slice(0, 3);
    popularTags.forEach(tag => {
      suggestions.push(`Explore ${tag} related content`);
    });
    
    return suggestions.slice(0, 5);
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(query: string, context?: RecommendationContext): string {
    const contextStr = context ? JSON.stringify(context) : '';
    return `${query}_${contextStr}`.replace(/\s+/g, '_').toLowerCase();
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(result: RecommendationResult): boolean {
    const now = Date.now();
    const expiration = this.config.cacheExpiration * 60 * 1000; // 转换为毫秒
    return (now - result.timestamp) < expiration;
  }

  /**
   * 添加到收藏
   */
  public addToBookmarks(itemId: string): void {
    if (!this.userPreferences.bookmarks.includes(itemId)) {
      this.userPreferences.bookmarks.push(itemId);
      this.emit('bookmarkAdded', itemId);
    }
  }

  /**
   * 从收藏移除
   */
  public removeFromBookmarks(itemId: string): void {
    const index = this.userPreferences.bookmarks.indexOf(itemId);
    if (index > -1) {
      this.userPreferences.bookmarks.splice(index, 1);
      this.emit('bookmarkRemoved', itemId);
    }
  }

  /**
   * 记录使用
   */
  public recordUsage(itemId: string): void {
    // 移除旧记录
    const index = this.userPreferences.recentlyUsed.indexOf(itemId);
    if (index > -1) {
      this.userPreferences.recentlyUsed.splice(index, 1);
    }
    
    // 添加到最前面
    this.userPreferences.recentlyUsed.unshift(itemId);
    
    // 保持最近使用列表在合理长度
    if (this.userPreferences.recentlyUsed.length > 20) {
      this.userPreferences.recentlyUsed.pop();
    }
    
    this.emit('usageRecorded', itemId);
  }

  /**
   * 获取推荐历史
   */
  public getRecommendationHistory(): RecommendationResult[] {
    return [...this.recommendations];
  }

  /**
   * 获取用户偏好
   */
  public getUserPreferences(): UserPreferences {
    return { ...this.userPreferences };
  }

  /**
   * 更新用户偏好
   */
  public updateUserPreferences(preferences: Partial<UserPreferences>): void {
    this.userPreferences = { ...this.userPreferences, ...preferences };
    this.emit('preferencesUpdated', this.userPreferences);
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.cache.clear();
    this.emit('cacheCleared');
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<RecommendationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): RecommendationConfig {
    return { ...this.config };
  }

  /**
   * 检查是否正在推荐
   */
  public isRecommendationInProgress(): boolean {
    return this.isRecommending;
  }

  /**
   * 获取推荐项目详情
   */
  public getRecommendationItem(itemId: string): RecommendationItem | null {
    for (const result of this.recommendations) {
      const item = result.items.find(item => item.id === itemId);
      if (item) {
        return item;
      }
    }
    return null;
  }

  /**
   * 搜索推荐项目
   */
  public searchRecommendations(
    searchTerm: string,
    filters?: {
      type?: RecommendationType;
      category?: string;
      minConfidence?: number;
    }
  ): RecommendationItem[] {
    const allItems = this.recommendations.flatMap(result => result.items);

    return allItems.filter(item => {
      // 文本搜索
      const matchesSearch = !searchTerm ||
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      // 类型过滤
      const matchesType = !filters?.type || item.type === filters.type;

      // 分类过滤
      const matchesCategory = !filters?.category || item.category === filters.category;

      // 置信度过滤
      const matchesConfidence = !filters?.minConfidence || item.confidence >= filters.minConfidence;

      return matchesSearch && matchesType && matchesCategory && matchesConfidence;
    });
  }
}

export default AIContentRecommendationService;
