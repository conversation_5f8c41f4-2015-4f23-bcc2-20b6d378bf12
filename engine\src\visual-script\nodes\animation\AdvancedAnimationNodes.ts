/**
 * 高级动画节点集合
 * 提供动画状态机、动画混合、IK系统等高级动画功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Quaternion, Object3D, Bone } from 'three';

/**
 * 动画状态枚举
 */
export enum AnimationState {
  IDLE = 'idle',
  PLAYING = 'playing',
  PAUSED = 'paused',
  STOPPED = 'stopped',
  TRANSITIONING = 'transitioning'
}

/**
 * 过渡类型枚举
 */
export enum TransitionType {
  IMMEDIATE = 'immediate',
  LINEAR = 'linear',
  SMOOTH = 'smooth',
  EASE_IN = 'easeIn',
  EASE_OUT = 'easeOut',
  EASE_IN_OUT = 'easeInOut'
}

/**
 * IK求解器类型枚举
 */
export enum IKSolverType {
  CCD = 'ccd',
  FABRIK = 'fabrik',
  JACOBIAN = 'jacobian',
  TWO_BONE = 'twoBone'
}

/**
 * 动画混合模式枚举
 */
export enum BlendMode {
  OVERRIDE = 'override',
  ADDITIVE = 'additive',
  MULTIPLY = 'multiply',
  SUBTRACT = 'subtract'
}

/**
 * 动画状态接口
 */
export interface AnimationStateInfo {
  name: string;
  clip: any;
  weight: number;
  speed: number;
  loop: boolean;
  clampWhenFinished: boolean;
  crossFadeDuration: number;
  enabled: boolean;
}

/**
 * 状态转换接口
 */
export interface StateTransition {
  from: string;
  to: string;
  condition: string;
  duration: number;
  type: TransitionType;
  hasExitTime: boolean;
  exitTime: number;
  interruptible: boolean;
}

/**
 * IK链接口
 */
export interface IKChain {
  id: string;
  bones: Bone[];
  target: Vector3;
  poleTarget?: Vector3;
  iterations: number;
  tolerance: number;
  solver: IKSolverType;
  weight: number;
  enabled: boolean;
}

/**
 * 动画混合层接口
 */
export interface AnimationLayer {
  name: string;
  weight: number;
  blendMode: BlendMode;
  mask?: string[];
  states: Map<string, AnimationStateInfo>;
  currentState: string;
  defaultState: string;
  enabled: boolean;
}

/**
 * 高级动画管理器
 */
class AdvancedAnimationManager {
  private stateMachines: Map<string, AnimationStateMachine> = new Map();
  private ikSolvers: Map<string, IKSolver> = new Map();
  private blendTrees: Map<string, AnimationBlendTree> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 创建动画状态机
   */
  createStateMachine(id: string, target: Object3D): AnimationStateMachine {
    const stateMachine = new AnimationStateMachine(id, target);
    this.stateMachines.set(id, stateMachine);
    this.emit('stateMachineCreated', { id, stateMachine });
    
    Debug.log('AdvancedAnimationManager', `动画状态机创建: ${id}`);
    return stateMachine;
  }

  /**
   * 创建IK求解器
   */
  createIKSolver(id: string, chain: IKChain): IKSolver {
    const solver = new IKSolver(id, chain);
    this.ikSolvers.set(id, solver);
    this.emit('ikSolverCreated', { id, solver });
    
    Debug.log('AdvancedAnimationManager', `IK求解器创建: ${id}`);
    return solver;
  }

  /**
   * 创建动画混合树
   */
  createBlendTree(id: string): AnimationBlendTree {
    const blendTree = new AnimationBlendTree(id);
    this.blendTrees.set(id, blendTree);
    this.emit('blendTreeCreated', { id, blendTree });
    
    Debug.log('AdvancedAnimationManager', `动画混合树创建: ${id}`);
    return blendTree;
  }

  /**
   * 更新所有动画系统
   */
  update(deltaTime: number): void {
    // 更新状态机
    for (const stateMachine of this.stateMachines.values()) {
      stateMachine.update(deltaTime);
    }

    // 更新IK求解器
    for (const solver of this.ikSolvers.values()) {
      solver.update(deltaTime);
    }

    // 更新混合树
    for (const blendTree of this.blendTrees.values()) {
      blendTree.update(deltaTime);
    }
  }

  /**
   * 获取状态机
   */
  getStateMachine(id: string): AnimationStateMachine | undefined {
    return this.stateMachines.get(id);
  }

  /**
   * 获取IK求解器
   */
  getIKSolver(id: string): IKSolver | undefined {
    return this.ikSolvers.get(id);
  }

  /**
   * 获取混合树
   */
  getBlendTree(id: string): AnimationBlendTree | undefined {
    return this.blendTrees.get(id);
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('AdvancedAnimationManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stateMachines.clear();
    this.ikSolvers.clear();
    this.blendTrees.clear();
    this.eventListeners.clear();
  }
}

/**
 * 动画状态机类
 */
class AnimationStateMachine {
  public id: string;
  public target: Object3D;
  public layers: Map<string, AnimationLayer> = new Map();
  public transitions: StateTransition[] = [];
  public parameters: Map<string, any> = new Map();
  public currentLayer: string = 'Base';
  public enabled: boolean = true;

  constructor(id: string, target: Object3D) {
    this.id = id;
    this.target = target;
    
    // 创建默认层
    this.createLayer('Base', 1.0, BlendMode.OVERRIDE);
  }

  /**
   * 创建动画层
   */
  createLayer(name: string, weight: number, blendMode: BlendMode): AnimationLayer {
    const layer: AnimationLayer = {
      name,
      weight,
      blendMode,
      states: new Map(),
      currentState: '',
      defaultState: '',
      enabled: true
    };

    this.layers.set(name, layer);
    return layer;
  }

  /**
   * 添加动画状态
   */
  addState(layerName: string, stateName: string, stateInfo: AnimationStateInfo): void {
    const layer = this.layers.get(layerName);
    if (layer) {
      layer.states.set(stateName, stateInfo);
      
      // 如果是第一个状态，设为默认状态
      if (layer.states.size === 1) {
        layer.defaultState = stateName;
        layer.currentState = stateName;
      }
    }
  }

  /**
   * 添加状态转换
   */
  addTransition(transition: StateTransition): void {
    this.transitions.push(transition);
  }

  /**
   * 播放状态
   */
  playState(layerName: string, stateName: string, crossFadeDuration: number = 0): boolean {
    const layer = this.layers.get(layerName);
    if (!layer || !layer.states.has(stateName)) {
      return false;
    }

    if (crossFadeDuration > 0) {
      this.crossFadeToState(layerName, stateName, crossFadeDuration);
    } else {
      layer.currentState = stateName;
    }

    return true;
  }

  /**
   * 交叉淡入到状态
   */
  private crossFadeToState(layerName: string, stateName: string, _duration: number): void {
    // 简化的交叉淡入实现
    const layer = this.layers.get(layerName);
    if (layer) {
      layer.currentState = stateName;
      // 这里应该实现实际的交叉淡入逻辑
    }
  }

  /**
   * 设置参数
   */
  setParameter(name: string, value: any): void {
    this.parameters.set(name, value);
  }

  /**
   * 获取参数
   */
  getParameter(name: string): any {
    return this.parameters.get(name);
  }

  /**
   * 更新状态机
   */
  update(deltaTime: number): void {
    if (!this.enabled) return;

    // 检查状态转换
    this.checkTransitions();

    // 更新所有层
    for (const layer of this.layers.values()) {
      if (layer.enabled) {
        this.updateLayer(layer, deltaTime);
      }
    }
  }

  /**
   * 检查状态转换
   */
  private checkTransitions(): void {
    for (const transition of this.transitions) {
      if (this.evaluateCondition(transition.condition)) {
        const layer = this.layers.get(this.currentLayer);
        if (layer && layer.currentState === transition.from) {
          this.playState(this.currentLayer, transition.to, transition.duration);
        }
      }
    }
  }

  /**
   * 评估转换条件
   */
  private evaluateCondition(_condition: string): boolean {
    // 简化的条件评估
    // 实际实现应该解析条件表达式
    return false;
  }

  /**
   * 更新动画层
   */
  private updateLayer(layer: AnimationLayer, _deltaTime: number): void {
    const currentState = layer.states.get(layer.currentState);
    if (currentState && currentState.enabled) {
      // 更新当前状态的动画
      // 这里应该调用实际的动画播放逻辑
    }
  }
}

/**
 * IK求解器类
 */
class IKSolver {
  public id: string;
  public chain: IKChain;
  public enabled: boolean = true;

  constructor(id: string, chain: IKChain) {
    this.id = id;
    this.chain = chain;
  }

  /**
   * 求解IK
   */
  solve(): boolean {
    if (!this.enabled || !this.chain.enabled) {
      return false;
    }

    switch (this.chain.solver) {
      case IKSolverType.CCD:
        return this.solveCCD();
      case IKSolverType.FABRIK:
        return this.solveFABRIK();
      case IKSolverType.TWO_BONE:
        return this.solveTwoBone();
      default:
        return this.solveCCD();
    }
  }

  /**
   * CCD求解器
   */
  private solveCCD(): boolean {
    const { bones, target, iterations, tolerance } = this.chain;
    
    for (let iter = 0; iter < iterations; iter++) {
      let converged = true;

      for (let i = bones.length - 2; i >= 0; i--) {
        const bone = bones[i];
        const endEffector = bones[bones.length - 1];
        
        // 计算从当前骨骼到末端执行器的向量
        const toEnd = new Vector3().subVectors(endEffector.position, bone.position);
        // 计算从当前骨骼到目标的向量
        const toTarget = new Vector3().subVectors(target, bone.position);
        
        // 计算旋转角度
        const angle = toEnd.angleTo(toTarget);
        
        if (angle > tolerance) {
          converged = false;
          
          // 计算旋转轴
          const axis = new Vector3().crossVectors(toEnd, toTarget).normalize();
          
          // 应用旋转
          const rotation = new Quaternion().setFromAxisAngle(axis, angle);
          bone.quaternion.multiplyQuaternions(rotation, bone.quaternion);
        }
      }

      if (converged) {
        return true;
      }
    }

    return false;
  }

  /**
   * FABRIK求解器
   */
  private solveFABRIK(): boolean {
    // FABRIK算法的简化实现
    const { bones, target } = this.chain;
    
    // 前向传递
    bones[bones.length - 1].position.copy(target);
    
    for (let i = bones.length - 2; i >= 0; i--) {
      const currentBone = bones[i];
      const nextBone = bones[i + 1];
      const distance = currentBone.position.distanceTo(nextBone.position);
      
      const direction = new Vector3().subVectors(currentBone.position, nextBone.position).normalize();
      currentBone.position.copy(nextBone.position).add(direction.multiplyScalar(distance));
    }
    
    // 后向传递
    // 这里应该实现完整的FABRIK后向传递
    
    return true;
  }

  /**
   * 双骨骼求解器
   */
  private solveTwoBone(): boolean {
    if (this.chain.bones.length !== 3) {
      return false;
    }

    const [root, middle, end] = this.chain.bones;
    const { target } = this.chain;

    // 计算骨骼长度
    const upperLength = root.position.distanceTo(middle.position);
    const lowerLength = middle.position.distanceTo(end.position);
    const targetDistance = root.position.distanceTo(target);

    // 检查目标是否可达
    if (targetDistance > upperLength + lowerLength) {
      // 目标太远，伸直骨骼链
      const direction = new Vector3().subVectors(target, root.position).normalize();
      middle.position.copy(root.position).add(direction.clone().multiplyScalar(upperLength));
      end.position.copy(target);
      return true;
    }

    // 使用余弦定理计算角度
    const cosAngle = (upperLength * upperLength + lowerLength * lowerLength - targetDistance * targetDistance) / 
                     (2 * upperLength * lowerLength);
    const angle = Math.acos(Math.max(-1, Math.min(1, cosAngle)));

    // 计算中间关节位置
    const toTarget = new Vector3().subVectors(target, root.position).normalize();
    const middleDistance = Math.sqrt(upperLength * upperLength + targetDistance * targetDistance - 
                                   2 * upperLength * targetDistance * Math.cos(Math.PI - angle));
    
    middle.position.copy(root.position).add(toTarget.multiplyScalar(middleDistance));
    end.position.copy(target);

    return true;
  }

  /**
   * 更新求解器
   */
  update(_deltaTime: number): void {
    if (this.enabled) {
      this.solve();
    }
  }
}

/**
 * 动画混合树类
 */
class AnimationBlendTree {
  public id: string;
  public nodes: Map<string, BlendNode> = new Map();
  public rootNode: string = '';
  public enabled: boolean = true;

  constructor(id: string) {
    this.id = id;
  }

  /**
   * 添加混合节点
   */
  addNode(id: string, node: BlendNode): void {
    this.nodes.set(id, node);
    
    if (this.nodes.size === 1) {
      this.rootNode = id;
    }
  }

  /**
   * 设置根节点
   */
  setRootNode(nodeId: string): void {
    if (this.nodes.has(nodeId)) {
      this.rootNode = nodeId;
    }
  }

  /**
   * 评估混合树
   */
  evaluate(): any {
    if (!this.rootNode || !this.enabled) {
      return null;
    }

    const rootNode = this.nodes.get(this.rootNode);
    return rootNode ? rootNode.evaluate() : null;
  }

  /**
   * 更新混合树
   */
  update(_deltaTime: number): void {
    if (this.enabled) {
      this.evaluate();
    }
  }
}

/**
 * 混合节点基类
 */
abstract class BlendNode {
  public id: string;
  public weight: number = 1.0;

  constructor(id: string) {
    this.id = id;
  }

  abstract evaluate(): any;
}

// 注释掉未使用的类以避免编译警告
// 这些类可能在未来的版本中使用

// /**
//  * 动画剪辑节点
//  */
// class AnimationClipNode extends BlendNode {
//   public clip: any;
//   public speed: number = 1.0;

//   constructor(id: string, clip: any) {
//     super(id);
//     this.clip = clip;
//   }

//   evaluate(): any {
//     return {
//       type: 'clip',
//       clip: this.clip,
//       weight: this.weight,
//       speed: this.speed
//     };
//   }
// }

// /**
//  * 混合节点
//  */
// class BlendNode2D extends BlendNode {
//   public inputX: BlendNode | null = null;
//   public inputY: BlendNode | null = null;
//   public parameterX: string = '';
//   public parameterY: string = '';

//   constructor(id: string) {
//     super(id);
//   }

//   evaluate(): any {
//     const resultX = this.inputX ? this.inputX.evaluate() : null;
//     const resultY = this.inputY ? this.inputY.evaluate() : null;

//     return {
//       type: 'blend2d',
//       inputX: resultX,
//       inputY: resultY,
//       weight: this.weight,
//       parameterX: this.parameterX,
//       parameterY: this.parameterY
//     };
//   }
// }

/**
 * 动画状态机节点
 */
export class AnimationStateMachineNode extends VisualScriptNode {
  public static readonly TYPE = 'AnimationStateMachine';
  public static readonly NAME = '动画状态机';
  public static readonly DESCRIPTION = '创建和管理动画状态机';

  private static animationManager: AdvancedAnimationManager = new AdvancedAnimationManager();

  constructor(nodeType: string = AnimationStateMachineNode.TYPE, name: string = AnimationStateMachineNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建状态机');
    this.addInput('addState', 'trigger', '添加状态');
    this.addInput('addTransition', 'trigger', '添加转换');
    this.addInput('playState', 'trigger', '播放状态');
    this.addInput('setParameter', 'trigger', '设置参数');
    this.addInput('stateMachineId', 'string', '状态机ID');
    this.addInput('target', 'object', '目标对象');
    this.addInput('layerName', 'string', '层名称');
    this.addInput('stateName', 'string', '状态名称');
    this.addInput('animationClip', 'object', '动画剪辑');
    this.addInput('crossFadeDuration', 'number', '交叉淡入时间');
    this.addInput('parameterName', 'string', '参数名称');
    this.addInput('parameterValue', 'any', '参数值');

    // 输出端口
    this.addOutput('stateMachine', 'object', '状态机');
    this.addOutput('stateMachineId', 'string', '状态机ID');
    this.addOutput('currentState', 'string', '当前状态');
    this.addOutput('currentLayer', 'string', '当前层');
    this.addOutput('isPlaying', 'boolean', '是否播放中');
    this.addOutput('onCreated', 'trigger', '状态机创建完成');
    this.addOutput('onStateAdded', 'trigger', '状态添加完成');
    this.addOutput('onTransitionAdded', 'trigger', '转换添加完成');
    this.addOutput('onStateChanged', 'trigger', '状态改变');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const addStateTrigger = inputs?.addState;
      const addTransitionTrigger = inputs?.addTransition;
      const playStateTrigger = inputs?.playState;
      const setParameterTrigger = inputs?.setParameter;

      if (createTrigger) {
        return this.createStateMachine(inputs);
      } else if (addStateTrigger) {
        return this.addState(inputs);
      } else if (addTransitionTrigger) {
        return this.addTransition(inputs);
      } else if (playStateTrigger) {
        return this.playState(inputs);
      } else if (setParameterTrigger) {
        return this.setParameter(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('AnimationStateMachineNode', '动画状态机操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createStateMachine(inputs: any): any {
    const stateMachineId = inputs?.stateMachineId as string || this.generateStateMachineId();
    const target = inputs?.target as Object3D;

    if (!target) {
      throw new Error('未提供目标对象');
    }

    const stateMachine = AnimationStateMachineNode.animationManager.createStateMachine(stateMachineId, target);

    Debug.log('AnimationStateMachineNode', `动画状态机创建成功: ${stateMachineId}`);

    return {
      stateMachine,
      stateMachineId,
      currentState: '',
      currentLayer: 'Base',
      isPlaying: false,
      onCreated: true,
      onStateAdded: false,
      onTransitionAdded: false,
      onStateChanged: false,
      onError: false
    };
  }

  private addState(inputs: any): any {
    const stateMachineId = inputs?.stateMachineId as string;
    const layerName = inputs?.layerName as string || 'Base';
    const stateName = inputs?.stateName as string;
    const animationClip = inputs?.animationClip;

    if (!stateMachineId || !stateName) {
      throw new Error('未提供状态机ID或状态名称');
    }

    const stateMachine = AnimationStateMachineNode.animationManager.getStateMachine(stateMachineId);
    if (!stateMachine) {
      throw new Error('状态机不存在');
    }

    const stateInfo: AnimationStateInfo = {
      name: stateName,
      clip: animationClip,
      weight: 1.0,
      speed: 1.0,
      loop: true,
      clampWhenFinished: false,
      crossFadeDuration: 0.25,
      enabled: true
    };

    stateMachine.addState(layerName, stateName, stateInfo);

    Debug.log('AnimationStateMachineNode', `状态添加成功: ${stateName} 到层 ${layerName}`);

    return {
      stateMachine,
      stateMachineId,
      currentState: stateMachine.layers.get(layerName)?.currentState || '',
      currentLayer: layerName,
      isPlaying: true,
      onCreated: false,
      onStateAdded: true,
      onTransitionAdded: false,
      onStateChanged: false,
      onError: false
    };
  }

  private addTransition(inputs: any): any {
    const stateMachineId = inputs?.stateMachineId as string;
    const fromState = inputs?.fromState as string;
    const toState = inputs?.toState as string;
    const condition = inputs?.condition as string || 'true';
    const duration = inputs?.crossFadeDuration as number || 0.25;

    if (!stateMachineId || !fromState || !toState) {
      throw new Error('未提供必要的转换参数');
    }

    const stateMachine = AnimationStateMachineNode.animationManager.getStateMachine(stateMachineId);
    if (!stateMachine) {
      throw new Error('状态机不存在');
    }

    const transition: StateTransition = {
      from: fromState,
      to: toState,
      condition,
      duration,
      type: TransitionType.SMOOTH,
      hasExitTime: false,
      exitTime: 0.9,
      interruptible: true
    };

    stateMachine.addTransition(transition);

    Debug.log('AnimationStateMachineNode', `转换添加成功: ${fromState} -> ${toState}`);

    return {
      stateMachine,
      stateMachineId,
      currentState: stateMachine.layers.get(stateMachine.currentLayer)?.currentState || '',
      currentLayer: stateMachine.currentLayer,
      isPlaying: true,
      onCreated: false,
      onStateAdded: false,
      onTransitionAdded: true,
      onStateChanged: false,
      onError: false
    };
  }

  private playState(inputs: any): any {
    const stateMachineId = inputs?.stateMachineId as string;
    const layerName = inputs?.layerName as string || 'Base';
    const stateName = inputs?.stateName as string;
    const crossFadeDuration = inputs?.crossFadeDuration as number || 0.25;

    if (!stateMachineId || !stateName) {
      throw new Error('未提供状态机ID或状态名称');
    }

    const stateMachine = AnimationStateMachineNode.animationManager.getStateMachine(stateMachineId);
    if (!stateMachine) {
      throw new Error('状态机不存在');
    }

    const success = stateMachine.playState(layerName, stateName, crossFadeDuration);
    if (!success) {
      throw new Error('状态播放失败');
    }

    Debug.log('AnimationStateMachineNode', `状态播放: ${stateName}`);

    return {
      stateMachine,
      stateMachineId,
      currentState: stateName,
      currentLayer: layerName,
      isPlaying: true,
      onCreated: false,
      onStateAdded: false,
      onTransitionAdded: false,
      onStateChanged: true,
      onError: false
    };
  }

  private setParameter(inputs: any): any {
    const stateMachineId = inputs?.stateMachineId as string;
    const parameterName = inputs?.parameterName as string;
    const parameterValue = inputs?.parameterValue;

    if (!stateMachineId || !parameterName) {
      throw new Error('未提供状态机ID或参数名称');
    }

    const stateMachine = AnimationStateMachineNode.animationManager.getStateMachine(stateMachineId);
    if (!stateMachine) {
      throw new Error('状态机不存在');
    }

    stateMachine.setParameter(parameterName, parameterValue);

    Debug.log('AnimationStateMachineNode', `参数设置: ${parameterName} = ${parameterValue}`);

    return {
      stateMachine,
      stateMachineId,
      currentState: stateMachine.layers.get(stateMachine.currentLayer)?.currentState || '',
      currentLayer: stateMachine.currentLayer,
      isPlaying: true,
      onCreated: false,
      onStateAdded: false,
      onTransitionAdded: false,
      onStateChanged: false,
      onError: false
    };
  }

  private generateStateMachineId(): string {
    return 'sm_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      stateMachine: null,
      stateMachineId: '',
      currentState: '',
      currentLayer: '',
      isPlaying: false,
      onCreated: false,
      onStateAdded: false,
      onTransitionAdded: false,
      onStateChanged: false,
      onError: false
    };
  }
}

/**
 * 动画混合节点
 */
export class AnimationBlendNode extends VisualScriptNode {
  public static readonly TYPE = 'AnimationBlend';
  public static readonly NAME = '动画混合';
  public static readonly DESCRIPTION = '混合多个动画';

  // private static animationManager: AdvancedAnimationManager = new AdvancedAnimationManager();

  constructor(nodeType: string = AnimationBlendNode.TYPE, name: string = AnimationBlendNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('blend', 'trigger', '执行混合');
    this.addInput('animation1', 'object', '动画1');
    this.addInput('animation2', 'object', '动画2');
    this.addInput('blendWeight', 'number', '混合权重');
    this.addInput('blendMode', 'string', '混合模式');
    this.addInput('target', 'object', '目标对象');

    // 输出端口
    this.addOutput('blendedAnimation', 'object', '混合后动画');
    this.addOutput('weight1', 'number', '动画1权重');
    this.addOutput('weight2', 'number', '动画2权重');
    this.addOutput('onBlended', 'trigger', '混合完成');
    this.addOutput('onError', 'trigger', '混合失败');
  }

  public execute(inputs?: any): any {
    try {
      const blendTrigger = inputs?.blend;
      if (!blendTrigger) {
        return this.getDefaultOutputs();
      }

      const animation1 = inputs?.animation1;
      const animation2 = inputs?.animation2;
      const blendWeight = inputs?.blendWeight as number ?? 0.5;
      const blendMode = inputs?.blendMode as string || 'linear';
      // const target = inputs?.target as Object3D;

      if (!animation1 || !animation2) {
        throw new Error('未提供足够的动画输入');
      }

      // 计算混合权重
      const weight1 = 1.0 - blendWeight;
      const weight2 = blendWeight;

      // 执行动画混合
      const blendedAnimation = this.blendAnimations(animation1, animation2, weight1, weight2, blendMode);

      Debug.log('AnimationBlendNode', `动画混合完成: 权重 ${weight1.toFixed(2)}/${weight2.toFixed(2)}`);

      return {
        blendedAnimation,
        weight1,
        weight2,
        onBlended: true,
        onError: false
      };

    } catch (error) {
      Debug.error('AnimationBlendNode', '动画混合失败', error);
      return {
        blendedAnimation: null,
        weight1: 0,
        weight2: 0,
        onBlended: false,
        onError: true
      };
    }
  }

  private blendAnimations(anim1: any, anim2: any, weight1: number, weight2: number, mode: string): any {
    // 简化的动画混合实现
    return {
      type: 'blended',
      animation1: anim1,
      animation2: anim2,
      weight1,
      weight2,
      blendMode: mode,
      timestamp: Date.now()
    };
  }

  private getDefaultOutputs(): any {
    return {
      blendedAnimation: null,
      weight1: 0,
      weight2: 0,
      onBlended: false,
      onError: false
    };
  }
}

/**
 * IK系统节点
 */
export class IKSystemNode extends VisualScriptNode {
  public static readonly TYPE = 'IKSystem';
  public static readonly NAME = 'IK系统';
  public static readonly DESCRIPTION = '反向运动学求解器';

  private static animationManager: AdvancedAnimationManager = new AdvancedAnimationManager();

  constructor(nodeType: string = IKSystemNode.TYPE, name: string = IKSystemNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建IK链');
    this.addInput('solve', 'trigger', '求解IK');
    this.addInput('setTarget', 'trigger', '设置目标');
    this.addInput('ikId', 'string', 'IK求解器ID');
    this.addInput('bones', 'array', '骨骼链');
    this.addInput('target', 'object', '目标位置');
    this.addInput('poleTarget', 'object', '极向量目标');
    this.addInput('solverType', 'string', '求解器类型');
    this.addInput('iterations', 'number', '迭代次数');
    this.addInput('tolerance', 'number', '容差');
    this.addInput('weight', 'number', 'IK权重');

    // 输出端口
    this.addOutput('ikSolver', 'object', 'IK求解器');
    this.addOutput('ikId', 'string', 'IK求解器ID');
    this.addOutput('solved', 'boolean', '是否求解成功');
    this.addOutput('error', 'number', '求解误差');
    this.addOutput('iterations', 'number', '实际迭代次数');
    this.addOutput('onCreated', 'trigger', 'IK链创建完成');
    this.addOutput('onSolved', 'trigger', 'IK求解完成');
    this.addOutput('onTargetSet', 'trigger', '目标设置完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const solveTrigger = inputs?.solve;
      const setTargetTrigger = inputs?.setTarget;

      if (createTrigger) {
        return this.createIKChain(inputs);
      } else if (solveTrigger) {
        return this.solveIK(inputs);
      } else if (setTargetTrigger) {
        return this.setTarget(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('IKSystemNode', 'IK系统操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createIKChain(inputs: any): any {
    const ikId = inputs?.ikId as string || this.generateIKId();
    const bones = inputs?.bones as Bone[] || [];
    const target = inputs?.target as Vector3 || new Vector3();
    const poleTarget = inputs?.poleTarget as Vector3;
    const solverType = inputs?.solverType as string || 'ccd';
    const iterations = inputs?.iterations as number || 10;
    const tolerance = inputs?.tolerance as number || 0.01;
    const weight = inputs?.weight as number || 1.0;

    if (bones.length < 2) {
      throw new Error('IK链至少需要2个骨骼');
    }

    const ikChain: IKChain = {
      id: ikId,
      bones,
      target,
      poleTarget,
      iterations,
      tolerance,
      solver: solverType as IKSolverType,
      weight,
      enabled: true
    };

    const ikSolver = IKSystemNode.animationManager.createIKSolver(ikId, ikChain);

    Debug.log('IKSystemNode', `IK链创建成功: ${ikId} (${bones.length}个骨骼)`);

    return {
      ikSolver,
      ikId,
      solved: false,
      error: 0,
      iterations: 0,
      onCreated: true,
      onSolved: false,
      onTargetSet: false,
      onError: false
    };
  }

  private solveIK(inputs: any): any {
    const ikId = inputs?.ikId as string;

    if (!ikId) {
      throw new Error('未提供IK求解器ID');
    }

    const ikSolver = IKSystemNode.animationManager.getIKSolver(ikId);
    if (!ikSolver) {
      throw new Error('IK求解器不存在');
    }

    const solved = ikSolver.solve();
    const error = this.calculateIKError(ikSolver);

    Debug.log('IKSystemNode', `IK求解${solved ? '成功' : '失败'}: ${ikId}, 误差=${error.toFixed(4)}`);

    return {
      ikSolver,
      ikId,
      solved,
      error,
      iterations: ikSolver.chain.iterations,
      onCreated: false,
      onSolved: true,
      onTargetSet: false,
      onError: !solved
    };
  }

  private setTarget(inputs: any): any {
    const ikId = inputs?.ikId as string;
    const target = inputs?.target as Vector3;

    if (!ikId) {
      throw new Error('未提供IK求解器ID');
    }

    if (!target) {
      throw new Error('未提供目标位置');
    }

    const ikSolver = IKSystemNode.animationManager.getIKSolver(ikId);
    if (!ikSolver) {
      throw new Error('IK求解器不存在');
    }

    ikSolver.chain.target.copy(target);

    Debug.log('IKSystemNode', `IK目标设置: ${ikId}`);

    return {
      ikSolver,
      ikId,
      solved: false,
      error: 0,
      iterations: 0,
      onCreated: false,
      onSolved: false,
      onTargetSet: true,
      onError: false
    };
  }

  private calculateIKError(ikSolver: IKSolver): number {
    const { bones, target } = ikSolver.chain;
    const endEffector = bones[bones.length - 1];
    return endEffector.position.distanceTo(target);
  }

  private generateIKId(): string {
    return 'ik_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      ikSolver: null,
      ikId: '',
      solved: false,
      error: 0,
      iterations: 0,
      onCreated: false,
      onSolved: false,
      onTargetSet: false,
      onError: false
    };
  }
}

/**
 * 动画事件节点
 */
export class AnimationEventNode extends VisualScriptNode {
  public static readonly TYPE = 'AnimationEvent';
  public static readonly NAME = '动画事件';
  public static readonly DESCRIPTION = '处理动画事件和回调';

  constructor(nodeType: string = AnimationEventNode.TYPE, name: string = AnimationEventNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('addEvent', 'trigger', '添加事件');
    this.addInput('removeEvent', 'trigger', '移除事件');
    this.addInput('triggerEvent', 'trigger', '触发事件');
    this.addInput('animationId', 'string', '动画ID');
    this.addInput('eventName', 'string', '事件名称');
    this.addInput('eventTime', 'number', '事件时间');
    this.addInput('eventData', 'any', '事件数据');

    // 输出端口
    this.addOutput('eventTriggered', 'boolean', '事件已触发');
    this.addOutput('eventName', 'string', '事件名称');
    this.addOutput('eventTime', 'number', '事件时间');
    this.addOutput('eventData', 'any', '事件数据');
    this.addOutput('onEventAdded', 'trigger', '事件添加完成');
    this.addOutput('onEventRemoved', 'trigger', '事件移除完成');
    this.addOutput('onEventTriggered', 'trigger', '事件触发');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const addEventTrigger = inputs?.addEvent;
      const removeEventTrigger = inputs?.removeEvent;
      const triggerEventTrigger = inputs?.triggerEvent;

      if (addEventTrigger) {
        return this.addEvent(inputs);
      } else if (removeEventTrigger) {
        return this.removeEvent(inputs);
      } else if (triggerEventTrigger) {
        return this.triggerEvent(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('AnimationEventNode', '动画事件操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private addEvent(inputs: any): any {
    const animationId = inputs?.animationId as string;
    const eventName = inputs?.eventName as string;
    const eventTime = inputs?.eventTime as number || 0;
    const eventData = inputs?.eventData;

    if (!animationId || !eventName) {
      throw new Error('未提供动画ID或事件名称');
    }

    // 这里应该将事件添加到动画系统中
    Debug.log('AnimationEventNode', `动画事件添加: ${eventName} 在时间 ${eventTime}`);

    return {
      eventTriggered: false,
      eventName,
      eventTime,
      eventData,
      onEventAdded: true,
      onEventRemoved: false,
      onEventTriggered: false,
      onError: false
    };
  }

  private removeEvent(inputs: any): any {
    const animationId = inputs?.animationId as string;
    const eventName = inputs?.eventName as string;

    if (!animationId || !eventName) {
      throw new Error('未提供动画ID或事件名称');
    }

    // 这里应该从动画系统中移除事件
    Debug.log('AnimationEventNode', `动画事件移除: ${eventName}`);

    return {
      eventTriggered: false,
      eventName,
      eventTime: 0,
      eventData: null,
      onEventAdded: false,
      onEventRemoved: true,
      onEventTriggered: false,
      onError: false
    };
  }

  private triggerEvent(inputs: any): any {
    const eventName = inputs?.eventName as string;
    const eventTime = inputs?.eventTime as number || 0;
    const eventData = inputs?.eventData;

    if (!eventName) {
      throw new Error('未提供事件名称');
    }

    Debug.log('AnimationEventNode', `动画事件触发: ${eventName}`);

    return {
      eventTriggered: true,
      eventName,
      eventTime,
      eventData,
      onEventAdded: false,
      onEventRemoved: false,
      onEventTriggered: true,
      onError: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      eventTriggered: false,
      eventName: '',
      eventTime: 0,
      eventData: null,
      onEventAdded: false,
      onEventRemoved: false,
      onEventTriggered: false,
      onError: false
    };
  }
}
