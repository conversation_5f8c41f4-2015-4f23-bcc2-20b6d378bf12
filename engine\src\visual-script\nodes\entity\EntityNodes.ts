/**
 * 实体节点集合
 * 提供实体创建、查询、管理等功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Quaternion } from 'three';

/**
 * 实体接口
 */
export interface Entity {
  id: string;
  name: string;
  active: boolean;
  transform: {
    position: Vector3;
    rotation: Quaternion;
    scale: Vector3;
  };
  components: Map<string, any>;
  children: Entity[];
  parent?: Entity;
  tags: Set<string>;
  layer: number;
}

/**
 * 创建实体节点
 */
export class CreateEntityNode extends VisualScriptNode {
  public static readonly TYPE = 'CreateEntity';
  public static readonly NAME = '创建实体';
  public static readonly DESCRIPTION = '创建新的游戏实体';

  constructor(nodeType: string = CreateEntityNode.TYPE, name: string = CreateEntityNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('name', 'string', '实体名称');
    this.addInput('position', 'object', '位置');
    this.addInput('rotation', 'object', '旋转');
    this.addInput('scale', 'object', '缩放');
    this.addInput('parent', 'object', '父实体');
    this.addInput('tags', 'array', '标签');
    this.addInput('layer', 'number', '层级');

    // 输出端口
    this.addOutput('entity', 'object', '创建的实体');
    this.addOutput('entityId', 'string', '实体ID');
    this.addOutput('success', 'boolean', '创建成功');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const name = inputs?.name as string || 'Entity';
      const position = inputs?.position as Vector3 || new Vector3(0, 0, 0);
      const rotation = inputs?.rotation as Quaternion || new Quaternion(0, 0, 0, 1);
      const scale = inputs?.scale as Vector3 || new Vector3(1, 1, 1);
      const parent = inputs?.parent as Entity;
      const tags = inputs?.tags as string[] || [];
      const layer = inputs?.layer as number || 0;

      // 创建新实体
      const entity: Entity = {
        id: this.generateEntityId(),
        name,
        active: true,
        transform: {
          position: position.clone(),
          rotation: rotation.clone(),
          scale: scale.clone()
        },
        components: new Map(),
        children: [],
        tags: new Set(tags),
        layer
      };

      // 设置父子关系
      if (parent) {
        entity.parent = parent;
        parent.children.push(entity);
      }

      Debug.log('CreateEntityNode', `实体创建成功: ${entity.name} (${entity.id})`);

      return {
        entity,
        entityId: entity.id,
        success: true,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('CreateEntityNode', '创建实体失败', error);
      return {
        entity: null,
        entityId: '',
        success: false,
        onCreated: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      entity: null,
      entityId: '',
      success: false,
      onCreated: false,
      onError: false
    };
  }

  private generateEntityId(): string {
    return 'entity_' + Math.random().toString(36).substr(2, 9);
  }
}

/**
 * 查找实体节点
 */
export class FindEntityNode extends VisualScriptNode {
  public static readonly TYPE = 'FindEntity';
  public static readonly NAME = '查找实体';
  public static readonly DESCRIPTION = '根据条件查找实体';

  constructor(nodeType: string = FindEntityNode.TYPE, name: string = FindEntityNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('find', 'trigger', '查找');
    this.addInput('entityId', 'string', '实体ID');
    this.addInput('entityName', 'string', '实体名称');
    this.addInput('tag', 'string', '标签');
    this.addInput('layer', 'number', '层级');
    this.addInput('parent', 'object', '父实体');

    // 输出端口
    this.addOutput('entity', 'object', '找到的实体');
    this.addOutput('entities', 'array', '找到的实体列表');
    this.addOutput('found', 'boolean', '是否找到');
    this.addOutput('count', 'number', '找到数量');
    this.addOutput('onFound', 'trigger', '找到实体');
    this.addOutput('onNotFound', 'trigger', '未找到');
  }

  public execute(inputs?: any): any {
    try {
      const findTrigger = inputs?.find;
      if (!findTrigger) {
        return this.getDefaultOutputs();
      }

      const entityId = inputs?.entityId as string;
      const entityName = inputs?.entityName as string;
      const tag = inputs?.tag as string;
      const layer = inputs?.layer as number;
      const parent = inputs?.parent as Entity;

      // 模拟实体查找（实际实现需要访问实体管理器）
      const foundEntities = this.findEntities({
        entityId,
        entityName,
        tag,
        layer,
        parent
      });

      const found = foundEntities.length > 0;
      const entity = found ? foundEntities[0] : null;

      return {
        entity,
        entities: foundEntities,
        found,
        count: foundEntities.length,
        onFound: found,
        onNotFound: !found
      };

    } catch (error) {
      Debug.error('FindEntityNode', '查找实体失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      entity: null,
      entities: [],
      found: false,
      count: 0,
      onFound: false,
      onNotFound: false
    };
  }

  private findEntities(criteria: any): Entity[] {
    // 模拟查找逻辑
    // 实际实现需要访问全局实体管理器
    return [];
  }
}

/**
 * 销毁实体节点
 */
export class DestroyEntityNode extends VisualScriptNode {
  public static readonly TYPE = 'DestroyEntity';
  public static readonly NAME = '销毁实体';
  public static readonly DESCRIPTION = '销毁指定的实体';

  constructor(nodeType: string = DestroyEntityNode.TYPE, name: string = DestroyEntityNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('destroy', 'trigger', '销毁');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('destroyChildren', 'boolean', '销毁子实体');
    this.addInput('delay', 'number', '延迟时间');

    // 输出端口
    this.addOutput('success', 'boolean', '销毁成功');
    this.addOutput('destroyedId', 'string', '被销毁的实体ID');
    this.addOutput('onDestroyed', 'trigger', '销毁完成');
    this.addOutput('onError', 'trigger', '销毁失败');
  }

  public execute(inputs?: any): any {
    try {
      const destroyTrigger = inputs?.destroy;
      if (!destroyTrigger) {
        return this.getDefaultOutputs();
      }

      const entity = inputs?.entity as Entity;
      const destroyChildren = inputs?.destroyChildren as boolean || false;
      const delay = inputs?.delay as number || 0;

      if (!entity) {
        throw new Error('未提供要销毁的实体');
      }

      // 延迟销毁
      if (delay > 0) {
        setTimeout(() => {
          this.performDestroy(entity, destroyChildren);
        }, delay * 1000);
      } else {
        this.performDestroy(entity, destroyChildren);
      }

      return {
        success: true,
        destroyedId: entity.id,
        onDestroyed: true,
        onError: false
      };

    } catch (error) {
      Debug.error('DestroyEntityNode', '销毁实体失败', error);
      return {
        success: false,
        destroyedId: '',
        onDestroyed: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      success: false,
      destroyedId: '',
      onDestroyed: false,
      onError: false
    };
  }

  private performDestroy(entity: Entity, destroyChildren: boolean): void {
    try {
      // 销毁子实体
      if (destroyChildren) {
        for (const child of entity.children) {
          this.performDestroy(child, true);
        }
      }

      // 从父实体中移除
      if (entity.parent) {
        const index = entity.parent.children.indexOf(entity);
        if (index > -1) {
          entity.parent.children.splice(index, 1);
        }
      }

      // 清理组件
      entity.components.clear();
      entity.children = [];
      entity.active = false;

      Debug.log('DestroyEntityNode', `实体已销毁: ${entity.name} (${entity.id})`);

    } catch (error) {
      Debug.error('DestroyEntityNode', '执行销毁失败', error);
      throw error;
    }
  }
}

/**
 * 克隆实体节点
 */
export class CloneEntityNode extends VisualScriptNode {
  public static readonly TYPE = 'CloneEntity';
  public static readonly NAME = '克隆实体';
  public static readonly DESCRIPTION = '克隆现有实体';

  constructor(nodeType: string = CloneEntityNode.TYPE, name: string = CloneEntityNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('clone', 'trigger', '克隆');
    this.addInput('sourceEntity', 'object', '源实体');
    this.addInput('position', 'object', '新位置');
    this.addInput('rotation', 'object', '新旋转');
    this.addInput('scale', 'object', '新缩放');
    this.addInput('cloneChildren', 'boolean', '克隆子实体');
    this.addInput('newName', 'string', '新名称');

    // 输出端口
    this.addOutput('clonedEntity', 'object', '克隆的实体');
    this.addOutput('clonedId', 'string', '克隆实体ID');
    this.addOutput('success', 'boolean', '克隆成功');
    this.addOutput('onCloned', 'trigger', '克隆完成');
    this.addOutput('onError', 'trigger', '克隆失败');
  }

  public execute(inputs?: any): any {
    try {
      const cloneTrigger = inputs?.clone;
      if (!cloneTrigger) {
        return this.getDefaultOutputs();
      }

      const sourceEntity = inputs?.sourceEntity as Entity;
      if (!sourceEntity) {
        throw new Error('未提供源实体');
      }

      const position = inputs?.position as Vector3 || sourceEntity.transform.position.clone();
      const rotation = inputs?.rotation as Quaternion || sourceEntity.transform.rotation.clone();
      const scale = inputs?.scale as Vector3 || sourceEntity.transform.scale.clone();
      const cloneChildren = inputs?.cloneChildren as boolean || false;
      const newName = inputs?.newName as string || `${sourceEntity.name}_Clone`;

      // 执行克隆
      const clonedEntity = this.performClone(sourceEntity, {
        position,
        rotation,
        scale,
        cloneChildren,
        newName
      });

      return {
        clonedEntity,
        clonedId: clonedEntity.id,
        success: true,
        onCloned: true,
        onError: false
      };

    } catch (error) {
      Debug.error('CloneEntityNode', '克隆实体失败', error);
      return {
        clonedEntity: null,
        clonedId: '',
        success: false,
        onCloned: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      clonedEntity: null,
      clonedId: '',
      success: false,
      onCloned: false,
      onError: false
    };
  }

  private performClone(source: Entity, options: any): Entity {
    const cloned: Entity = {
      id: this.generateEntityId(),
      name: options.newName,
      active: source.active,
      transform: {
        position: options.position.clone(),
        rotation: options.rotation.clone(),
        scale: options.scale.clone()
      },
      components: new Map(),
      children: [],
      tags: new Set(source.tags),
      layer: source.layer
    };

    // 克隆组件
    for (const [key, component] of source.components) {
      cloned.components.set(key, this.cloneComponent(component));
    }

    // 克隆子实体
    if (options.cloneChildren) {
      for (const child of source.children) {
        const clonedChild = this.performClone(child, {
          position: child.transform.position.clone(),
          rotation: child.transform.rotation.clone(),
          scale: child.transform.scale.clone(),
          cloneChildren: true,
          newName: `${child.name}_Clone`
        });
        clonedChild.parent = cloned;
        cloned.children.push(clonedChild);
      }
    }

    Debug.log('CloneEntityNode', `实体克隆成功: ${cloned.name} (${cloned.id})`);
    return cloned;
  }

  private cloneComponent(component: any): any {
    // 简单的深拷贝实现
    return JSON.parse(JSON.stringify(component));
  }

  private generateEntityId(): string {
    return 'entity_' + Math.random().toString(36).substr(2, 9);
  }
}

/**
 * 实体激活状态节点
 */
export class EntityActiveNode extends VisualScriptNode {
  public static readonly TYPE = 'EntityActive';
  public static readonly NAME = '实体激活状态';
  public static readonly DESCRIPTION = '控制实体的激活状态';

  constructor(nodeType: string = EntityActiveNode.TYPE, name: string = EntityActiveNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('entity', 'object', '目标实体');
    this.addInput('setActive', 'trigger', '设置激活');
    this.addInput('active', 'boolean', '激活状态');
    this.addInput('includeChildren', 'boolean', '包含子实体');

    // 输出端口
    this.addOutput('isActive', 'boolean', '当前激活状态');
    this.addOutput('entity', 'object', '实体');
    this.addOutput('onActivated', 'trigger', '激活时');
    this.addOutput('onDeactivated', 'trigger', '停用时');
    this.addOutput('onChanged', 'trigger', '状态改变');
  }

  public execute(inputs?: any): any {
    try {
      const entity = inputs?.entity as Entity;
      const setActiveTrigger = inputs?.setActive;
      const active = inputs?.active as boolean;
      const includeChildren = inputs?.includeChildren as boolean || false;

      if (!entity) {
        return {
          isActive: false,
          entity: null,
          onActivated: false,
          onDeactivated: false,
          onChanged: false
        };
      }

      let stateChanged = false;

      // 设置激活状态
      if (setActiveTrigger && active !== undefined) {
        const oldActive = entity.active;
        entity.active = active;
        stateChanged = oldActive !== active;

        // 递归设置子实体状态
        if (includeChildren) {
          this.setChildrenActive(entity, active);
        }

        Debug.log('EntityActiveNode', `实体激活状态已更改: ${entity.name} -> ${active}`);
      }

      return {
        isActive: entity.active,
        entity,
        onActivated: stateChanged && entity.active,
        onDeactivated: stateChanged && !entity.active,
        onChanged: stateChanged
      };

    } catch (error) {
      Debug.error('EntityActiveNode', '设置实体激活状态失败', error);
      return {
        isActive: false,
        entity: null,
        onActivated: false,
        onDeactivated: false,
        onChanged: false
      };
    }
  }

  private setChildrenActive(entity: Entity, active: boolean): void {
    for (const child of entity.children) {
      child.active = active;
      this.setChildrenActive(child, active);
    }
  }
}
