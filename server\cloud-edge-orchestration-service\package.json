{"name": "cloud-edge-orchestration-service", "version": "1.0.0", "description": "云边协同编排服务 - 混合云部署、边缘计算网络、5G网络应用", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "start:dev": "ts-node src/main.ts", "start:debug": "ts-node --inspect src/main.ts", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@nestjs/common": "^9.4.3", "@nestjs/core": "^9.4.3", "@nestjs/platform-express": "^9.4.3", "@nestjs/websockets": "^9.4.3", "@nestjs/platform-socket.io": "^9.4.3", "@nestjs/typeorm": "^9.0.1", "@nestjs/config": "^2.3.4", "@nestjs/schedule": "^2.2.3", "@nestjs/swagger": "^6.3.0", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "redis": "^4.6.7", "socket.io": "^4.7.2", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "rxjs": "^7.8.1", "reflect-metadata": "^0.1.13", "uuid": "^9.0.0", "moment": "^2.29.4", "lodash": "^4.17.21", "kubernetes": "^0.18.1", "docker": "^1.0.0", "aws-sdk": "^2.1467.0", "azure-sdk": "^1.0.0", "google-cloud": "^0.60.0", "alibaba-cloud": "^1.0.0", "openstack": "^1.0.0", "vmware-vsphere": "^1.0.0", "edge-computing": "^1.0.0", "5g-network": "^1.0.0", "network-slicing": "^1.0.0", "load-balancer": "^1.0.0", "service-mesh": "^1.0.0", "istio": "^1.0.0", "envoy": "^1.0.0"}, "devDependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/uuid": "^9.0.2", "@types/lodash": "^4.14.195", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}