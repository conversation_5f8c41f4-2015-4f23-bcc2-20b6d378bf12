/**
 * UI界面节点集合
 * 提供用户界面创建、交互、布局等功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector2, Vector3, Color } from 'three';

/**
 * UI元素类型枚举
 */
export enum UIElementType {
  BUTTON = 'button',
  TEXT = 'text',
  INPUT = 'input',
  SLIDER = 'slider',
  TOGGLE = 'toggle',
  DROPDOWN = 'dropdown',
  PANEL = 'panel',
  WINDOW = 'window',
  IMAGE = 'image',
  PROGRESS_BAR = 'progressBar',
  SCROLL_VIEW = 'scrollView',
  TAB_VIEW = 'tabView',
  LIST_VIEW = 'listView',
  GRID_VIEW = 'gridView'
}

/**
 * UI布局类型枚举
 */
export enum UILayoutType {
  ABSOLUTE = 'absolute',
  RELATIVE = 'relative',
  FLEX = 'flex',
  GRID = 'grid',
  STACK = 'stack'
}

/**
 * UI对齐方式枚举
 */
export enum UIAlignment {
  TOP_LEFT = 'topLeft',
  TOP_CENTER = 'topCenter',
  TOP_RIGHT = 'topRight',
  MIDDLE_LEFT = 'middleLeft',
  MIDDLE_CENTER = 'middleCenter',
  MIDDLE_RIGHT = 'middleRight',
  BOTTOM_LEFT = 'bottomLeft',
  BOTTOM_CENTER = 'bottomCenter',
  BOTTOM_RIGHT = 'bottomRight'
}

/**
 * UI元素接口
 */
export interface UIElement {
  id: string;
  type: UIElementType;
  name: string;
  position: Vector2;
  size: Vector2;
  rotation: number;
  scale: Vector2;
  visible: boolean;
  interactive: boolean;
  parent?: string;
  children: string[];
  style: UIStyle;
  properties: { [key: string]: any };
  events: UIEventHandlers;
}

/**
 * UI样式接口
 */
export interface UIStyle {
  backgroundColor: Color;
  borderColor: Color;
  borderWidth: number;
  borderRadius: number;
  opacity: number;
  fontSize: number;
  fontColor: Color;
  fontFamily: string;
  textAlign: 'left' | 'center' | 'right';
  padding: { top: number; right: number; bottom: number; left: number };
  margin: { top: number; right: number; bottom: number; left: number };
  shadow: {
    enabled: boolean;
    color: Color;
    offset: Vector2;
    blur: number;
  };
}

/**
 * UI事件处理器接口
 */
export interface UIEventHandlers {
  onClick?: Function;
  onHover?: Function;
  onFocus?: Function;
  onBlur?: Function;
  onChange?: Function;
  onDrag?: Function;
  onDrop?: Function;
}

/**
 * UI布局配置接口
 */
export interface UILayoutConfig {
  type: UILayoutType;
  alignment: UIAlignment;
  spacing: number;
  padding: number;
  wrap: boolean;
  direction: 'row' | 'column';
  justifyContent: 'start' | 'center' | 'end' | 'spaceBetween' | 'spaceAround';
  alignItems: 'start' | 'center' | 'end' | 'stretch';
}

/**
 * UI主题接口
 */
export interface UITheme {
  name: string;
  colors: {
    primary: Color;
    secondary: Color;
    background: Color;
    surface: Color;
    text: Color;
    textSecondary: Color;
    border: Color;
    success: Color;
    warning: Color;
    error: Color;
  };
  fonts: {
    primary: string;
    secondary: string;
    monospace: string;
  };
  sizes: {
    small: number;
    medium: number;
    large: number;
    extraLarge: number;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
}

/**
 * 高级UI管理器
 */
class AdvancedUIManager {
  private elements: Map<string, UIElement> = new Map();
  private layouts: Map<string, UILayoutConfig> = new Map();
  private themes: Map<string, UITheme> = new Map();
  private currentTheme: string = 'default';
  private eventListeners: Map<string, Function[]> = new Map();
  private canvas: HTMLCanvasElement | null = null;
  private context: CanvasRenderingContext2D | null = null;

  constructor() {
    this.initializeDefaultTheme();
  }

  /**
   * 初始化默认主题
   */
  private initializeDefaultTheme(): void {
    const defaultTheme: UITheme = {
      name: 'default',
      colors: {
        primary: new Color(0x007bff),
        secondary: new Color(0x6c757d),
        background: new Color(0xffffff),
        surface: new Color(0xf8f9fa),
        text: new Color(0x212529),
        textSecondary: new Color(0x6c757d),
        border: new Color(0xdee2e6),
        success: new Color(0x28a745),
        warning: new Color(0xffc107),
        error: new Color(0xdc3545)
      },
      fonts: {
        primary: 'Arial, sans-serif',
        secondary: 'Georgia, serif',
        monospace: 'Courier New, monospace'
      },
      sizes: {
        small: 12,
        medium: 16,
        large: 20,
        extraLarge: 24
      },
      spacing: {
        xs: 4,
        sm: 8,
        md: 16,
        lg: 24,
        xl: 32
      }
    };

    this.themes.set('default', defaultTheme);
  }

  /**
   * 设置画布
   */
  setCanvas(canvas: HTMLCanvasElement): void {
    this.canvas = canvas;
    this.context = canvas.getContext('2d');
  }

  /**
   * 创建UI元素
   */
  createElement(type: UIElementType, name: string, position: Vector2, size: Vector2): UIElement {
    const element: UIElement = {
      id: this.generateElementId(),
      type,
      name,
      position: position.clone(),
      size: size.clone(),
      rotation: 0,
      scale: new Vector2(1, 1),
      visible: true,
      interactive: true,
      children: [],
      style: this.getDefaultStyle(),
      properties: this.getDefaultProperties(type),
      events: {}
    };

    this.elements.set(element.id, element);
    this.emit('elementCreated', { element });

    Debug.log('AdvancedUIManager', `UI元素创建: ${element.id} (${type})`);
    return element;
  }

  /**
   * 删除UI元素
   */
  removeElement(elementId: string): void {
    const element = this.elements.get(elementId);
    if (!element) return;

    // 删除子元素
    for (const childId of element.children) {
      this.removeElement(childId);
    }

    // 从父元素中移除
    if (element.parent) {
      const parent = this.elements.get(element.parent);
      if (parent) {
        const index = parent.children.indexOf(elementId);
        if (index > -1) {
          parent.children.splice(index, 1);
        }
      }
    }

    this.elements.delete(elementId);
    this.emit('elementRemoved', { elementId });

    Debug.log('AdvancedUIManager', `UI元素删除: ${elementId}`);
  }

  /**
   * 设置父子关系
   */
  setParent(childId: string, parentId: string): void {
    const child = this.elements.get(childId);
    const parent = this.elements.get(parentId);

    if (!child || !parent) return;

    // 从旧父元素中移除
    if (child.parent) {
      const oldParent = this.elements.get(child.parent);
      if (oldParent) {
        const index = oldParent.children.indexOf(childId);
        if (index > -1) {
          oldParent.children.splice(index, 1);
        }
      }
    }

    // 设置新的父子关系
    child.parent = parentId;
    if (!parent.children.includes(childId)) {
      parent.children.push(childId);
    }

    this.emit('parentChanged', { childId, parentId });
  }

  /**
   * 更新元素属性
   */
  updateElement(elementId: string, updates: Partial<UIElement>): void {
    const element = this.elements.get(elementId);
    if (!element) return;

    Object.assign(element, updates);
    this.emit('elementUpdated', { element, updates });

    Debug.log('AdvancedUIManager', `UI元素更新: ${elementId}`);
  }

  /**
   * 设置元素样式
   */
  setElementStyle(elementId: string, style: Partial<UIStyle>): void {
    const element = this.elements.get(elementId);
    if (!element) return;

    Object.assign(element.style, style);
    this.emit('styleUpdated', { elementId, style });
  }

  /**
   * 添加事件监听器
   */
  addEventListener(elementId: string, eventType: string, handler: Function): void {
    const element = this.elements.get(elementId);
    if (!element) return;

    element.events[eventType as keyof UIEventHandlers] = handler;
    this.emit('eventListenerAdded', { elementId, eventType });
  }

  /**
   * 触发元素事件
   */
  triggerEvent(elementId: string, eventType: string, eventData?: any): void {
    const element = this.elements.get(elementId);
    if (!element) return;

    const handler = element.events[eventType as keyof UIEventHandlers];
    if (handler) {
      try {
        handler(eventData);
        this.emit('eventTriggered', { elementId, eventType, eventData });
      } catch (error) {
        Debug.error('AdvancedUIManager', `事件处理失败: ${elementId}.${eventType}`, error);
      }
    }
  }

  /**
   * 应用布局
   */
  applyLayout(containerId: string, layoutConfig: UILayoutConfig): void {
    const container = this.elements.get(containerId);
    if (!container) return;

    this.layouts.set(containerId, layoutConfig);

    // 根据布局类型排列子元素
    switch (layoutConfig.type) {
      case UILayoutType.FLEX:
        this.applyFlexLayout(container, layoutConfig);
        break;
      case UILayoutType.GRID:
        this.applyGridLayout(container, layoutConfig);
        break;
      case UILayoutType.STACK:
        this.applyStackLayout(container, layoutConfig);
        break;
    }

    this.emit('layoutApplied', { containerId, layoutConfig });
    Debug.log('AdvancedUIManager', `布局应用: ${containerId} (${layoutConfig.type})`);
  }

  /**
   * 应用Flex布局
   */
  private applyFlexLayout(container: UIElement, config: UILayoutConfig): void {
    const children = container.children.map(id => this.elements.get(id)).filter(Boolean) as UIElement[];
    if (children.length === 0) return;

    let currentPos = config.padding;
    const isRow = config.direction === 'row';

    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      
      if (isRow) {
        child.position.x = currentPos;
        child.position.y = config.padding;
        currentPos += child.size.x + config.spacing;
      } else {
        child.position.x = config.padding;
        child.position.y = currentPos;
        currentPos += child.size.y + config.spacing;
      }
    }
  }

  /**
   * 应用Grid布局
   */
  private applyGridLayout(container: UIElement, config: UILayoutConfig): void {
    const children = container.children.map(id => this.elements.get(id)).filter(Boolean) as UIElement[];
    if (children.length === 0) return;

    const cols = Math.ceil(Math.sqrt(children.length));
    const rows = Math.ceil(children.length / cols);

    const cellWidth = (container.size.x - config.padding * 2 - config.spacing * (cols - 1)) / cols;
    const cellHeight = (container.size.y - config.padding * 2 - config.spacing * (rows - 1)) / rows;

    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      const col = i % cols;
      const row = Math.floor(i / cols);

      child.position.x = config.padding + col * (cellWidth + config.spacing);
      child.position.y = config.padding + row * (cellHeight + config.spacing);
      child.size.x = cellWidth;
      child.size.y = cellHeight;
    }
  }

  /**
   * 应用Stack布局
   */
  private applyStackLayout(container: UIElement, config: UILayoutConfig): void {
    const children = container.children.map(id => this.elements.get(id)).filter(Boolean) as UIElement[];
    if (children.length === 0) return;

    // Stack布局将所有子元素叠放在同一位置
    for (const child of children) {
      child.position.x = config.padding;
      child.position.y = config.padding;
      child.size.x = container.size.x - config.padding * 2;
      child.size.y = container.size.y - config.padding * 2;
    }
  }

  /**
   * 设置主题
   */
  setTheme(themeName: string): void {
    if (this.themes.has(themeName)) {
      this.currentTheme = themeName;
      this.applyThemeToAllElements();
      this.emit('themeChanged', { themeName });
      Debug.log('AdvancedUIManager', `主题切换: ${themeName}`);
    }
  }

  /**
   * 应用主题到所有元素
   */
  private applyThemeToAllElements(): void {
    const theme = this.themes.get(this.currentTheme);
    if (!theme) return;

    for (const element of this.elements.values()) {
      this.applyThemeToElement(element, theme);
    }
  }

  /**
   * 应用主题到单个元素
   */
  private applyThemeToElement(element: UIElement, theme: UITheme): void {
    // 根据元素类型应用相应的主题样式
    switch (element.type) {
      case UIElementType.BUTTON:
        element.style.backgroundColor = theme.colors.primary;
        element.style.fontColor = theme.colors.background;
        break;
      case UIElementType.TEXT:
        element.style.fontColor = theme.colors.text;
        break;
      case UIElementType.PANEL:
        element.style.backgroundColor = theme.colors.surface;
        element.style.borderColor = theme.colors.border;
        break;
    }

    element.style.fontFamily = theme.fonts.primary;
  }

  /**
   * 渲染UI
   */
  render(): void {
    if (!this.context || !this.canvas) return;

    // 清空画布
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // 渲染所有可见元素
    for (const element of this.elements.values()) {
      if (element.visible && !element.parent) {
        this.renderElement(element);
      }
    }
  }

  /**
   * 渲染单个元素
   */
  private renderElement(element: UIElement): void {
    if (!this.context) return;

    this.context.save();

    // 应用变换
    this.context.translate(element.position.x, element.position.y);
    this.context.rotate(element.rotation);
    this.context.scale(element.scale.x, element.scale.y);

    // 渲染元素背景
    if (element.style.backgroundColor) {
      this.context.fillStyle = `#${element.style.backgroundColor.getHexString()}`;
      this.context.fillRect(0, 0, element.size.x, element.size.y);
    }

    // 渲染边框
    if (element.style.borderWidth > 0) {
      this.context.strokeStyle = `#${element.style.borderColor.getHexString()}`;
      this.context.lineWidth = element.style.borderWidth;
      this.context.strokeRect(0, 0, element.size.x, element.size.y);
    }

    // 根据类型渲染内容
    this.renderElementContent(element);

    // 渲染子元素
    for (const childId of element.children) {
      const child = this.elements.get(childId);
      if (child && child.visible) {
        this.renderElement(child);
      }
    }

    this.context.restore();
  }

  /**
   * 渲染元素内容
   */
  private renderElementContent(element: UIElement): void {
    if (!this.context) return;

    switch (element.type) {
      case UIElementType.TEXT:
        this.renderText(element);
        break;
      case UIElementType.BUTTON:
        this.renderButton(element);
        break;
      case UIElementType.PROGRESS_BAR:
        this.renderProgressBar(element);
        break;
    }
  }

  /**
   * 渲染文本
   */
  private renderText(element: UIElement): void {
    if (!this.context) return;

    const text = element.properties.text || '';
    this.context.fillStyle = `#${element.style.fontColor.getHexString()}`;
    this.context.font = `${element.style.fontSize}px ${element.style.fontFamily}`;
    this.context.textAlign = element.style.textAlign as CanvasTextAlign;
    this.context.fillText(text, element.size.x / 2, element.size.y / 2);
  }

  /**
   * 渲染按钮
   */
  private renderButton(element: UIElement): void {
    // 按钮已经在renderElement中渲染了背景，这里只需要渲染文本
    this.renderText(element);
  }

  /**
   * 渲染进度条
   */
  private renderProgressBar(element: UIElement): void {
    if (!this.context) return;

    const progress = Math.max(0, Math.min(1, element.properties.progress || 0));
    const progressWidth = element.size.x * progress;

    // 渲染进度
    this.context.fillStyle = `#${element.style.backgroundColor.getHexString()}`;
    this.context.fillRect(0, 0, progressWidth, element.size.y);
  }

  // 辅助方法
  private getDefaultStyle(): UIStyle {
    const theme = this.themes.get(this.currentTheme)!;
    return {
      backgroundColor: theme.colors.surface,
      borderColor: theme.colors.border,
      borderWidth: 1,
      borderRadius: 4,
      opacity: 1,
      fontSize: theme.sizes.medium,
      fontColor: theme.colors.text,
      fontFamily: theme.fonts.primary,
      textAlign: 'center',
      padding: { top: 8, right: 8, bottom: 8, left: 8 },
      margin: { top: 0, right: 0, bottom: 0, left: 0 },
      shadow: {
        enabled: false,
        color: new Color(0x000000),
        offset: new Vector2(2, 2),
        blur: 4
      }
    };
  }

  private getDefaultProperties(type: UIElementType): { [key: string]: any } {
    switch (type) {
      case UIElementType.TEXT:
        return { text: 'Text' };
      case UIElementType.BUTTON:
        return { text: 'Button' };
      case UIElementType.INPUT:
        return { placeholder: 'Enter text...', value: '' };
      case UIElementType.SLIDER:
        return { min: 0, max: 100, value: 50 };
      case UIElementType.TOGGLE:
        return { checked: false };
      case UIElementType.PROGRESS_BAR:
        return { progress: 0.5 };
      default:
        return {};
    }
  }

  private generateElementId(): string {
    return 'ui_' + Math.random().toString(36).substr(2, 9);
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('AdvancedUIManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 获取元素
   */
  getElement(elementId: string): UIElement | undefined {
    return this.elements.get(elementId);
  }

  /**
   * 获取所有元素
   */
  getAllElements(): UIElement[] {
    return Array.from(this.elements.values());
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.elements.clear();
    this.layouts.clear();
    this.eventListeners.clear();
  }
}

/**
 * UI元素创建节点
 */
export class CreateUIElementNode extends VisualScriptNode {
  public static readonly TYPE = 'CreateUIElement';
  public static readonly NAME = '创建UI元素';
  public static readonly DESCRIPTION = '创建各种类型的UI元素';

  private static uiManager: AdvancedUIManager = new AdvancedUIManager();

  constructor(nodeType: string = CreateUIElementNode.TYPE, name: string = CreateUIElementNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('elementType', 'string', '元素类型');
    this.addInput('name', 'string', '元素名称');
    this.addInput('position', 'object', '位置');
    this.addInput('size', 'object', '尺寸');
    this.addInput('text', 'string', '文本内容');
    this.addInput('visible', 'boolean', '可见性');
    this.addInput('interactive', 'boolean', '可交互');
    this.addInput('parent', 'string', '父元素ID');

    // 输出端口
    this.addOutput('element', 'object', 'UI元素');
    this.addOutput('elementId', 'string', '元素ID');
    this.addOutput('elementType', 'string', '元素类型');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const elementType = inputs?.elementType as string || 'button';
      const name = inputs?.name as string || 'New Element';
      const position = inputs?.position as Vector2 || new Vector2(0, 0);
      const size = inputs?.size as Vector2 || new Vector2(100, 30);
      const text = inputs?.text as string;
      const visible = inputs?.visible as boolean ?? true;
      const interactive = inputs?.interactive as boolean ?? true;
      const parentId = inputs?.parent as string;

      // 创建UI元素
      const element = CreateUIElementNode.uiManager.createElement(
        elementType as UIElementType,
        name,
        position,
        size
      );

      // 设置属性
      element.visible = visible;
      element.interactive = interactive;

      if (text && (elementType === 'text' || elementType === 'button')) {
        element.properties.text = text;
      }

      // 设置父子关系
      if (parentId) {
        CreateUIElementNode.uiManager.setParent(element.id, parentId);
      }

      Debug.log('CreateUIElementNode', `UI元素创建成功: ${element.id} (${elementType})`);

      return {
        element,
        elementId: element.id,
        elementType: element.type,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('CreateUIElementNode', 'UI元素创建失败', error);
      return {
        element: null,
        elementId: '',
        elementType: '',
        onCreated: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      element: null,
      elementId: '',
      elementType: '',
      onCreated: false,
      onError: false
    };
  }
}

/**
 * UI布局节点
 */
export class UILayoutNode extends VisualScriptNode {
  public static readonly TYPE = 'UILayout';
  public static readonly NAME = 'UI布局';
  public static readonly DESCRIPTION = '管理UI元素的布局和排列';

  private static uiManager: AdvancedUIManager = new AdvancedUIManager();

  constructor(nodeType: string = UILayoutNode.TYPE, name: string = UILayoutNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('applyLayout', 'trigger', '应用布局');
    this.addInput('containerId', 'string', '容器ID');
    this.addInput('layoutType', 'string', '布局类型');
    this.addInput('direction', 'string', '排列方向');
    this.addInput('alignment', 'string', '对齐方式');
    this.addInput('spacing', 'number', '间距');
    this.addInput('padding', 'number', '内边距');
    this.addInput('wrap', 'boolean', '自动换行');

    // 输出端口
    this.addOutput('layoutConfig', 'object', '布局配置');
    this.addOutput('affectedElements', 'array', '受影响的元素');
    this.addOutput('onLayoutApplied', 'trigger', '布局应用完成');
    this.addOutput('onError', 'trigger', '布局失败');
  }

  public execute(inputs?: any): any {
    try {
      const applyTrigger = inputs?.applyLayout;
      if (!applyTrigger) {
        return this.getDefaultOutputs();
      }

      const containerId = inputs?.containerId as string;
      if (!containerId) {
        throw new Error('未提供容器ID');
      }

      const layoutType = inputs?.layoutType as string || 'flex';
      const direction = inputs?.direction as string || 'row';
      const alignment = inputs?.alignment as string || 'middleCenter';
      const spacing = inputs?.spacing as number || 8;
      const padding = inputs?.padding as number || 8;
      const wrap = inputs?.wrap as boolean || false;

      // 创建布局配置
      const layoutConfig: UILayoutConfig = {
        type: layoutType as UILayoutType,
        alignment: alignment as UIAlignment,
        spacing,
        padding,
        wrap,
        direction: direction as 'row' | 'column',
        justifyContent: 'start',
        alignItems: 'start'
      };

      // 应用布局
      UILayoutNode.uiManager.applyLayout(containerId, layoutConfig);

      // 获取受影响的元素
      const container = UILayoutNode.uiManager.getElement(containerId);
      const affectedElements = container ? container.children : [];

      Debug.log('UILayoutNode', `布局应用成功: ${containerId} (${layoutType})`);

      return {
        layoutConfig,
        affectedElements,
        onLayoutApplied: true,
        onError: false
      };

    } catch (error) {
      Debug.error('UILayoutNode', 'UI布局应用失败', error);
      return {
        layoutConfig: null,
        affectedElements: [],
        onLayoutApplied: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      layoutConfig: null,
      affectedElements: [],
      onLayoutApplied: false,
      onError: false
    };
  }
}

/**
 * UI事件处理节点
 */
export class UIEventHandlerNode extends VisualScriptNode {
  public static readonly TYPE = 'UIEventHandler';
  public static readonly NAME = 'UI事件处理';
  public static readonly DESCRIPTION = '处理UI元素的用户交互事件';

  private static uiManager: AdvancedUIManager = new AdvancedUIManager();

  constructor(nodeType: string = UIEventHandlerNode.TYPE, name: string = UIEventHandlerNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('addListener', 'trigger', '添加监听器');
    this.addInput('triggerEvent', 'trigger', '触发事件');
    this.addInput('elementId', 'string', '元素ID');
    this.addInput('eventType', 'string', '事件类型');
    this.addInput('eventData', 'any', '事件数据');

    // 输出端口
    this.addOutput('elementId', 'string', '元素ID');
    this.addOutput('eventType', 'string', '事件类型');
    this.addOutput('eventData', 'any', '事件数据');
    this.addOutput('onListenerAdded', 'trigger', '监听器添加完成');
    this.addOutput('onEventTriggered', 'trigger', '事件触发');
    this.addOutput('onClick', 'trigger', '点击事件');
    this.addOutput('onHover', 'trigger', '悬停事件');
    this.addOutput('onChange', 'trigger', '值改变事件');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const addListenerTrigger = inputs?.addListener;
      const triggerEventTrigger = inputs?.triggerEvent;
      const elementId = inputs?.elementId as string;
      const eventType = inputs?.eventType as string;
      const eventData = inputs?.eventData;

      if (!elementId || !eventType) {
        throw new Error('未提供元素ID或事件类型');
      }

      if (addListenerTrigger) {
        return this.addEventListener(elementId, eventType, eventData);
      } else if (triggerEventTrigger) {
        return this.triggerEvent(elementId, eventType, eventData);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('UIEventHandlerNode', 'UI事件处理失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private addEventListener(elementId: string, eventType: string, eventData: any): any {
    // 创建事件处理函数
    const handler = (data: any) => {
      Debug.log('UIEventHandlerNode', `事件触发: ${elementId}.${eventType}`);
      // 这里可以触发相应的输出端口
    };

    UIEventHandlerNode.uiManager.addEventListener(elementId, eventType, handler);

    return {
      elementId,
      eventType,
      eventData,
      onListenerAdded: true,
      onEventTriggered: false,
      onClick: false,
      onHover: false,
      onChange: false,
      onError: false
    };
  }

  private triggerEvent(elementId: string, eventType: string, eventData: any): any {
    UIEventHandlerNode.uiManager.triggerEvent(elementId, eventType, eventData);

    // 根据事件类型设置相应的输出
    const outputs = this.getDefaultOutputs();
    outputs.elementId = elementId;
    outputs.eventType = eventType;
    outputs.eventData = eventData;
    outputs.onEventTriggered = true;

    switch (eventType) {
      case 'click':
        outputs.onClick = true;
        break;
      case 'hover':
        outputs.onHover = true;
        break;
      case 'change':
        outputs.onChange = true;
        break;
    }

    return outputs;
  }

  private getDefaultOutputs(): any {
    return {
      elementId: '',
      eventType: '',
      eventData: null,
      onListenerAdded: false,
      onEventTriggered: false,
      onClick: false,
      onHover: false,
      onChange: false,
      onError: false
    };
  }
}
