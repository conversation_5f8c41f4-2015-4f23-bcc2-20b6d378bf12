/**
 * NFT管理面板 - 显示和管理用户的NFT资产
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Typography,
  Image,
  Tag,
  Modal,
  Input,
  Select,
  Tooltip,
  Empty,
  Spin,
  message
} from 'antd';
import {
  PictureOutlined,
  EyeOutlined,
  ShareAltOutlined,
  DownloadOutlined,
  EditOutlined,
  PlusOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { useNFT } from '../../hooks/useNFT';
// import { NFTMintingModal } from './NFTMintingModal';
// import { NFTDetailModal } from './NFTDetailModal';
// import { NFTTransferModal } from './NFTTransferModal';
import './NFTManagementPanel.less';

const { Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;

export interface NFTManagementPanelProps {
  className?: string;
  style?: React.CSSProperties;
}

export const NFTManagementPanel: React.FC<NFTManagementPanelProps> = ({
  className,
  style
}) => {
  const {
    nfts,
    isLoading,
    loadUserNFTs,
    mintNFT,
    transferNFT,
    displayNFT
  } = useNFT();

  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [showMintingModal, setShowMintingModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [selectedNFT, setSelectedNFT] = useState<any>(null);

  // 加载用户NFT
  useEffect(() => {
    loadUserNFTs();
  }, []);

  // 过滤NFT列表
  const filteredNFTs = nfts.filter(nft => {
    const matchesSearch = nft.metadata.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         nft.metadata.description.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesFilter = filterType === 'all' || 
                         nft.metadata.dl_engine_data.asset_type === filterType;
    
    return matchesSearch && matchesFilter;
  });

  // 获取资产类型标签颜色
  const getAssetTypeColor = (assetType: string) => {
    const colors: Record<string, string> = {
      model: 'blue',
      texture: 'green',
      audio: 'orange',
      scene: 'purple',
      animation: 'cyan',
      material: 'magenta'
    };
    return colors[assetType] || 'default';
  };

  // 格式化创建时间
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  // 处理NFT铸造 - 将在NFT铸造模态框中使用
  const handleMintNFT = async (assetId: string, metadata: any) => {
    try {
      await mintNFT(assetId, metadata);
      setShowMintingModal(false);
      message.success('NFT铸造成功！');
      await loadUserNFTs(); // 重新加载NFT列表
    } catch (error) {
      console.error('铸造NFT失败:', error);
      message.error('NFT铸造失败');
    }
  };
  // 暂时未使用，但保留用于将来的NFT铸造功能
  console.log('handleMintNFT function ready:', handleMintNFT);

  // 处理NFT展示
  const handleDisplayNFT = async (nft: any) => {
    try {
      await displayNFT(nft.tokenId, nft.contractAddress);
      message.success('NFT已在场景中显示');
    } catch (error) {
      console.error('显示NFT失败:', error);
      message.error('显示NFT失败');
    }
  };

  // 处理NFT转移 - 将在NFT转移模态框中使用
  const handleTransferNFT = async (toAddress: string) => {
    if (!selectedNFT) return;

    try {
      await transferNFT(selectedNFT.tokenId, selectedNFT.contractAddress, toAddress);
      setShowTransferModal(false);
      setSelectedNFT(null);
      message.success('NFT转移成功！');
      await loadUserNFTs(); // 重新加载NFT列表
    } catch (error) {
      console.error('转移NFT失败:', error);
      message.error('NFT转移失败');
    }
  };
  // 暂时未使用，但保留用于将来的NFT转移功能
  console.log('handleTransferNFT function ready:', handleTransferNFT);

  // 处理NFT详情查看
  const handleViewDetails = (nft: any) => {
    setSelectedNFT(nft);
    setShowDetailModal(true);
  };

  // 处理NFT转移
  const handleTransfer = (nft: any) => {
    setSelectedNFT(nft);
    setShowTransferModal(true);
  };

  return (
    <div className={`nft-management-panel ${className || ''}`} style={style}>
      <Card
        title={
          <Space>
            <PictureOutlined />
            <span>我的NFT资产</span>
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowMintingModal(true)}
          >
            铸造NFT
          </Button>
        }
        size="small"
      >
        {/* 搜索和过滤 */}
        <div className="nft-filters">
          <Space style={{ width: '100%', marginBottom: 16 }}>
            <Search
              placeholder="搜索NFT名称或描述"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ flex: 1 }}
              allowClear
            />
            <Select
              value={filterType}
              onChange={setFilterType}
              style={{ width: 120 }}
              suffixIcon={<FilterOutlined />}
            >
              <Option value="all">全部类型</Option>
              <Option value="model">3D模型</Option>
              <Option value="texture">纹理</Option>
              <Option value="audio">音频</Option>
              <Option value="scene">场景</Option>
              <Option value="animation">动画</Option>
              <Option value="material">材质</Option>
            </Select>
          </Space>
        </div>

        {/* NFT列表 */}
        <Spin spinning={isLoading}>
          {filteredNFTs.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                nfts.length === 0 ? "您还没有任何NFT资产" : "没有找到匹配的NFT"
              }
            >
              {nfts.length === 0 && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowMintingModal(true)}
                >
                  铸造第一个NFT
                </Button>
              )}
            </Empty>
          ) : (
            <List
              grid={{
                gutter: 16,
                xs: 1,
                sm: 2,
                md: 2,
                lg: 3,
                xl: 3,
                xxl: 4
              }}
              dataSource={filteredNFTs}
              renderItem={(nft) => (
                <List.Item>
                  <Card
                    hoverable
                    className="nft-card"
                    cover={
                      <div className="nft-image-container">
                        <Image
                          src={nft.metadata.image}
                          alt={nft.metadata.name}
                          preview={false}
                          fallback="/images/nft-placeholder.png"
                        />
                        <div className="nft-overlay">
                          <Space>
                            <Tooltip title="查看详情">
                              <Button
                                type="primary"
                                shape="circle"
                                icon={<EyeOutlined />}
                                onClick={() => handleViewDetails(nft)}
                              />
                            </Tooltip>
                            <Tooltip title="在场景中显示">
                              <Button
                                type="default"
                                shape="circle"
                                icon={<PictureOutlined />}
                                onClick={() => handleDisplayNFT(nft)}
                              />
                            </Tooltip>
                          </Space>
                        </div>
                      </div>
                    }
                    actions={[
                      <Tooltip title="转移">
                        <ShareAltOutlined onClick={() => handleTransfer(nft)} />
                      </Tooltip>,
                      <Tooltip title="下载">
                        <DownloadOutlined />
                      </Tooltip>,
                      <Tooltip title="编辑">
                        <EditOutlined />
                      </Tooltip>
                    ]}
                  >
                    <Card.Meta
                      title={
                        <div className="nft-title">
                          <Text ellipsis={{ tooltip: nft.metadata.name }}>
                            {nft.metadata.name}
                          </Text>
                          <Tag color={getAssetTypeColor(nft.metadata.dl_engine_data.asset_type)}>
                            {nft.metadata.dl_engine_data.asset_type}
                          </Tag>
                        </div>
                      }
                      description={
                        <div className="nft-description">
                          <Paragraph
                            ellipsis={{ rows: 2, tooltip: nft.metadata.description }}
                            style={{ marginBottom: 8 }}
                          >
                            {nft.metadata.description}
                          </Paragraph>
                          <div className="nft-info">
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              Token ID: {nft.tokenId}
                            </Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              创建于: {formatDate(nft.mintedAt)}
                            </Text>
                          </div>
                        </div>
                      }
                    />
                  </Card>
                </List.Item>
              )}
            />
          )}
        </Spin>
      </Card>

      {/* NFT铸造模态框 */}
      <Modal
        title="铸造NFT"
        open={showMintingModal}
        onCancel={() => setShowMintingModal(false)}
        footer={null}
      >
        <div>
          <Text>NFT铸造功能开发中...</Text>
        </div>
      </Modal>

      {/* NFT详情模态框 */}
      <Modal
        title="NFT详情"
        open={showDetailModal}
        onCancel={() => {
          setShowDetailModal(false);
          setSelectedNFT(null);
        }}
        footer={null}
      >
        <div>
          <Text>NFT详情功能开发中...</Text>
        </div>
      </Modal>

      {/* NFT转移模态框 */}
      <Modal
        title="转移NFT"
        open={showTransferModal}
        onCancel={() => {
          setShowTransferModal(false);
          setSelectedNFT(null);
        }}
        footer={null}
      >
        <div>
          <Text>NFT转移功能开发中...</Text>
        </div>
      </Modal>
    </div>
  );
};
