/**
 * 场景生成节点集合
 * 提供自动场景生成、程序化内容创建、场景布局等功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Vector2, Color, Box3, Sphere, Quaternion } from 'three';
import { Entity } from '../entity/EntityNodes';

/**
 * 场景类型枚举
 */
export enum SceneType {
  INDOOR = 'indoor',
  OUTDOOR = 'outdoor',
  URBAN = 'urban',
  NATURAL = 'natural',
  FANTASY = 'fantasy',
  SCIFI = 'scifi',
  ABSTRACT = 'abstract',
  ARCHITECTURAL = 'architectural'
}

/**
 * 生成风格枚举
 */
export enum GenerationStyle {
  REALISTIC = 'realistic',
  STYLIZED = 'stylized',
  MINIMALIST = 'minimalist',
  DETAILED = 'detailed',
  PROCEDURAL = 'procedural',
  HANDCRAFTED = 'handcrafted'
}

/**
 * 场景配置接口
 */
export interface SceneConfig {
  type: SceneType;
  style: GenerationStyle;
  size: Vector3;
  density: number;
  complexity: number;
  seed: number;
  theme: string;
  colorPalette: Color[];
  lighting: LightingConfig;
  weather: WeatherConfig;
}

/**
 * 光照配置接口
 */
export interface LightingConfig {
  ambientIntensity: number;
  sunIntensity: number;
  sunAngle: number;
  sunColor: Color;
  skyColor: Color;
  fogDensity: number;
  fogColor: Color;
  shadows: boolean;
}

/**
 * 天气配置接口
 */
export interface WeatherConfig {
  type: 'clear' | 'cloudy' | 'rainy' | 'snowy' | 'foggy' | 'stormy';
  intensity: number;
  windSpeed: number;
  windDirection: Vector3;
  temperature: number;
  humidity: number;
}

/**
 * 生成结果接口
 */
export interface GenerationResult {
  entities: Entity[];
  bounds: Box3;
  statistics: GenerationStatistics;
  metadata: any;
}

/**
 * 生成统计接口
 */
export interface GenerationStatistics {
  totalEntities: number;
  entityTypes: Map<string, number>;
  generationTime: number;
  memoryUsage: number;
  triangleCount: number;
  textureCount: number;
}

/**
 * 程序化场景生成器
 */
class ProceduralSceneGenerator {
  private seed: number = 12345;
  private random: () => number;

  constructor(seed?: number) {
    if (seed !== undefined) {
      this.seed = seed;
    }
    this.random = this.createSeededRandom(this.seed);
  }

  /**
   * 创建种子随机数生成器
   */
  private createSeededRandom(seed: number): () => number {
    let state = seed;
    return () => {
      state = (state * 1664525 + 1013904223) % 4294967296;
      return state / 4294967296;
    };
  }

  /**
   * 生成场景
   */
  generateScene(config: SceneConfig): GenerationResult {
    const startTime = performance.now();
    const entities: Entity[] = [];
    const statistics: GenerationStatistics = {
      totalEntities: 0,
      entityTypes: new Map(),
      generationTime: 0,
      memoryUsage: 0,
      triangleCount: 0,
      textureCount: 0
    };

    try {
      // 根据场景类型生成不同内容
      switch (config.type) {
        case SceneType.INDOOR:
          entities.push(...this.generateIndoorScene(config));
          break;
        case SceneType.OUTDOOR:
          entities.push(...this.generateOutdoorScene(config));
          break;
        case SceneType.URBAN:
          entities.push(...this.generateUrbanScene(config));
          break;
        case SceneType.NATURAL:
          entities.push(...this.generateNaturalScene(config));
          break;
        case SceneType.FANTASY:
          entities.push(...this.generateFantasyScene(config));
          break;
        case SceneType.SCIFI:
          entities.push(...this.generateSciFiScene(config));
          break;
        default:
          entities.push(...this.generateBasicScene(config));
          break;
      }

      // 计算边界
      const bounds = this.calculateBounds(entities);

      // 更新统计信息
      statistics.totalEntities = entities.length;
      statistics.generationTime = performance.now() - startTime;
      this.updateEntityTypeStatistics(entities, statistics);

      Debug.log('ProceduralSceneGenerator', `场景生成完成: ${entities.length}个实体, 耗时${statistics.generationTime.toFixed(2)}ms`);

      return {
        entities,
        bounds,
        statistics,
        metadata: {
          config,
          seed: this.seed,
          timestamp: Date.now()
        }
      };

    } catch (error) {
      Debug.error('ProceduralSceneGenerator', '场景生成失败', error);
      throw error;
    }
  }

  /**
   * 生成室内场景
   */
  private generateIndoorScene(config: SceneConfig): Entity[] {
    const entities: Entity[] = [];
    const roomSize = config.size;

    // 生成房间结构
    entities.push(...this.generateRoom(roomSize));
    
    // 生成家具
    entities.push(...this.generateFurniture(roomSize, config.density));
    
    // 生成装饰品
    entities.push(...this.generateDecorations(roomSize, config.complexity));

    return entities;
  }

  /**
   * 生成户外场景
   */
  private generateOutdoorScene(config: SceneConfig): Entity[] {
    const entities: Entity[] = [];
    const areaSize = config.size;

    // 生成地形
    entities.push(...this.generateTerrain(areaSize));
    
    // 生成植被
    entities.push(...this.generateVegetation(areaSize, config.density));
    
    // 生成岩石和其他自然元素
    entities.push(...this.generateNaturalElements(areaSize, config.complexity));

    return entities;
  }

  /**
   * 生成城市场景
   */
  private generateUrbanScene(config: SceneConfig): Entity[] {
    const entities: Entity[] = [];
    const citySize = config.size;

    // 生成建筑
    entities.push(...this.generateBuildings(citySize, config.density));
    
    // 生成道路
    entities.push(...this.generateRoads(citySize));
    
    // 生成城市设施
    entities.push(...this.generateUrbanFurniture(citySize, config.complexity));

    return entities;
  }

  /**
   * 生成自然场景
   */
  private generateNaturalScene(config: SceneConfig): Entity[] {
    const entities: Entity[] = [];
    const areaSize = config.size;

    // 生成地形
    entities.push(...this.generateNaturalTerrain(areaSize));
    
    // 生成森林
    entities.push(...this.generateForest(areaSize, config.density));
    
    // 生成水体
    entities.push(...this.generateWaterBodies(areaSize));

    return entities;
  }

  /**
   * 生成奇幻场景
   */
  private generateFantasyScene(config: SceneConfig): Entity[] {
    const entities: Entity[] = [];
    const areaSize = config.size;

    // 生成魔法地形
    entities.push(...this.generateMagicalTerrain(areaSize));
    
    // 生成奇幻建筑
    entities.push(...this.generateFantasyStructures(areaSize, config.density));
    
    // 生成魔法元素
    entities.push(...this.generateMagicalElements(areaSize, config.complexity));

    return entities;
  }

  /**
   * 生成科幻场景
   */
  private generateSciFiScene(config: SceneConfig): Entity[] {
    const entities: Entity[] = [];
    const areaSize = config.size;

    // 生成科技地形
    entities.push(...this.generateTechTerrain(areaSize));
    
    // 生成科幻建筑
    entities.push(...this.generateSciFiStructures(areaSize, config.density));
    
    // 生成科技设备
    entities.push(...this.generateTechDevices(areaSize, config.complexity));

    return entities;
  }

  /**
   * 生成基础场景
   */
  private generateBasicScene(config: SceneConfig): Entity[] {
    const entities: Entity[] = [];
    const areaSize = config.size;

    // 生成基础地面
    entities.push(this.createEntity('Ground', new Vector3(0, 0, 0), areaSize));
    
    // 生成一些基础对象
    const objectCount = Math.floor(config.density * 10);
    for (let i = 0; i < objectCount; i++) {
      const position = new Vector3(
        (this.random() - 0.5) * areaSize.x,
        0,
        (this.random() - 0.5) * areaSize.z
      );
      entities.push(this.createEntity('BasicObject', position, new Vector3(1, 1, 1)));
    }

    return entities;
  }

  /**
   * 生成房间结构
   */
  private generateRoom(size: Vector3): Entity[] {
    const entities: Entity[] = [];
    
    // 地板
    entities.push(this.createEntity('Floor', new Vector3(0, 0, 0), new Vector3(size.x, 0.1, size.z)));
    
    // 墙壁
    entities.push(this.createEntity('Wall', new Vector3(-size.x/2, size.y/2, 0), new Vector3(0.1, size.y, size.z)));
    entities.push(this.createEntity('Wall', new Vector3(size.x/2, size.y/2, 0), new Vector3(0.1, size.y, size.z)));
    entities.push(this.createEntity('Wall', new Vector3(0, size.y/2, -size.z/2), new Vector3(size.x, size.y, 0.1)));
    entities.push(this.createEntity('Wall', new Vector3(0, size.y/2, size.z/2), new Vector3(size.x, size.y, 0.1)));
    
    // 天花板
    entities.push(this.createEntity('Ceiling', new Vector3(0, size.y, 0), new Vector3(size.x, 0.1, size.z)));

    return entities;
  }

  /**
   * 生成家具
   */
  private generateFurniture(roomSize: Vector3, density: number): Entity[] {
    const entities: Entity[] = [];
    const furnitureTypes = ['Table', 'Chair', 'Sofa', 'Bed', 'Desk', 'Bookshelf'];
    const furnitureCount = Math.floor(density * 8);

    for (let i = 0; i < furnitureCount; i++) {
      const type = furnitureTypes[Math.floor(this.random() * furnitureTypes.length)];
      const position = new Vector3(
        (this.random() - 0.5) * (roomSize.x - 2),
        0,
        (this.random() - 0.5) * (roomSize.z - 2)
      );
      const size = this.getFurnitureSize(type);
      entities.push(this.createEntity(type, position, size));
    }

    return entities;
  }

  /**
   * 生成装饰品
   */
  private generateDecorations(roomSize: Vector3, complexity: number): Entity[] {
    const entities: Entity[] = [];
    const decorationTypes = ['Lamp', 'Picture', 'Plant', 'Vase', 'Clock', 'Mirror'];
    const decorationCount = Math.floor(complexity * 15);

    for (let i = 0; i < decorationCount; i++) {
      const type = decorationTypes[Math.floor(this.random() * decorationTypes.length)];
      const position = new Vector3(
        (this.random() - 0.5) * roomSize.x,
        this.random() * roomSize.y * 0.8,
        (this.random() - 0.5) * roomSize.z
      );
      const size = this.getDecorationSize(type);
      entities.push(this.createEntity(type, position, size));
    }

    return entities;
  }

  /**
   * 创建实体
   */
  private createEntity(type: string, position: Vector3, size: Vector3): Entity {
    const entity: Entity = {
      id: this.generateEntityId(),
      name: type,
      active: true,
      transform: {
        position: position.clone(),
        rotation: new Quaternion(),
        scale: size.clone()
      },
      components: new Map(),
      children: [],
      tags: new Set([type.toLowerCase()]),
      layer: 0
    };

    return entity;
  }

  /**
   * 获取家具尺寸
   */
  private getFurnitureSize(type: string): Vector3 {
    const sizes: { [key: string]: Vector3 } = {
      'Table': new Vector3(1.5, 0.8, 1.0),
      'Chair': new Vector3(0.5, 1.0, 0.5),
      'Sofa': new Vector3(2.0, 0.8, 1.0),
      'Bed': new Vector3(2.0, 0.5, 1.5),
      'Desk': new Vector3(1.2, 0.8, 0.6),
      'Bookshelf': new Vector3(0.3, 2.0, 1.0)
    };
    return sizes[type] || new Vector3(1, 1, 1);
  }

  /**
   * 获取装饰品尺寸
   */
  private getDecorationSize(type: string): Vector3 {
    const sizes: { [key: string]: Vector3 } = {
      'Lamp': new Vector3(0.3, 0.6, 0.3),
      'Picture': new Vector3(0.05, 0.8, 0.6),
      'Plant': new Vector3(0.4, 0.8, 0.4),
      'Vase': new Vector3(0.2, 0.5, 0.2),
      'Clock': new Vector3(0.05, 0.3, 0.3),
      'Mirror': new Vector3(0.05, 1.0, 0.8)
    };
    return sizes[type] || new Vector3(0.2, 0.2, 0.2);
  }

  /**
   * 计算边界
   */
  private calculateBounds(entities: Entity[]): Box3 {
    const bounds = new Box3();
    
    for (const entity of entities) {
      const entityBounds = new Box3().setFromCenterAndSize(
        entity.transform.position,
        entity.transform.scale
      );
      bounds.union(entityBounds);
    }

    return bounds;
  }

  /**
   * 更新实体类型统计
   */
  private updateEntityTypeStatistics(entities: Entity[], statistics: GenerationStatistics): void {
    for (const entity of entities) {
      const type = entity.name;
      const count = statistics.entityTypes.get(type) || 0;
      statistics.entityTypes.set(type, count + 1);
    }
  }

  /**
   * 生成实体ID
   */
  private generateEntityId(): string {
    return 'generated_' + Math.random().toString(36).substr(2, 9);
  }

  // 其他生成方法的简化实现
  private generateTerrain(size: Vector3): Entity[] { return [this.createEntity('Terrain', new Vector3(0, 0, 0), size)]; }
  private generateVegetation(size: Vector3, density: number): Entity[] { return []; }
  private generateNaturalElements(size: Vector3, complexity: number): Entity[] { return []; }
  private generateBuildings(size: Vector3, density: number): Entity[] { return []; }
  private generateRoads(size: Vector3): Entity[] { return []; }
  private generateUrbanFurniture(size: Vector3, complexity: number): Entity[] { return []; }
  private generateNaturalTerrain(size: Vector3): Entity[] { return []; }
  private generateForest(size: Vector3, density: number): Entity[] { return []; }
  private generateWaterBodies(size: Vector3): Entity[] { return []; }
  private generateMagicalTerrain(size: Vector3): Entity[] { return []; }
  private generateFantasyStructures(size: Vector3, density: number): Entity[] { return []; }
  private generateMagicalElements(size: Vector3, complexity: number): Entity[] { return []; }
  private generateTechTerrain(size: Vector3): Entity[] { return []; }
  private generateSciFiStructures(size: Vector3, density: number): Entity[] { return []; }
  private generateTechDevices(size: Vector3, complexity: number): Entity[] { return []; }
}

/**
 * 自动场景生成节点
 */
export class AutoSceneGenerationNode extends VisualScriptNode {
  public static readonly TYPE = 'AutoSceneGeneration';
  public static readonly NAME = '自动场景生成';
  public static readonly DESCRIPTION = '根据配置自动生成3D场景';

  private generator: ProceduralSceneGenerator;

  constructor(nodeType: string = AutoSceneGenerationNode.TYPE, name: string = AutoSceneGenerationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.generator = new ProceduralSceneGenerator();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('generate', 'trigger', '生成');
    this.addInput('sceneType', 'string', '场景类型');
    this.addInput('style', 'string', '生成风格');
    this.addInput('size', 'object', '场景尺寸');
    this.addInput('density', 'number', '密度');
    this.addInput('complexity', 'number', '复杂度');
    this.addInput('seed', 'number', '随机种子');
    this.addInput('theme', 'string', '主题');
    this.addInput('colorPalette', 'array', '色彩方案');

    // 输出端口
    this.addOutput('entities', 'array', '生成的实体');
    this.addOutput('entityCount', 'number', '实体数量');
    this.addOutput('bounds', 'object', '场景边界');
    this.addOutput('statistics', 'object', '生成统计');
    this.addOutput('generationTime', 'number', '生成时间');
    this.addOutput('onGenerated', 'trigger', '生成完成');
    this.addOutput('onError', 'trigger', '生成失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const generateTrigger = inputs?.generate;
      if (!generateTrigger) {
        return this.getDefaultOutputs();
      }

      const sceneType = inputs?.sceneType as string || 'indoor';
      const style = inputs?.style as string || 'realistic';
      const size = inputs?.size as Vector3 || new Vector3(10, 3, 10);
      const density = inputs?.density as number || 0.5;
      const complexity = inputs?.complexity as number || 0.5;
      const seed = inputs?.seed as number || Math.floor(Math.random() * 1000000);
      const theme = inputs?.theme as string || 'modern';
      const colorPalette = inputs?.colorPalette as Color[] || [new Color(0.8, 0.8, 0.8)];

      // 创建场景配置
      const config: SceneConfig = {
        type: sceneType as SceneType,
        style: style as GenerationStyle,
        size,
        density: Math.max(0, Math.min(1, density)),
        complexity: Math.max(0, Math.min(1, complexity)),
        seed,
        theme,
        colorPalette,
        lighting: this.getDefaultLighting(),
        weather: this.getDefaultWeather()
      };

      // 创建新的生成器实例（使用新种子）
      this.generator = new ProceduralSceneGenerator(seed);

      // 生成场景
      const result = this.generator.generateScene(config);

      Debug.log('AutoSceneGenerationNode', `场景生成成功: ${result.entities.length}个实体`);

      return {
        entities: result.entities,
        entityCount: result.entities.length,
        bounds: result.bounds,
        statistics: result.statistics,
        generationTime: result.statistics.generationTime,
        onGenerated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('AutoSceneGenerationNode', '自动场景生成失败', error);
      return {
        entities: [],
        entityCount: 0,
        bounds: new Box3(),
        statistics: null,
        generationTime: 0,
        onGenerated: false,
        onError: true
      };
    }
  }

  private getDefaultLighting(): LightingConfig {
    return {
      ambientIntensity: 0.3,
      sunIntensity: 1.0,
      sunAngle: 45,
      sunColor: new Color(1, 0.95, 0.8),
      skyColor: new Color(0.5, 0.7, 1),
      fogDensity: 0.01,
      fogColor: new Color(0.8, 0.9, 1),
      shadows: true
    };
  }

  private getDefaultWeather(): WeatherConfig {
    return {
      type: 'clear',
      intensity: 0.5,
      windSpeed: 2.0,
      windDirection: new Vector3(1, 0, 0),
      temperature: 20,
      humidity: 0.6
    };
  }

  private getDefaultOutputs(): any {
    return {
      entities: [],
      entityCount: 0,
      bounds: new Box3(),
      statistics: null,
      generationTime: 0,
      onGenerated: false,
      onError: false
    };
  }
}

/**
 * 场景布局节点
 */
export class SceneLayoutNode extends VisualScriptNode {
  public static readonly TYPE = 'SceneLayout';
  public static readonly NAME = '场景布局';
  public static readonly DESCRIPTION = '自动排列和布局场景中的对象';

  constructor(nodeType: string = SceneLayoutNode.TYPE, name: string = SceneLayoutNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('layout', 'trigger', '布局');
    this.addInput('entities', 'array', '实体列表');
    this.addInput('layoutType', 'string', '布局类型');
    this.addInput('bounds', 'object', '布局边界');
    this.addInput('spacing', 'number', '间距');
    this.addInput('alignment', 'string', '对齐方式');
    this.addInput('avoidOverlap', 'boolean', '避免重叠');

    // 输出端口
    this.addOutput('layoutedEntities', 'array', '布局后的实体');
    this.addOutput('layoutBounds', 'object', '布局边界');
    this.addOutput('efficiency', 'number', '布局效率');
    this.addOutput('onLayouted', 'trigger', '布局完成');
  }

  public execute(inputs?: any): any {
    try {
      const layoutTrigger = inputs?.layout;
      if (!layoutTrigger) {
        return this.getDefaultOutputs();
      }

      const entities = inputs?.entities as Entity[] || [];
      const layoutType = inputs?.layoutType as string || 'grid';
      const bounds = inputs?.bounds as Box3 || new Box3(new Vector3(-10, 0, -10), new Vector3(10, 10, 10));
      const spacing = inputs?.spacing as number || 1.0;
      const alignment = inputs?.alignment as string || 'center';
      const avoidOverlap = inputs?.avoidOverlap as boolean || true;

      // 执行布局
      const layoutedEntities = this.performLayout(entities, layoutType, bounds, spacing, alignment, avoidOverlap);

      // 计算布局边界
      const layoutBounds = this.calculateLayoutBounds(layoutedEntities);

      // 计算布局效率
      const efficiency = this.calculateLayoutEfficiency(layoutedEntities, bounds);

      Debug.log('SceneLayoutNode', `场景布局完成: ${layoutedEntities.length}个实体`);

      return {
        layoutedEntities,
        layoutBounds,
        efficiency,
        onLayouted: true
      };

    } catch (error) {
      Debug.error('SceneLayoutNode', '场景布局失败', error);
      return this.getDefaultOutputs();
    }
  }

  private performLayout(entities: Entity[], layoutType: string, bounds: Box3, spacing: number, alignment: string, avoidOverlap: boolean): Entity[] {
    const layoutedEntities = entities.map(entity => ({ ...entity })); // 深拷贝

    switch (layoutType) {
      case 'grid':
        this.gridLayout(layoutedEntities, bounds, spacing);
        break;
      case 'circle':
        this.circleLayout(layoutedEntities, bounds, spacing);
        break;
      case 'line':
        this.lineLayout(layoutedEntities, bounds, spacing, alignment);
        break;
      case 'random':
        this.randomLayout(layoutedEntities, bounds, avoidOverlap);
        break;
      case 'cluster':
        this.clusterLayout(layoutedEntities, bounds, spacing);
        break;
      default:
        this.gridLayout(layoutedEntities, bounds, spacing);
        break;
    }

    return layoutedEntities;
  }

  private gridLayout(entities: Entity[], bounds: Box3, spacing: number): void {
    const size = bounds.getSize(new Vector3());
    const center = bounds.getCenter(new Vector3());

    const cols = Math.ceil(Math.sqrt(entities.length));
    const rows = Math.ceil(entities.length / cols);

    const cellWidth = size.x / cols;
    const cellDepth = size.z / rows;

    entities.forEach((entity, index) => {
      const col = index % cols;
      const row = Math.floor(index / cols);

      entity.transform.position.set(
        center.x + (col - cols / 2 + 0.5) * cellWidth,
        center.y,
        center.z + (row - rows / 2 + 0.5) * cellDepth
      );
    });
  }

  private circleLayout(entities: Entity[], bounds: Box3, spacing: number): void {
    const center = bounds.getCenter(new Vector3());
    const size = bounds.getSize(new Vector3());
    const radius = Math.min(size.x, size.z) * 0.4;

    entities.forEach((entity, index) => {
      const angle = (index / entities.length) * Math.PI * 2;
      entity.transform.position.set(
        center.x + Math.cos(angle) * radius,
        center.y,
        center.z + Math.sin(angle) * radius
      );
    });
  }

  private lineLayout(entities: Entity[], bounds: Box3, spacing: number, alignment: string): void {
    const center = bounds.getCenter(new Vector3());
    const size = bounds.getSize(new Vector3());

    const totalWidth = (entities.length - 1) * spacing;
    let startX = center.x - totalWidth / 2;

    if (alignment === 'left') {
      startX = bounds.min.x;
    } else if (alignment === 'right') {
      startX = bounds.max.x - totalWidth;
    }

    entities.forEach((entity, index) => {
      entity.transform.position.set(
        startX + index * spacing,
        center.y,
        center.z
      );
    });
  }

  private randomLayout(entities: Entity[], bounds: Box3, avoidOverlap: boolean): void {
    const size = bounds.getSize(new Vector3());
    const min = bounds.min;

    entities.forEach(entity => {
      let position: Vector3;
      let attempts = 0;

      do {
        position = new Vector3(
          min.x + Math.random() * size.x,
          min.y + Math.random() * size.y,
          min.z + Math.random() * size.z
        );
        attempts++;
      } while (avoidOverlap && this.checkOverlap(position, entity, entities) && attempts < 50);

      entity.transform.position.copy(position);
    });
  }

  private clusterLayout(entities: Entity[], bounds: Box3, spacing: number): void {
    // 简化的聚类布局实现
    const clusterCount = Math.min(3, Math.ceil(entities.length / 5));
    const clusters: Entity[][] = Array.from({ length: clusterCount }, () => []);

    // 分配实体到聚类
    entities.forEach((entity, index) => {
      clusters[index % clusterCount].push(entity);
    });

    // 为每个聚类分配位置
    const center = bounds.getCenter(new Vector3());
    const size = bounds.getSize(new Vector3());

    clusters.forEach((cluster, clusterIndex) => {
      const clusterCenter = new Vector3(
        center.x + (Math.random() - 0.5) * size.x * 0.6,
        center.y,
        center.z + (Math.random() - 0.5) * size.z * 0.6
      );

      // 在聚类中心周围布局实体
      cluster.forEach((entity, entityIndex) => {
        const angle = (entityIndex / cluster.length) * Math.PI * 2;
        const radius = spacing * (1 + entityIndex * 0.3);

        entity.transform.position.set(
          clusterCenter.x + Math.cos(angle) * radius,
          clusterCenter.y,
          clusterCenter.z + Math.sin(angle) * radius
        );
      });
    });
  }

  private checkOverlap(position: Vector3, entity: Entity, entities: Entity[]): boolean {
    const entitySize = entity.transform.scale;

    for (const other of entities) {
      if (other === entity) continue;

      const distance = position.distanceTo(other.transform.position);
      const minDistance = (entitySize.length() + other.transform.scale.length()) / 2;

      if (distance < minDistance) {
        return true;
      }
    }

    return false;
  }

  private calculateLayoutBounds(entities: Entity[]): Box3 {
    const bounds = new Box3();

    for (const entity of entities) {
      const entityBounds = new Box3().setFromCenterAndSize(
        entity.transform.position,
        entity.transform.scale
      );
      bounds.union(entityBounds);
    }

    return bounds;
  }

  private calculateLayoutEfficiency(entities: Entity[], bounds: Box3): number {
    if (entities.length === 0) return 0;

    // 简化的效率计算：基于空间利用率
    const layoutBounds = this.calculateLayoutBounds(entities);
    const layoutVolume = layoutBounds.getSize(new Vector3()).x * layoutBounds.getSize(new Vector3()).z;
    const boundsVolume = bounds.getSize(new Vector3()).x * bounds.getSize(new Vector3()).z;

    return Math.min(1, layoutVolume / boundsVolume);
  }

  private getDefaultOutputs(): any {
    return {
      layoutedEntities: [],
      layoutBounds: new Box3(),
      efficiency: 0,
      onLayouted: false
    };
  }
}
