/**
 * 输入节点集合
 * 提供键盘、鼠标、触摸、游戏手柄等输入处理功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector2, Vector3 } from 'three';

/**
 * 键盘按键状态
 */
export enum KeyState {
  UP = 'up',
  DOWN = 'down',
  PRESSED = 'pressed',
  RELEASED = 'released'
}

/**
 * 鼠标按钮枚举
 */
export enum MouseButton {
  LEFT = 0,
  MIDDLE = 1,
  RIGHT = 2
}

/**
 * 触摸点信息
 */
export interface TouchPoint {
  id: number;
  position: Vector2;
  pressure: number;
  radiusX: number;
  radiusY: number;
  rotationAngle: number;
  timestamp: number;
}

/**
 * 游戏手柄状态
 */
export interface GamepadState {
  id: string;
  index: number;
  connected: boolean;
  buttons: boolean[];
  axes: number[];
  timestamp: number;
}

/**
 * 输入管理器
 */
class InputManager {
  private static instance: InputManager;
  private keyStates: Map<string, KeyState> = new Map();
  private mousePosition: Vector2 = new Vector2(0, 0);
  private mouseButtons: Map<MouseButton, boolean> = new Map();
  private touchPoints: Map<number, TouchPoint> = new Map();
  private gamepads: Map<number, GamepadState> = new Map();
  private wheelDelta: Vector2 = new Vector2(0, 0);
  private eventListeners: Map<string, Function[]> = new Map();

  private constructor() {
    this.setupEventListeners();
  }

  public static getInstance(): InputManager {
    if (!InputManager.instance) {
      InputManager.instance = new InputManager();
    }
    return InputManager.instance;
  }

  private setupEventListeners(): void {
    if (typeof window === 'undefined') return;

    // 键盘事件
    window.addEventListener('keydown', (e) => {
      this.keyStates.set(e.code, KeyState.PRESSED);
      this.emit('keydown', { code: e.code, key: e.key });
    });

    window.addEventListener('keyup', (e) => {
      this.keyStates.set(e.code, KeyState.RELEASED);
      this.emit('keyup', { code: e.code, key: e.key });
    });

    // 鼠标事件
    window.addEventListener('mousemove', (e) => {
      this.mousePosition.set(e.clientX, e.clientY);
      this.emit('mousemove', { position: this.mousePosition.clone() });
    });

    window.addEventListener('mousedown', (e) => {
      this.mouseButtons.set(e.button as MouseButton, true);
      this.emit('mousedown', { button: e.button, position: this.mousePosition.clone() });
    });

    window.addEventListener('mouseup', (e) => {
      this.mouseButtons.set(e.button as MouseButton, false);
      this.emit('mouseup', { button: e.button, position: this.mousePosition.clone() });
    });

    window.addEventListener('wheel', (e) => {
      this.wheelDelta.set(e.deltaX, e.deltaY);
      this.emit('wheel', { delta: this.wheelDelta.clone() });
    });

    // 触摸事件
    window.addEventListener('touchstart', (e) => {
      this.handleTouchEvent(e, 'touchstart');
    });

    window.addEventListener('touchmove', (e) => {
      this.handleTouchEvent(e, 'touchmove');
    });

    window.addEventListener('touchend', (e) => {
      this.handleTouchEvent(e, 'touchend');
    });

    // 游戏手柄事件
    window.addEventListener('gamepadconnected', (e) => {
      this.updateGamepadState();
      this.emit('gamepadconnected', { gamepad: e.gamepad });
    });

    window.addEventListener('gamepaddisconnected', (e) => {
      this.gamepads.delete(e.gamepad.index);
      this.emit('gamepaddisconnected', { gamepad: e.gamepad });
    });
  }

  private handleTouchEvent(e: TouchEvent, type: string): void {
    for (let i = 0; i < e.changedTouches.length; i++) {
      const touch = e.changedTouches[i];
      const touchPoint: TouchPoint = {
        id: touch.identifier,
        position: new Vector2(touch.clientX, touch.clientY),
        pressure: touch.force || 1.0,
        radiusX: touch.radiusX || 1,
        radiusY: touch.radiusY || 1,
        rotationAngle: touch.rotationAngle || 0,
        timestamp: Date.now()
      };

      if (type === 'touchstart' || type === 'touchmove') {
        this.touchPoints.set(touch.identifier, touchPoint);
      } else if (type === 'touchend') {
        this.touchPoints.delete(touch.identifier);
      }

      this.emit(type, { touch: touchPoint });
    }
  }

  private updateGamepadState(): void {
    const gamepads = navigator.getGamepads();
    for (let i = 0; i < gamepads.length; i++) {
      const gamepad = gamepads[i];
      if (gamepad) {
        const state: GamepadState = {
          id: gamepad.id,
          index: gamepad.index,
          connected: gamepad.connected,
          buttons: gamepad.buttons.map(button => button.pressed),
          axes: Array.from(gamepad.axes),
          timestamp: gamepad.timestamp
        };
        this.gamepads.set(i, state);
      }
    }
  }

  public getKeyState(keyCode: string): KeyState {
    return this.keyStates.get(keyCode) || KeyState.UP;
  }

  public isKeyPressed(keyCode: string): boolean {
    return this.keyStates.get(keyCode) === KeyState.PRESSED;
  }

  public isKeyDown(keyCode: string): boolean {
    const state = this.keyStates.get(keyCode);
    return state === KeyState.PRESSED || state === KeyState.DOWN;
  }

  public getMousePosition(): Vector2 {
    return this.mousePosition.clone();
  }

  public isMouseButtonDown(button: MouseButton): boolean {
    return this.mouseButtons.get(button) || false;
  }

  public getWheelDelta(): Vector2 {
    const delta = this.wheelDelta.clone();
    this.wheelDelta.set(0, 0); // 重置滚轮增量
    return delta;
  }

  public getTouchPoints(): TouchPoint[] {
    return Array.from(this.touchPoints.values());
  }

  public getGamepadState(index: number): GamepadState | null {
    return this.gamepads.get(index) || null;
  }

  public getAllGamepads(): GamepadState[] {
    this.updateGamepadState();
    return Array.from(this.gamepads.values());
  }

  public on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  public off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('InputManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  public update(): void {
    // 更新按键状态
    for (const [key, state] of this.keyStates) {
      if (state === KeyState.PRESSED) {
        this.keyStates.set(key, KeyState.DOWN);
      } else if (state === KeyState.RELEASED) {
        this.keyStates.set(key, KeyState.UP);
      }
    }

    // 更新游戏手柄状态
    this.updateGamepadState();
  }
}

/**
 * 键盘输入节点
 */
export class KeyboardInputNode extends VisualScriptNode {
  public static readonly TYPE = 'KeyboardInput';
  public static readonly NAME = '键盘输入';
  public static readonly DESCRIPTION = '检测键盘按键输入';

  private inputManager: InputManager;

  constructor(nodeType: string = KeyboardInputNode.TYPE, name: string = KeyboardInputNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.inputManager = InputManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('keyCode', 'string', '按键代码');
    this.addInput('checkPressed', 'trigger', '检查按下');
    this.addInput('checkDown', 'trigger', '检查持续按下');
    this.addInput('checkReleased', 'trigger', '检查释放');

    // 输出端口
    this.addOutput('isPressed', 'boolean', '刚按下');
    this.addOutput('isDown', 'boolean', '持续按下');
    this.addOutput('isReleased', 'boolean', '刚释放');
    this.addOutput('keyState', 'string', '按键状态');
    this.addOutput('onPressed', 'trigger', '按下时');
    this.addOutput('onDown', 'trigger', '持续按下时');
    this.addOutput('onReleased', 'trigger', '释放时');
  }

  public execute(inputs?: any): any {
    try {
      const keyCode = inputs?.keyCode as string;
      if (!keyCode) {
        return this.getDefaultOutputs();
      }

      const keyState = this.inputManager.getKeyState(keyCode);
      const isPressed = keyState === KeyState.PRESSED;
      const isDown = this.inputManager.isKeyDown(keyCode);
      const isReleased = keyState === KeyState.RELEASED;

      return {
        isPressed,
        isDown,
        isReleased,
        keyState,
        onPressed: isPressed,
        onDown: isDown,
        onReleased: isReleased
      };

    } catch (error) {
      Debug.error('KeyboardInputNode', '键盘输入检测失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      isPressed: false,
      isDown: false,
      isReleased: false,
      keyState: KeyState.UP,
      onPressed: false,
      onDown: false,
      onReleased: false
    };
  }
}

/**
 * 鼠标输入节点
 */
export class MouseInputNode extends VisualScriptNode {
  public static readonly TYPE = 'MouseInput';
  public static readonly NAME = '鼠标输入';
  public static readonly DESCRIPTION = '检测鼠标输入和位置';

  private inputManager: InputManager;

  constructor(nodeType: string = MouseInputNode.TYPE, name: string = MouseInputNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.inputManager = InputManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('button', 'number', '鼠标按钮');
    this.addInput('checkButton', 'trigger', '检查按钮');
    this.addInput('getPosition', 'trigger', '获取位置');
    this.addInput('getWheel', 'trigger', '获取滚轮');

    // 输出端口
    this.addOutput('position', 'object', '鼠标位置');
    this.addOutput('x', 'number', 'X坐标');
    this.addOutput('y', 'number', 'Y坐标');
    this.addOutput('isButtonDown', 'boolean', '按钮按下');
    this.addOutput('wheelDelta', 'object', '滚轮增量');
    this.addOutput('wheelX', 'number', '水平滚轮');
    this.addOutput('wheelY', 'number', '垂直滚轮');
    this.addOutput('onMove', 'trigger', '移动时');
    this.addOutput('onButtonDown', 'trigger', '按钮按下时');
    this.addOutput('onButtonUp', 'trigger', '按钮释放时');
    this.addOutput('onWheel', 'trigger', '滚轮滚动时');
  }

  public execute(inputs?: any): any {
    try {
      const button = inputs?.button as number;
      const position = this.inputManager.getMousePosition();
      const wheelDelta = this.inputManager.getWheelDelta();
      
      let isButtonDown = false;
      if (button !== undefined) {
        isButtonDown = this.inputManager.isMouseButtonDown(button as MouseButton);
      }

      return {
        position,
        x: position.x,
        y: position.y,
        isButtonDown,
        wheelDelta,
        wheelX: wheelDelta.x,
        wheelY: wheelDelta.y,
        onMove: false,
        onButtonDown: false,
        onButtonUp: false,
        onWheel: wheelDelta.length() > 0
      };

    } catch (error) {
      Debug.error('MouseInputNode', '鼠标输入检测失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      position: new Vector2(0, 0),
      x: 0,
      y: 0,
      isButtonDown: false,
      wheelDelta: new Vector2(0, 0),
      wheelX: 0,
      wheelY: 0,
      onMove: false,
      onButtonDown: false,
      onButtonUp: false,
      onWheel: false
    };
  }
}

/**
 * 触摸输入节点
 */
export class TouchInputNode extends VisualScriptNode {
  public static readonly TYPE = 'TouchInput';
  public static readonly NAME = '触摸输入';
  public static readonly DESCRIPTION = '检测触摸屏输入';

  private inputManager: InputManager;

  constructor(nodeType: string = TouchInputNode.TYPE, name: string = TouchInputNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.inputManager = InputManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('getTouches', 'trigger', '获取触摸点');
    this.addInput('touchIndex', 'number', '触摸点索引');

    // 输出端口
    this.addOutput('touchCount', 'number', '触摸点数量');
    this.addOutput('touches', 'array', '所有触摸点');
    this.addOutput('firstTouch', 'object', '第一个触摸点');
    this.addOutput('touchPosition', 'object', '触摸位置');
    this.addOutput('touchPressure', 'number', '触摸压力');
    this.addOutput('isTouching', 'boolean', '正在触摸');
    this.addOutput('onTouchStart', 'trigger', '触摸开始');
    this.addOutput('onTouchMove', 'trigger', '触摸移动');
    this.addOutput('onTouchEnd', 'trigger', '触摸结束');
  }

  public execute(inputs?: any): any {
    try {
      const touchPoints = this.inputManager.getTouchPoints();
      const touchIndex = inputs?.touchIndex as number || 0;
      
      const touchCount = touchPoints.length;
      const isTouching = touchCount > 0;
      const firstTouch = touchPoints.length > 0 ? touchPoints[0] : null;
      const selectedTouch = touchPoints[touchIndex] || firstTouch;

      return {
        touchCount,
        touches: touchPoints,
        firstTouch,
        touchPosition: selectedTouch?.position || new Vector2(0, 0),
        touchPressure: selectedTouch?.pressure || 0,
        isTouching,
        onTouchStart: false,
        onTouchMove: false,
        onTouchEnd: false
      };

    } catch (error) {
      Debug.error('TouchInputNode', '触摸输入检测失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      touchCount: 0,
      touches: [],
      firstTouch: null,
      touchPosition: new Vector2(0, 0),
      touchPressure: 0,
      isTouching: false,
      onTouchStart: false,
      onTouchMove: false,
      onTouchEnd: false
    };
  }
}

/**
 * 游戏手柄输入节点
 */
export class GamepadInputNode extends VisualScriptNode {
  public static readonly TYPE = 'GamepadInput';
  public static readonly NAME = '游戏手柄输入';
  public static readonly DESCRIPTION = '检测游戏手柄输入';

  private inputManager: InputManager;

  constructor(nodeType: string = GamepadInputNode.TYPE, name: string = GamepadInputNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.inputManager = InputManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('gamepadIndex', 'number', '手柄索引');
    this.addInput('buttonIndex', 'number', '按钮索引');
    this.addInput('axisIndex', 'number', '轴索引');
    this.addInput('checkButton', 'trigger', '检查按钮');
    this.addInput('getAxis', 'trigger', '获取轴值');

    // 输出端口
    this.addOutput('isConnected', 'boolean', '已连接');
    this.addOutput('gamepadId', 'string', '手柄ID');
    this.addOutput('buttonPressed', 'boolean', '按钮按下');
    this.addOutput('axisValue', 'number', '轴值');
    this.addOutput('leftStick', 'object', '左摇杆');
    this.addOutput('rightStick', 'object', '右摇杆');
    this.addOutput('triggers', 'object', '扳机键');
    this.addOutput('allButtons', 'array', '所有按钮');
    this.addOutput('allAxes', 'array', '所有轴');
    this.addOutput('onButtonPress', 'trigger', '按钮按下时');
    this.addOutput('onConnect', 'trigger', '连接时');
    this.addOutput('onDisconnect', 'trigger', '断开时');
  }

  public execute(inputs?: any): any {
    try {
      const gamepadIndex = inputs?.gamepadIndex as number || 0;
      const buttonIndex = inputs?.buttonIndex as number || 0;
      const axisIndex = inputs?.axisIndex as number || 0;

      const gamepadState = this.inputManager.getGamepadState(gamepadIndex);
      
      if (!gamepadState) {
        return this.getDefaultOutputs();
      }

      const buttonPressed = gamepadState.buttons[buttonIndex] || false;
      const axisValue = gamepadState.axes[axisIndex] || 0;

      // 解析常用控制
      const leftStick = new Vector2(
        gamepadState.axes[0] || 0,
        gamepadState.axes[1] || 0
      );
      const rightStick = new Vector2(
        gamepadState.axes[2] || 0,
        gamepadState.axes[3] || 0
      );
      const triggers = {
        left: gamepadState.axes[6] || 0,
        right: gamepadState.axes[7] || 0
      };

      return {
        isConnected: gamepadState.connected,
        gamepadId: gamepadState.id,
        buttonPressed,
        axisValue,
        leftStick,
        rightStick,
        triggers,
        allButtons: gamepadState.buttons,
        allAxes: gamepadState.axes,
        onButtonPress: buttonPressed,
        onConnect: false,
        onDisconnect: false
      };

    } catch (error) {
      Debug.error('GamepadInputNode', '游戏手柄输入检测失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      isConnected: false,
      gamepadId: '',
      buttonPressed: false,
      axisValue: 0,
      leftStick: new Vector2(0, 0),
      rightStick: new Vector2(0, 0),
      triggers: { left: 0, right: 0 },
      allButtons: [],
      allAxes: [],
      onButtonPress: false,
      onConnect: false,
      onDisconnect: false
    };
  }
}
