/**
 * AI代码生成服务
 * 提供智能代码生成功能，包括组件代码、样式代码、交互逻辑等
 */
import { EventEmitter } from '../utils/EventEmitter';

// 代码类型枚举
export enum CodeType {
  COMPONENT = 'component',
  STYLE = 'style',
  LOGIC = 'logic',
  TEST = 'test',
  CONFIG = 'config',
  HOOK = 'hook',
  UTILITY = 'utility',
  API = 'api'
}

// 框架类型枚举
export enum FrameworkType {
  REACT = 'react',
  VUE = 'vue',
  ANGULAR = 'angular',
  VANILLA = 'vanilla',
  SVELTE = 'svelte'
}

// 样式类型枚举
export enum StyleType {
  CSS = 'css',
  SCSS = 'scss',
  LESS = 'less',
  STYLED_COMPONENTS = 'styled-components',
  CSS_MODULES = 'css-modules',
  TAILWIND = 'tailwind'
}

// 代码生成请求接口
export interface CodeGenerationRequest {
  id: string;
  type: CodeType;
  framework: FrameworkType;
  styleType?: StyleType;
  description: string;
  requirements: CodeRequirements;
  context?: CodeContext;
  options?: CodeGenerationOptions;
}

// 代码需求接口
export interface CodeRequirements {
  functionality: string[];
  props?: PropertyDefinition[];
  state?: StateDefinition[];
  events?: EventDefinition[];
  styling?: StylingRequirements;
  accessibility?: boolean;
  responsive?: boolean;
  testing?: boolean;
}

// 属性定义接口
export interface PropertyDefinition {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: any;
  description?: string;
}

// 状态定义接口
export interface StateDefinition {
  name: string;
  type: string;
  initialValue: any;
  description?: string;
}

// 事件定义接口
export interface EventDefinition {
  name: string;
  parameters: string[];
  description?: string;
}

// 样式需求接口
export interface StylingRequirements {
  theme?: string;
  colors?: string[];
  layout?: string;
  animations?: boolean;
  responsive?: boolean;
}

// 代码上下文接口
export interface CodeContext {
  existingComponents?: string[];
  dependencies?: string[];
  designSystem?: string;
  codeStyle?: CodeStyleGuide;
}

// 代码风格指南接口
export interface CodeStyleGuide {
  indentation: 'spaces' | 'tabs';
  indentSize: number;
  quotes: 'single' | 'double';
  semicolons: boolean;
  trailingCommas: boolean;
  naming: 'camelCase' | 'PascalCase' | 'snake_case' | 'kebab-case';
}

// 代码生成选项接口
export interface CodeGenerationOptions {
  includeComments: boolean;
  includeTypes: boolean;
  includeTests: boolean;
  includeStorybook: boolean;
  optimizeForPerformance: boolean;
  followBestPractices: boolean;
}

// 生成的代码接口
export interface GeneratedCode {
  id: string;
  request: CodeGenerationRequest;
  files: CodeFile[];
  dependencies: string[];
  instructions: string[];
  quality: CodeQuality;
  timestamp: number;
}

// 代码文件接口
export interface CodeFile {
  name: string;
  path: string;
  content: string;
  type: string;
  size: number;
  language: string;
}

// 代码质量接口
export interface CodeQuality {
  overall: number; // 0-100
  readability: number;
  maintainability: number;
  performance: number;
  accessibility: number;
  testCoverage: number;
  issues: CodeIssue[];
  suggestions: string[];
}

// 代码问题接口
export interface CodeIssue {
  type: 'error' | 'warning' | 'info';
  message: string;
  line?: number;
  column?: number;
  file?: string;
  severity: number;
}

// 代码模板接口
export interface CodeTemplate {
  id: string;
  name: string;
  description: string;
  type: CodeType;
  framework: FrameworkType;
  template: string;
  variables: TemplateVariable[];
  examples: string[];
}

// 模板变量接口
export interface TemplateVariable {
  name: string;
  type: string;
  description: string;
  defaultValue?: any;
  required: boolean;
}

/**
 * AI代码生成服务类
 */
export class AICodeGeneratorService extends EventEmitter {
  private static instance: AICodeGeneratorService;
  private generatedCodes: GeneratedCode[] = [];
  private templates: CodeTemplate[] = [];
  private isGenerating: boolean = false;
  private _defaultStyleGuide: CodeStyleGuide;

  private constructor() {
    super();
    this._defaultStyleGuide = this.getDefaultStyleGuide();
    this.initializeTemplates();
  }

  public static getInstance(): AICodeGeneratorService {
    if (!AICodeGeneratorService.instance) {
      AICodeGeneratorService.instance = new AICodeGeneratorService();
    }
    return AICodeGeneratorService.instance;
  }

  /**
   * 获取默认代码风格
   */
  private getDefaultStyleGuide(): CodeStyleGuide {
    return {
      indentation: 'spaces',
      indentSize: 2,
      quotes: 'single',
      semicolons: true,
      trailingCommas: true,
      naming: 'camelCase'
    };
  }

  /**
   * 获取当前默认样式指南
   */
  public getDefaultStyleGuideConfig(): CodeStyleGuide {
    return { ...this._defaultStyleGuide };
  }

  /**
   * 初始化代码模板
   */
  private initializeTemplates(): void {
    this.templates = [
      {
        id: 'react_component',
        name: 'React Component',
        description: 'Basic React functional component template',
        type: CodeType.COMPONENT,
        framework: FrameworkType.REACT,
        template: `import React from 'react';
import './{{componentName}}.css';

interface {{componentName}}Props {
  {{#each props}}
  {{name}}{{#unless required}}?{{/unless}}: {{type}};
  {{/each}}
}

const {{componentName}}: React.FC<{{componentName}}Props> = ({
  {{#each props}}{{name}}{{#if defaultValue}} = {{defaultValue}}{{/if}}{{#unless @last}},{{/unless}}{{/each}}
}) => {
  return (
    <div className="{{kebabCase componentName}}">
      {/* Component content */}
    </div>
  );
};

export default {{componentName}};`,
        variables: [
          {
            name: 'componentName',
            type: 'string',
            description: 'Name of the component',
            required: true
          }
        ],
        examples: ['Button', 'Card', 'Modal']
      }
    ];
  }

  /**
   * 生成代码
   */
  public async generateCode(request: CodeGenerationRequest): Promise<GeneratedCode> {
    if (this.isGenerating) {
      throw new Error('Code generation already in progress');
    }

    this.isGenerating = true;
    this.emit('codeGenerationStarted', request);

    try {
      const generatedCode = await this.performCodeGeneration(request);

      this.generatedCodes.push(generatedCode);

      // 保持历史记录在合理范围内
      if (this.generatedCodes.length > 100) {
        this.generatedCodes.shift();
      }

      this.emit('codeGenerationCompleted', generatedCode);
      return generatedCode;
    } catch (error) {
      this.emit('codeGenerationError', error);
      throw error;
    } finally {
      this.isGenerating = false;
    }
  }

  /**
   * 执行代码生成
   */
  private async performCodeGeneration(request: CodeGenerationRequest): Promise<GeneratedCode> {
    // 模拟AI代码生成延迟
    await new Promise(resolve => setTimeout(resolve, 3000));

    const files: CodeFile[] = [];
    const dependencies: string[] = [];
    const instructions: string[] = [];

    // 根据类型生成不同的代码文件
    switch (request.type) {
      case CodeType.COMPONENT:
        files.push(...this.generateComponentFiles(request));
        dependencies.push(...this.getComponentDependencies(request));
        instructions.push(...this.getComponentInstructions(request));
        break;

      case CodeType.STYLE:
        files.push(...this.generateStyleFiles(request));
        break;

      case CodeType.LOGIC:
        files.push(...this.generateLogicFiles(request));
        break;

      case CodeType.TEST:
        files.push(...this.generateTestFiles(request));
        dependencies.push(...this.getTestDependencies(request));
        break;

      default:
        throw new Error(`Unsupported code type: ${request.type}`);
    }

    // 分析代码质量
    const quality = this.analyzeCodeQuality(files, request);

    const generatedCode: GeneratedCode = {
      id: request.id,
      request,
      files,
      dependencies,
      instructions,
      quality,
      timestamp: Date.now()
    };

    return generatedCode;
  }

  /**
   * 生成组件文件
   */
  private generateComponentFiles(request: CodeGenerationRequest): CodeFile[] {
    const files: CodeFile[] = [];
    const componentName = this.extractComponentName(request.description);

    // 生成主组件文件
    const componentContent = this.generateComponentContent(request, componentName);
    files.push({
      name: `${componentName}.tsx`,
      path: `components/${componentName}/${componentName}.tsx`,
      content: componentContent,
      type: 'component',
      size: componentContent.length,
      language: 'typescript'
    });

    // 生成样式文件
    if (request.styleType) {
      const styleContent = this.generateStyleContent(request, componentName);
      const styleExtension = this.getStyleExtension(request.styleType);
      files.push({
        name: `${componentName}.${styleExtension}`,
        path: `components/${componentName}/${componentName}.${styleExtension}`,
        content: styleContent,
        type: 'style',
        size: styleContent.length,
        language: styleExtension
      });
    }

    // 生成类型定义文件
    if (request.options?.includeTypes) {
      const typesContent = this.generateTypesContent(request, componentName);
      files.push({
        name: `${componentName}.types.ts`,
        path: `components/${componentName}/${componentName}.types.ts`,
        content: typesContent,
        type: 'types',
        size: typesContent.length,
        language: 'typescript'
      });
    }

    // 生成测试文件
    if (request.options?.includeTests) {
      const testContent = this.generateTestContent(request, componentName);
      files.push({
        name: `${componentName}.test.tsx`,
        path: `components/${componentName}/${componentName}.test.tsx`,
        content: testContent,
        type: 'test',
        size: testContent.length,
        language: 'typescript'
      });
    }

    // 生成Storybook文件
    if (request.options?.includeStorybook) {
      const storyContent = this.generateStoryContent(request, componentName);
      files.push({
        name: `${componentName}.stories.tsx`,
        path: `components/${componentName}/${componentName}.stories.tsx`,
        content: storyContent,
        type: 'story',
        size: storyContent.length,
        language: 'typescript'
      });
    }

    return files;
  }

  /**
   * 生成组件内容
   */
  private generateComponentContent(request: CodeGenerationRequest, componentName: string): string {
    const { framework, requirements, options } = request;

    switch (framework) {
      case FrameworkType.REACT:
        return this.generateReactComponent(componentName, requirements, options);
      case FrameworkType.VUE:
        return this.generateVueComponent(componentName, requirements, options);
      case FrameworkType.ANGULAR:
        return this.generateAngularComponent(componentName, requirements, options);
      default:
        return this.generateVanillaComponent(componentName, requirements, options);
    }
  }

  /**
   * 生成React组件
   */
  private generateReactComponent(
    componentName: string,
    requirements: CodeRequirements,
    _options?: CodeGenerationOptions
  ): string {
    const imports = ['import React'];
    const propsInterface = this.generatePropsInterface(componentName, requirements.props);
    const stateHooks = this.generateStateHooks(requirements.state);
    const eventHandlers = this.generateEventHandlers(requirements.events);
    const componentBody = this.generateComponentBody(requirements.functionality);

    if (requirements.state && requirements.state.length > 0) {
      imports.push('{ useState, useEffect }');
    }

    let content = `${imports.join(', ')} from 'react';\n`;

    if (requirements.styling) {
      content += `import './${componentName}.css';\n`;
    }

    content += '\n';

    if (_options?.includeComments) {
      content += `/**\n * ${componentName} Component\n * ${requirements.functionality.join(', ')}\n */\n`;
    }

    content += propsInterface + '\n';
    content += `const ${componentName}: React.FC<${componentName}Props> = ({\n`;

    if (requirements.props) {
      content += requirements.props.map(prop =>
        `  ${prop.name}${prop.defaultValue ? ` = ${JSON.stringify(prop.defaultValue)}` : ''}`
      ).join(',\n');
    }

    content += '\n}) => {\n';
    content += stateHooks;
    content += eventHandlers;
    content += '\n  return (\n';
    content += componentBody;
    content += '  );\n';
    content += '};\n\n';
    content += `export default ${componentName};`;

    return content;
  }

  /**
   * 生成Vue组件
   */
  private generateVueComponent(
    componentName: string,
    requirements: CodeRequirements,
    _options?: CodeGenerationOptions
  ): string {
    let content = '<template>\n';
    content += '  <div class="' + this.kebabCase(componentName) + '">\n';
    content += '    <!-- Component content -->\n';
    content += '  </div>\n';
    content += '</template>\n\n';

    content += '<script setup lang="ts">\n';

    if (requirements.props) {
      content += 'interface Props {\n';
      requirements.props.forEach(prop => {
        content += `  ${prop.name}${prop.required ? '' : '?'}: ${prop.type};\n`;
      });
      content += '}\n\n';
      content += 'const props = defineProps<Props>();\n';
    }

    if (requirements.state) {
      requirements.state.forEach(state => {
        content += `const ${state.name} = ref(${JSON.stringify(state.initialValue)});\n`;
      });
    }

    content += '</script>\n\n';

    if (requirements.styling) {
      content += '<style scoped>\n';
      content += `.${this.kebabCase(componentName)} {\n`;
      content += '  /* Component styles */\n';
      content += '}\n';
      content += '</style>';
    }

    return content;
  }

  /**
   * 生成Angular组件
   */
  private generateAngularComponent(
    componentName: string,
    requirements: CodeRequirements,
    _options?: CodeGenerationOptions
  ): string {
    let content = `import { Component, Input, Output, EventEmitter } from '@angular/core';\n\n`;

    content += '@Component({\n';
    content += `  selector: 'app-${this.kebabCase(componentName)}',\n`;
    content += `  templateUrl: './${this.kebabCase(componentName)}.component.html',\n`;
    content += `  styleUrls: ['./${this.kebabCase(componentName)}.component.css']\n`;
    content += '})\n';
    content += `export class ${componentName}Component {\n`;

    if (requirements.props) {
      requirements.props.forEach(prop => {
        content += `  @Input() ${prop.name}${prop.required ? '' : '?'}: ${prop.type};\n`;
      });
    }

    if (requirements.events) {
      requirements.events.forEach(event => {
        content += `  @Output() ${event.name} = new EventEmitter<any>();\n`;
      });
    }

    content += '}';

    return content;
  }

  /**
   * 生成原生JavaScript组件
   */
  private generateVanillaComponent(
    componentName: string,
    requirements: CodeRequirements,
    _options?: CodeGenerationOptions
  ): string {
    let content = `class ${componentName} {\n`;
    content += '  constructor(element, options = {}) {\n';
    content += '    this.element = element;\n';
    content += '    this.options = { ...this.defaultOptions, ...options };\n';
    content += '    this.init();\n';
    content += '  }\n\n';

    content += '  defaultOptions = {\n';
    if (requirements.props) {
      requirements.props.forEach(prop => {
        content += `    ${prop.name}: ${JSON.stringify(prop.defaultValue || null)},\n`;
      });
    }
    content += '  };\n\n';

    content += '  init() {\n';
    content += '    this.render();\n';
    content += '    this.bindEvents();\n';
    content += '  }\n\n';

    content += '  render() {\n';
    content += '    // Render component\n';
    content += '  }\n\n';

    content += '  bindEvents() {\n';
    content += '    // Bind event listeners\n';
    content += '  }\n';
    content += '}';

    return content;
  }

  /**
   * 生成Props接口
   */
  private generatePropsInterface(componentName: string, props?: PropertyDefinition[]): string {
    if (!props || props.length === 0) {
      return `interface ${componentName}Props {}`;
    }

    let content = `interface ${componentName}Props {\n`;
    props.forEach(prop => {
      if (prop.description) {
        content += `  /** ${prop.description} */\n`;
      }
      content += `  ${prop.name}${prop.required ? '' : '?'}: ${prop.type};\n`;
    });
    content += '}';

    return content;
  }

  /**
   * 生成状态Hooks
   */
  private generateStateHooks(state?: StateDefinition[]): string {
    if (!state || state.length === 0) return '';

    let content = '\n';
    state.forEach(s => {
      content += `  const [${s.name}, set${this.capitalize(s.name)}] = useState<${s.type}>(${JSON.stringify(s.initialValue)});\n`;
    });

    return content;
  }

  /**
   * 生成事件处理器
   */
  private generateEventHandlers(events?: EventDefinition[]): string {
    if (!events || events.length === 0) return '';

    let content = '\n';
    events.forEach(event => {
      const params = event.parameters.join(', ');
      content += `  const handle${this.capitalize(event.name)} = (${params}) => {\n`;
      content += `    // Handle ${event.name}\n`;
      content += '  };\n';
    });

    return content;
  }

  /**
   * 生成组件主体
   */
  private generateComponentBody(functionality: string[]): string {
    let content = '    <div className="component-container">\n';

    functionality.forEach(func => {
      content += `      {/* ${func} */}\n`;
    });

    content += '    </div>\n';
    return content;
  }

  /**
   * 生成样式文件
   */
  private generateStyleFiles(request: CodeGenerationRequest): CodeFile[] {
    const files: CodeFile[] = [];
    const componentName = this.extractComponentName(request.description);
    const styleContent = this.generateStyleContent(request, componentName);
    const extension = this.getStyleExtension(request.styleType || StyleType.CSS);

    files.push({
      name: `${componentName}.${extension}`,
      path: `styles/${componentName}.${extension}`,
      content: styleContent,
      type: 'style',
      size: styleContent.length,
      language: extension
    });

    return files;
  }

  /**
   * 生成样式内容
   */
  private generateStyleContent(request: CodeGenerationRequest, componentName: string): string {
    const { styleType, requirements } = request;
    const className = this.kebabCase(componentName);

    switch (styleType) {
      case StyleType.SCSS:
        return this.generateScssContent(className, requirements.styling);
      case StyleType.LESS:
        return this.generateLessContent(className, requirements.styling);
      case StyleType.STYLED_COMPONENTS:
        return this.generateStyledComponentsContent(componentName, requirements.styling);
      case StyleType.TAILWIND:
        return this.generateTailwindContent(className, requirements.styling);
      default:
        return this.generateCssContent(className, requirements.styling);
    }
  }

  /**
   * 生成CSS内容
   */
  private generateCssContent(className: string, styling?: StylingRequirements): string {
    let content = `.${className} {\n`;
    content += '  display: block;\n';

    if (styling?.colors && styling.colors.length > 0) {
      content += `  color: ${styling.colors[0]};\n`;
    }

    if (styling?.layout) {
      content += `  /* Layout: ${styling.layout} */\n`;
    }

    content += '}\n';

    if (styling?.responsive) {
      content += '\n@media (max-width: 768px) {\n';
      content += `  .${className} {\n`;
      content += '    /* Mobile styles */\n';
      content += '  }\n';
      content += '}\n';
    }

    return content;
  }

  /**
   * 生成逻辑文件
   */
  private generateLogicFiles(request: CodeGenerationRequest): CodeFile[] {
    const files: CodeFile[] = [];
    const logicContent = this.generateLogicContent(request);

    files.push({
      name: 'logic.ts',
      path: 'utils/logic.ts',
      content: logicContent,
      type: 'logic',
      size: logicContent.length,
      language: 'typescript'
    });

    return files;
  }

  /**
   * 生成逻辑内容
   */
  private generateLogicContent(request: CodeGenerationRequest): string {
    let content = '// Generated logic\n\n';

    request.requirements.functionality.forEach(func => {
      const functionName = this.camelCase(func);
      content += `export const ${functionName} = () => {\n`;
      content += `  // Implement ${func}\n`;
      content += '};\n\n';
    });

    return content;
  }

  /**
   * 生成测试文件
   */
  private generateTestFiles(request: CodeGenerationRequest): CodeFile[] {
    const files: CodeFile[] = [];
    const componentName = this.extractComponentName(request.description);
    const testContent = this.generateTestContent(request, componentName);

    files.push({
      name: `${componentName}.test.tsx`,
      path: `__tests__/${componentName}.test.tsx`,
      content: testContent,
      type: 'test',
      size: testContent.length,
      language: 'typescript'
    });

    return files;
  }

  /**
   * 生成测试内容
   */
  private generateTestContent(request: CodeGenerationRequest, componentName: string): string {
    let content = `import { render, screen } from '@testing-library/react';\n`;
    content += `import ${componentName} from '../${componentName}';\n\n`;

    content += `describe('${componentName}', () => {\n`;
    content += '  it(\'renders without crashing\', () => {\n';
    content += `    render(<${componentName} />);\n`;
    content += '  });\n';

    if (request.requirements.functionality) {
      request.requirements.functionality.forEach(func => {
        content += `\n  it('${func}', () => {\n`;
        content += '    // Test implementation\n';
        content += '  });\n';
      });
    }

    content += '});';

    return content;
  }

  /**
   * 生成Storybook内容
   */
  private generateStoryContent(_request: CodeGenerationRequest, componentName: string): string {
    let content = `import type { Meta, StoryObj } from '@storybook/react';\n`;
    content += `import ${componentName} from './${componentName}';\n\n`;

    content += `const meta: Meta<typeof ${componentName}> = {\n`;
    content += `  title: 'Components/${componentName}',\n`;
    content += `  component: ${componentName},\n`;
    content += '  parameters: {\n';
    content += '    layout: \'centered\',\n';
    content += '  },\n';
    content += '};\n\n';

    content += 'export default meta;\n';
    content += `type Story = StoryObj<typeof meta>;\n\n`;

    content += 'export const Default: Story = {\n';
    content += '  args: {},\n';
    content += '};';

    return content;
  }

  /**
   * 分析代码质量
   */
  private analyzeCodeQuality(files: CodeFile[], request: CodeGenerationRequest): CodeQuality {
    const issues: CodeIssue[] = [];
    const suggestions: string[] = [];

    // 基础质量分析
    let readability = 85;
    let maintainability = 80;
    let performance = 75;
    let accessibility = request.requirements.accessibility ? 90 : 60;
    let testCoverage = request.options?.includeTests ? 80 : 0;

    // 检查代码问题
    files.forEach(file => {
      if (file.content.length > 10000) {
        issues.push({
          type: 'warning',
          message: 'File is quite large, consider splitting into smaller modules',
          file: file.name,
          severity: 3
        });
      }

      if (!file.content.includes('//') && !file.content.includes('/*')) {
        issues.push({
          type: 'info',
          message: 'Consider adding comments for better documentation',
          file: file.name,
          severity: 1
        });
        readability -= 5;
      }
    });

    // 生成建议
    if (!request.options?.includeTests) {
      suggestions.push('Add unit tests to improve code reliability');
    }

    if (!request.requirements.accessibility) {
      suggestions.push('Consider adding accessibility features');
    }

    if (request.framework === FrameworkType.REACT && !request.options?.includeTypes) {
      suggestions.push('Add TypeScript types for better type safety');
    }

    const overall = Math.round((readability + maintainability + performance + accessibility + testCoverage) / 5);

    return {
      overall,
      readability,
      maintainability,
      performance,
      accessibility,
      testCoverage,
      issues,
      suggestions
    };
  }

  /**
   * 获取组件依赖
   */
  private getComponentDependencies(request: CodeGenerationRequest): string[] {
    const dependencies: string[] = [];

    switch (request.framework) {
      case FrameworkType.REACT:
        dependencies.push('react');
        if (request.options?.includeTypes) {
          dependencies.push('@types/react');
        }
        break;
      case FrameworkType.VUE:
        dependencies.push('vue');
        break;
      case FrameworkType.ANGULAR:
        dependencies.push('@angular/core');
        break;
    }

    if (request.styleType === StyleType.STYLED_COMPONENTS) {
      dependencies.push('styled-components');
    }

    return dependencies;
  }

  /**
   * 获取测试依赖
   */
  private getTestDependencies(request: CodeGenerationRequest): string[] {
    const dependencies: string[] = [];

    switch (request.framework) {
      case FrameworkType.REACT:
        dependencies.push('@testing-library/react', '@testing-library/jest-dom');
        break;
      case FrameworkType.VUE:
        dependencies.push('@vue/test-utils');
        break;
      case FrameworkType.ANGULAR:
        dependencies.push('@angular/testing');
        break;
    }

    return dependencies;
  }

  /**
   * 获取组件说明
   */
  private getComponentInstructions(request: CodeGenerationRequest): string[] {
    const instructions: string[] = [];

    instructions.push('1. Install the required dependencies');
    instructions.push('2. Import the component in your application');
    instructions.push('3. Use the component with the provided props');

    if (request.options?.includeTests) {
      instructions.push('4. Run tests with: npm test');
    }

    if (request.options?.includeStorybook) {
      instructions.push('5. View component in Storybook');
    }

    return instructions;
  }

  // 辅助方法
  private extractComponentName(description: string): string {
    // 从描述中提取组件名称
    const words = description.split(' ');
    return words.find(word => /^[A-Z]/.test(word)) || 'Component';
  }

  private getStyleExtension(styleType: StyleType): string {
    switch (styleType) {
      case StyleType.SCSS: return 'scss';
      case StyleType.LESS: return 'less';
      case StyleType.STYLED_COMPONENTS: return 'ts';
      case StyleType.CSS_MODULES: return 'module.css';
      case StyleType.TAILWIND: return 'css';
      default: return 'css';
    }
  }

  private generateScssContent(className: string, _styling?: StylingRequirements): string {
    return `.${className} {\n  // SCSS styles\n}`;
  }

  private generateLessContent(className: string, _styling?: StylingRequirements): string {
    return `.${className} {\n  // LESS styles\n}`;
  }

  private generateStyledComponentsContent(componentName: string, _styling?: StylingRequirements): string {
    return `import styled from 'styled-components';\n\nexport const Styled${componentName} = styled.div\`\n  // Styled component styles\n\`;`;
  }

  private generateTailwindContent(className: string, _styling?: StylingRequirements): string {
    return `/* Tailwind classes for ${className} */\n.${className} {\n  @apply block;\n}`;
  }

  private generateTypesContent(request: CodeGenerationRequest, componentName: string): string {
    let content = `// Type definitions for ${componentName}\n\n`;

    if (request.requirements.props) {
      content += `export interface ${componentName}Props {\n`;
      request.requirements.props.forEach(prop => {
        content += `  ${prop.name}${prop.required ? '' : '?'}: ${prop.type};\n`;
      });
      content += '}\n\n';
    }

    return content;
  }

  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  private camelCase(str: string): string {
    return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    }).replace(/\s+/g, '');
  }

  private kebabCase(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
  }

  /**
   * 获取生成的代码
   */
  public getGeneratedCodes(): GeneratedCode[] {
    return [...this.generatedCodes];
  }

  /**
   * 获取代码模板
   */
  public getTemplates(): CodeTemplate[] {
    return [...this.templates];
  }

  /**
   * 添加自定义模板
   */
  public addTemplate(template: CodeTemplate): void {
    this.templates.push(template);
    this.emit('templateAdded', template);
  }

  /**
   * 清除生成历史
   */
  public clearGeneratedCodes(): void {
    this.generatedCodes = [];
    this.emit('codesCleared');
  }

  /**
   * 检查是否正在生成
   */
  public isGenerationInProgress(): boolean {
    return this.isGenerating;
  }

  /**
   * 导出代码
   */
  public exportCode(codeId: string, format: 'zip' | 'files'): string | CodeFile[] {
    const generatedCode = this.generatedCodes.find(code => code.id === codeId);
    if (!generatedCode) {
      throw new Error('Generated code not found');
    }

    if (format === 'files') {
      return generatedCode.files;
    }

    // 模拟ZIP导出
    return 'data:application/zip;base64,UEsDBAoAAAAAAA=='; // 空ZIP的base64
  }

  /**
   * 预览代码
   */
  public previewCode(codeId: string): string {
    const generatedCode = this.generatedCodes.find(code => code.id === codeId);
    if (!generatedCode) {
      throw new Error('Generated code not found');
    }

    // 返回主要文件的内容作为预览
    const mainFile = generatedCode.files.find(file => file.type === 'component') || generatedCode.files[0];
    return mainFile.content;
  }
}

export default AICodeGeneratorService;